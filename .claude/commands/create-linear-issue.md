# /create-linear-issue {{title}}

## Issue Structure Template

### Title
Clear, actionable title describing what needs to be done

### Description
**Problem/Need:**
Brief explanation of what needs to be implemented or fixed

**Acceptance Criteria:**
- [ ] Specific, testable requirement 1
- [ ] Specific, testable requirement 2
- [ ] Specific, testable requirement 3

**Technical Context:**
- Affected files/components
- Related Linear issues (if any)
- Any constraints or considerations

**Definition of Done:**
- [ ] Implementation complete
- [ ] Tests added/updated
- [ ] Documentation updated (if needed)
- [ ] PR created and ready for review

### Labels
Add appropriate labels (bug, feature, enhancement, etc.)

### Project Assignment
Assign to relevant project/milestone

### Team Assignment
Assign to appropriate team

## Create in Linear
Use Linear MCP to create the issue with above structure