# Contract & CompanyConnection Disconnection Edge Cases

**Issue Title**: Fix Contract disconnection and handle reconnection edge cases when user leaves company

**Related to**: TYM-52 (User leaving company functionality)
**Priority**: High
**Type**: Bug Fix + Enhancement

## Current Problems

### 1. Contract Status Not Updated
When a user leaves a company, their contract remains `active` and `connected` even though:
- CompanyUserRole is correctly set to `active: false` ✓
- User no longer appears in company index ✓
- User loses access to the company ✓

**Root Cause**: Multi-tenant scoping bug in `User#leave_company` method
```ruby
# Current buggy code (line 106-109)
contracts = Contract.where(user_id: self.id, company_id: company.id)
# This is scoped by acts_as_tenant to current company, not the leaving company
```

### 2. Contract Connection Status Still Shows "Connected"
The ContractShow component shows:
- **Status**: `active` (from `contract.status` field)
- **Connection**: `Připojen/Connected` (from `contract.user_id` presence check)

Both remain unchanged after leaving company.

## Edge Cases & Scenarios

### Scenario 1: User Leaves Company
**Current Flow**:
1. User clicks "Leave Company" 
2. CompanyUserRole.active → false ✓
3. Contract.status → should be `terminated` ❌
4. Contract.user_id → should remain (for history) ✓

**Expected Result**:
- Contract status: `terminated`
- Connection status: Still shows user (for audit trail)
- User cannot access company resources

### Scenario 2: User Rejoins Same Company Later
**Current Flow**:
1. Company sends new invitation (creates new Contract)
2. User has inactive CompanyUserRole from previous membership
3. User accepts invitation

**Issues to Consider**:
- Should reactivate existing CompanyUserRole or create new?
- What happens to old terminated contracts?
- How to handle role assignment (might be different than before)?

**Recommended Approach**:
```ruby
# In CompanyConnectionsController#accept
company_user_role = CompanyUserRole.with_inactive
  .find_or_initialize_by(
    user: current_user,
    company: contract.company
  )
company_user_role.active = true
company_user_role.role = Role.find_by(name: 'employee') # New role
```

### Scenario 3: Multiple Contracts in Same Company
**Edge Case**: User might have multiple contracts (different positions/periods)
- Only active contracts should be terminated when leaving
- Historical terminated contracts should remain unchanged
- New invitation creates new contract (don't reuse old ones)

### Scenario 4: Owner Leaving Company
**Current Protection**: ✓ Already prevents last owner from leaving
**Additional Consideration**: Should owner's contracts be handled differently?

## Technical Requirements

### 1. Fix Contract Termination
```ruby
# User model - leave_company method
def leave_company(company)
  ActiveRecord::Base.transaction do
    # ... existing code ...
    
    # FIX: Use unscoped to bypass tenant scoping
    contracts = Contract.unscoped
      .where(user_id: self.id, company_id: company.id)
      .where.not(status: :terminated) # Don't re-terminate
    
    contracts.each do |contract|
      contract.terminate! # This also handles CompanyUserRole
    end
    
    # ... rest of existing code ...
  end
end
```

### 2. Update Contract Model
```ruby
# Consider adding a "disconnected_at" timestamp
class Contract < ApplicationRecord
  # Add to track when user left company
  # disconnected_at :datetime
  
  def disconnect!
    update!(
      status: :terminated,
      disconnected_at: Time.current
    )
  end
end
```

### 3. Update ContractShow Component
```vue
<!-- Show different connection status for terminated contracts -->
<div v-if="contract.status === 'terminated'" class="text-red-600">
  {{ $t('disconnected', 'Odpojen') }}
  <span v-if="contract.disconnected_at" class="text-xs">
    ({{ disconnectedDate }})
  </span>
</div>
```

### 4. Handle Rejoin Logic
```ruby
# CompanyConnectionsController#accept
def accept
  ActsAsTenant.without_tenant do
    contract = Contract.find(params[:id])
    
    # Find existing role (even if inactive)
    company_user_role = CompanyUserRole.with_inactive
      .find_or_initialize_by(
        user: current_user,
        company: contract.company
      )
    
    # Reactivate or create new
    company_user_role.active = true
    company_user_role.role = Role.find_by(name: 'employee')
    company_user_role.is_primary = true unless current_user.company_user_roles.active.exists?
    
    if company_user_role.save
      contract.update(user: current_user)
      # Log rejoin event for audit trail
      Rails.logger.info "User #{current_user.id} rejoined company #{contract.company.id}"
    end
  end
end
```

## Testing Requirements

### Test Cases:
1. **Leave Company with Active Contract**
   - Verify contract is terminated
   - Verify user loses access
   - Verify contract shows in company but as terminated

2. **Leave Company with Multiple Contracts**
   - Verify all active contracts terminated
   - Verify suspended/terminated contracts unchanged

3. **Rejoin Same Company**
   - Verify new contract created
   - Verify CompanyUserRole reactivated
   - Verify access restored

4. **Leave While in Different Tenant Context**
   - Verify contracts still terminated correctly
   - Verify no cross-tenant data leakage

## Implementation Steps

1. **Fix immediate bug** (Contract termination)
   - Update `User#leave_company` with unscoped query
   - Test with multi-tenant scenarios

2. **Update UI** (ContractShow component)
   - Show proper disconnection status
   - Add disconnection timestamp display

3. **Enhance rejoin flow**
   - Update CompanyConnections acceptance logic
   - Add audit logging for rejoins

4. **Add comprehensive tests**
   - Unit tests for model methods
   - Integration tests for full flow
   - E2E tests for UI behavior

## Definition of Done

- [ ] Contracts are properly terminated when user leaves company
- [ ] Contract status shows correctly in UI after leaving
- [ ] Users can rejoin companies they previously left
- [ ] All edge cases have test coverage
- [ ] No tenant scoping issues in multi-company scenarios
- [ ] Audit trail maintained for leave/rejoin events