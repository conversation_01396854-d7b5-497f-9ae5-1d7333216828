# ISSUE: Playwright E2E Tests Failing Due to JWT Authentication LocalStorage Key Mismatch

**Status:** 🔴 Critical - All E2E tests failing  
**Priority:** High  
**Category:** Testing Infrastructure  
**Affects:** All Playwright E2E tests requiring authentication  

## Problem Summary

All Playwright E2E tests are failing because JWT tokens are stored in localStorage with incorrect keys, causing the SPA to treat authenticated users as unauthenticated and redirect to login pages.

## Root Cause Analysis

### Critical Issue: LocalStorage Key Mismatch

**Test Fixture Code** (storing token):
```typescript
// test/e2e/fixtures/auth.ts, lines 52-53
localStorage.setItem('jwt_token', authResponse.access_token);
localStorage.setItem('authToken', authResponse.access_token);
```

**Application Expected Key**:
```javascript
// app/frontend/services/jwtStorage.js, line 9
const STORAGE_KEY = 'TYMBOX_JWT_TOKEN';
```

**Impact**: <PERSON> never finds the JWT token because it's stored under wrong keys.

## Authentication Flow Analysis

### How SPA Authentication Works

1. **JWT API Login**: `/api/v1/auth/jwt_login` returns `access_token`
2. **Token Storage**: Application expects token in localStorage as `TYMBOX_JWT_TOKEN`
3. **Vuex State**: Token loaded from localStorage into `userStore.jwtToken` (memory)
4. **Router Guards**: Check `store.getters['userStore/isAuthenticated']`
5. **Request Interceptors**: Add `Authorization: Bearer <token>` from Vuex state
6. **Session Restoration**: If no Vuex token, call `/api/v1/auth/restore_session` as fallback

### Why Tests Fail

1. ✅ **API Authentication**: JWT login succeeds, token received
2. ❌ **Token Storage**: Token stored with wrong localStorage key (`jwt_token` vs `TYMBOX_JWT_TOKEN`)
3. ❌ **Token Loading**: Application can't find token in localStorage
4. ❌ **Vuex State**: `userStore.jwtToken` remains empty
5. ❌ **Router Guard**: `isAuthenticated` returns `false`
6. ❌ **Session Fallback**: `/api/v1/auth/restore_session` fails (no Redis session in test)
7. ❌ **Redirect**: Router redirects to login page

## Current Test Behavior

```bash
# Test Output After Initial Fix
Authenticating via JWT <NAME_EMAIL>...
JWT authentication <NAME_EMAIL>
JWT authentication setup <NAME_EMAIL>
# ✅ JWT token received and stored with correct key

Error: JWT authentication <NAME_EMAIL>. 
Redirected to login: http://192.168.1.51:5100/cs/users/sign_in
# ❌ Still redirected despite correct localStorage key
```

## Update: Additional Investigation Required

**Status**: LocalStorage key mismatch fixed, but issue persists. Additional root causes identified:

1. **✅ Fixed**: LocalStorage key mismatch (`jwt_token` → `TYMBOX_JWT_TOKEN`)
2. **🔍 Investigating**: Vuex store initialization timing
3. **🔍 Investigating**: SPA routing and authentication middleware timing
4. **🔍 Investigating**: Token validation and session restoration flow

## Reproduction Steps

1. **Run any E2E test**:
   ```bash
   npx playwright test test/e2e/tym47-calendar-button-positioning.spec.ts --project=chromium
   ```

2. **Observe behavior**:
   - JWT authentication succeeds
   - Token stored in localStorage with wrong key
   - Navigation to protected routes redirects to login
   - Test fails with "Authentication failed - redirected to login"

## File Analysis

### Key Files Involved

- **`test/e2e/fixtures/auth.ts`** - Authentication fixture (NEEDS FIX)
- **`app/frontend/services/jwtStorage.js`** - Defines correct localStorage key
- **`app/frontend/store/userStore.js`** - Vuex authentication state
- **`app/frontend/router/index.js`** - Router guards checking authentication
- **`app/frontend/services/authService.js`** - Token restoration logic

### Architecture Understanding

```mermaid
graph TD
    A[JWT API Login] --> B[Receive access_token]
    B --> C[Store in localStorage as TYMBOX_JWT_TOKEN]
    C --> D[Load into Vuex userStore]
    D --> E[Router Guard Checks Vuex]
    E --> F[Allow Protected Route Access]
    
    G[Test Fixture] --> H[Store as jwt_token]
    H --> I[Application Looks for TYMBOX_JWT_TOKEN]
    I --> J[Token Not Found]
    J --> K[Vuex State Empty]
    K --> L[Router Guard Returns False]
    L --> M[Redirect to Login]
```

## Solutions

### ✅ Solution 1: Fix LocalStorage Key (Minimal)

**Update test fixture to use correct key**:

```typescript
// test/e2e/fixtures/auth.ts, line 52
localStorage.setItem('TYMBOX_JWT_TOKEN', authResponse.access_token);
```

### ✅ Solution 2: Proper Vuex Integration (Recommended)

**Initialize both localStorage and Vuex state**:

```typescript
await page.evaluate((authResponse) => {
  // Store with correct localStorage key
  localStorage.setItem('TYMBOX_JWT_TOKEN', authResponse.access_token);
  
  // Initialize Vuex store if available
  if (window.$store) {
    window.$store.commit('userStore/SET_JWT_TOKEN', authResponse.access_token);
    window.$store.commit('userStore/SET_USER', authResponse.user);
  }
}, authData);
```

### ✅ Solution 3: Use Application's Auth Method

**Call the application's login method directly**:

```typescript
await page.evaluate((authResponse) => {
  // Use the same method the app uses for login
  if (window.AuthService) {
    window.AuthService.setAuthToken(authResponse.access_token);
    window.AuthService.setUser(authResponse.user);
  }
}, authData);
```

## Testing Validation

### Success Criteria

1. **JWT Token Stored Correctly**: `localStorage.getItem('TYMBOX_JWT_TOKEN')` returns valid token
2. **Vuex State Populated**: `store.getters['userStore/isAuthenticated']` returns `true`
3. **Protected Routes Accessible**: Navigation to `/cs/calendar`, `/cs/mainbox` succeeds
4. **No Login Redirects**: Tests reach intended pages without authentication redirects

### Test Command

```bash
# Test single spec with debug output
npx playwright test test/e2e/tym47-calendar-button-positioning.spec.ts:59 --project=chromium --reporter=line

# Expected successful output:
# ✅ JWT authentication successful
# ✅ Token stored with correct key
# ✅ Protected route accessible
# ✅ Test passes
```

## Priority & Impact

### Why This is Critical

- **All E2E Tests Affected**: No authentication-requiring tests can pass
- **False Negatives**: Features work correctly but tests fail
- **Development Velocity**: Blocks TDD workflow and feature validation
- **CI/CD Pipeline**: Prevents automated testing in deployment pipeline

### Business Impact

- Cannot validate calendar functionality fixes (TYM-47)
- Cannot ensure UI components work correctly
- Risk of shipping broken features due to test blindness
- Developer confidence in test suite compromised

## Estimated Fix Time

- **Solution 1 (localStorage key)**: 5 minutes
- **Solution 2 (Vuex integration)**: 15 minutes  
- **Solution 3 (Auth service)**: 30 minutes
- **Full validation**: 1 hour

## Related Issues

- **TYM-47**: Calendar button positioning tests blocked by this issue
- **Test Infrastructure**: General E2E testing reliability
- **Authentication Architecture**: Understanding between JWT-only vs session modes

---

**Created:** 2025-01-23  
**Last Updated:** 2025-01-23  
**Assigned To:** [To be assigned]  
**Labels:** `bug`, `testing`, `authentication`, `critical`