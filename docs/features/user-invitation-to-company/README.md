# User Invitation to Company - Feature Documentation

**Last Updated**: 2025-01-23  
**Status**: Working Feature Documentation  
**Purpose**: Complete architectural reference for user invitation system

## Architecture Overview

### Core Models & Relationships

```ruby
User
├── has_many :company_user_roles
├── has_many :companies (through: company_user_roles)
├── has_many :contracts

CompanyUserRole (PRIMARY CONNECTION)
├── belongs_to :user
├── belongs_to :company  
├── belongs_to :role
├── active: boolean (determines visibility in company index)
├── is_primary: boolean (determines current tenant)

Contract (SECONDARY CONNECTION - Required for work assignments)
├── belongs_to :company
├── belongs_to :user (optional: true) # nil = pending invitation
├── email: string # Links invitation to user before acceptance
├── status: active/suspended/terminated
```

## Company Display Logic

### CompanyIndex Display Conditions
A company appears in the user's company index when:

1. **PRIMARY CONDITION**: User has `CompanyUserRole` with `active: true`
2. **SECONDARY CONDITIONS**: 
   - Role relationship exists (`belongs_to :role`)
   - Company relationship exists (`belongs_to :company`)

### API Controller Implementation
```ruby
# app/controllers/api/v1/companies_controller.rb:91-94
@company_user_roles = current_user.company_user_roles
                                 .includes(:company, :role)
                                 .where(active: true)  # Only active roles displayed
```

### Frontend Implementation
```javascript
// app/frontend/components/companies/CompanyIndex.vue:129-153
axios.get('/api/v1/companies', {
  headers: { 'Accept': 'application/json' }
})
.then(response => {
  this.companyUserRoles = response.data.company_user_roles;
  this.currentTenant = response.data.current_tenant;
});
```

## Company Switching Data Flow

### Frontend Process
1. **User Action**: Click "Select" button → `switchAssignment(companyId)`
2. **Service Call**: `AuthService.switchCompany(companyId)`
3. **JWT Update**: New token generated with updated `company_id` in payload
4. **State Refresh**: Vuex store updated, UI refreshed
5. **Navigation**: Redirect to dashboard with new company context

### Backend Process
```ruby
# app/controllers/api/v1/companies_controller.rb:19-85
def switch_company
  # 1. VALIDATION: Ensure user has access
  target_company = current_user.companies.find(company_id)
  
  # 2. TENANT SWITCH: Update current context
  ActsAsTenant.current_tenant = target_company
  
  # 3. PRIMARY UPDATE: Set as user's primary company
  current_user.set_primary_company(target_company)
  
  # 4. JWT GENERATION: New token with updated company_id
  new_token = JwtService.encode_access_token(current_user.jwt_payload)
  
  # 5. REDIS UPDATE: Persist for page refresh
  JwtSessionService.update_session_token(user_id, session_id, new_token)
end
```

## CompanyConnection Acceptance Processes

### EXISTING User Flow (Secure Pattern)

**Process Flow**:
1. **Check Connection**: Verify user not already connected
2. **Send Notification**: Email via `CompanyConnectionMailer.existing_user_notification`
3. **User Login**: User logs in normally, sees pending contracts
4. **API Fetch**: `/api/v1/company_connections/fetch` shows pending contracts
5. **Manual Accept**: User clicks accept → `/api/v1/company_connections/accept`

**Implementation**:
```ruby
# app/services/invitation_service.rb:135-172
def handle_existing_user_invitation(user, first_name, last_name, sender, company)
  # Send notification email only - no role creation yet
  CompanyConnectionMailer.existing_user_notification(
    sender: sender,
    user: user,
    company: company,
    contract: nil
  ).deliver_now
end

# app/controllers/api/v1/company_connections_controller.rb:15-48
def accept
  # Creates CompanyUserRole AND links contract in single transaction
  company_user_role = CompanyUserRole.find_or_initialize_by(
    user: current_user,
    company: contract.company,
    active: true
  )
  company_user_role.role = Role.find_by(name: 'employee')
  
  if company_user_role.save
    contract.update(user: current_user)  # Links contract to user
  end
end
```

### NEW User Flow (Registration-Based)

**Process Flow**:
1. **Generate Token**: Create invitation token via `generate_invitation_token`
2. **Store Invitation**: Save in `Invitation` model with unique token
3. **Send Email**: Invitation email with registration link
4. **User Registration**: Complete registration via `complete_user_registration`
5. **Manual Acceptance**: User redirected to company connections for manual acceptance

**Implementation**:
```ruby
# app/services/invitation_service.rb:106-120
def complete_user_registration(...)
  if user.save
    # NEW users now follow same secure manual acceptance pattern as EXISTING users
    # Clean up invitation token without creating company connection
    clear_invitation_token(invitation_token, user.email, company_id)
    
    return { success: true, user: user }
  end
end
```

**Frontend Flow**:
```javascript
// app/frontend/views/auth/AcceptInvitationView.vue:221-225
// NEW users redirected to company connections for manual acceptance
this.$router.push({ 
  name: 'companyConnections', 
  params: { locale: this.$route.params.locale } 
})
```

## Contract.user_id = nil Logic Pattern

### Smart Workaround Explanation
The `Contract.user_id = nil` pattern serves as a **pending invitation marker**:

```ruby
# When invitation is sent
Contract.create!(
  email: '<EMAIL>',
  company: company,
  user_id: nil  # PENDING STATE
)

# When invitation is accepted (EXISTING users)
contract.update!(user_id: user.id)  # ACCEPTED STATE

# Query pending contracts
Contract.where(email: user.email, user_id: nil)
```

**Purpose**: This allows the system to:
1. Track pending invitations by email before user exists
2. Link contracts to users only after proper acceptance
3. Maintain data integrity across invitation workflows

## Security & Access Control

### Multi-Tenant Architecture
- All models use `acts_as_tenant(:company)` for data isolation
- JWT tokens include `company_id` for tenant context
- Company switching validates user access via `current_user.companies.find(company_id)`

### Authentication Flow
- JWT-only authentication with HttpOnly refresh cookies
- Access tokens include current `company_id` in payload
- Redis session persistence for page refresh scenarios

### Authorization System
- ActionPolicy with role-based access control
- Company-scoped permissions for all operations
- Frontend authorization via `authorizationMixin.js`

## Technical Implementation Files

### Core Service Files
- `app/services/invitation_service.rb` - Main invitation logic
- `app/controllers/api/v1/company_connections_controller.rb` - Acceptance API
- `app/controllers/api/v1/companies_controller.rb` - Company switching

### Frontend Components
- `app/frontend/components/companies/CompanyIndex.vue` - Company display
- `app/frontend/services/authService.js` - Company switching logic

### Models
- `app/models/company_user_role.rb` - Primary connection model
- `app/models/contract.rb` - Secondary connection model
- `app/models/user.rb` - User management and relationships

## API Endpoints

### Company Management
- `GET /api/v1/companies` - List user's companies
- `POST /api/v1/companies/switch_company` - Switch active company

### Company Connections
- `GET /api/v1/company_connections/fetch` - Get pending contracts
- `POST /api/v1/company_connections/:id/accept` - Accept invitation

### Authentication
- `POST /api/v1/auth/jwt_login` - JWT authentication
- `POST /api/v1/auth/jwt_logout` - JWT logout
- `POST /api/v1/auth/restore_session` - Session restoration

## Testing Scenarios

### Validation Queries
```ruby
# Check user's active companies
user.company_user_roles.where(active: true).includes(:company)

# Check pending contracts for user
Contract.where(email: user.email, user_id: nil)

# Verify contract linking
Contract.where(user: user, company: company)
```

### Test Cases
1. **EXISTING User**: Notification → Login → Manual acceptance → Contract linking
2. **NEW User**: Registration → Auto-acceptance → Contract processing
3. **Company Switching**: Validate access → Update JWT → Tenant switching
4. **Security**: Ensure users can only access authorized companies

---

## Integration Notes

### Database Relationships
- User ↔ Company via CompanyUserRole (many-to-many)
- User ↔ Company via Contract (many-to-many, work assignments)
- Both relationships required for complete company connection

### State Management
- JWT tokens carry company context
- Vuex manages client-side state
- Redis provides session persistence

This feature enables secure multi-tenant company management with proper invitation workflows for both new and existing users.