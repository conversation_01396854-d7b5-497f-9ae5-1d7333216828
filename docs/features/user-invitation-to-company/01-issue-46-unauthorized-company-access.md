# 01-Issue-46: Unauthorized Company Access - Invited User

**Issue ID**: TYM-46  
**Severity**: Critical Security Vulnerability  
**Status**: Fix Planned  
**Created**: 2025-01-23

## Problem Description

**CRITICAL SECURITY VULNERABILITY**: NEW users can access company data without properly completing invitation acceptance process.

### Vulnerable Flow
1. User receives invitation email to join company
2. User clicks invitation link and creates account
3. User logs in but does NOT accept invitation through UI
4. **SECURITY BREACH**: User can switch to inviter company via company switcher
5. User sees unauthorized employee data and company information

### Evidence from Logs
```
Company switch: User 129 switched to company 98
JWT tenant context set: company_id=98 for user=129 with role=employee
```

## Root Cause Analysis

### Workflow Inconsistency Between User Types

| Aspect | EXISTING User (Secure) | NEW User (Vulnerable) |
|--------|------------------------|----------------------|
| **CompanyUserRole Creation** | Manual (API accept) | **Automatic (registration)** |
| **Contract Linking** | ✅ During acceptance | ❌ **MISSING** |
| **Company Visibility** | After full acceptance | **Immediately** |
| **Switch Capability** | After proper acceptance | **Before proper acceptance** |

### Technical Root Cause

**NEW Users Vulnerable Flow**:
```ruby
# app/services/invitation_service.rb:249-253
company_user_role = user.company_user_roles.create!(
  company: company,
  role: employee_role,
  active: true  # ← USER SEES COMPANY IMMEDIATELY
)
# Contract linking is MISSING in this method!
```

**EXISTING Users Secure Flow**:
```ruby
# app/controllers/api/v1/company_connections_controller.rb:39-42
if company_user_role.save
  contract.update(user: current_user)  # ← PROPER CONTRACT LINKING
end
```

### Why Security Breach Occurs

1. **Display Logic**: Only checks `CompanyUserRole.active = true`
2. **Company Switching**: Validates via `current_user.companies.find(company_id)` (passes)
3. **Missing Validation**: No check for contract linking completion
4. **Premature Access**: User gets company access before proper acceptance

## Planned Fix: Option 1 - Manual Acceptance for Both

### Strategy
**Unify workflows by making NEW users follow the secure EXISTING user pattern**

### Implementation Plan

#### 1. Modify NEW User Registration Flow
- **Remove** auto-creation of CompanyUserRole during registration
- **Remove** automatic `accept_company_connection` call
- NEW users should follow same manual acceptance as EXISTING users

#### 2. Update InvitationService Logic
```ruby
# CURRENT (vulnerable):
def complete_user_registration(...)
  if user.save
    result = accept_company_connection(user, company_id, sender_id, invitation_token)
  end
end

# FIXED (secure):
def complete_user_registration(...)
  if user.save
    # Don't auto-accept - let user manually accept via UI
    return { success: true, user: user }
  end
end
```

#### 3. Ensure Consistent UI Flow
- NEW users see pending contracts via `/api/v1/company_connections/fetch`
- NEW users manually accept via `/api/v1/company_connections/accept`
- Both NEW and EXISTING users follow identical acceptance pattern

### Benefits of This Approach

✅ **Security**: No premature company access  
✅ **Consistency**: Both user types follow same secure pattern  
✅ **User Control**: Explicit acceptance required  
✅ **Audit Trail**: Clear acceptance workflow  
✅ **Minimal Changes**: Reuse existing secure infrastructure  

### Files to Modify

1. **`app/services/invitation_service.rb`**
   - Remove automatic `accept_company_connection` call
   - Modify `complete_user_registration` method

2. **`app/controllers/api/v1/invitations_controller.rb`**
   - Update NEW user registration completion
   - Ensure proper redirect to pending invitations

3. **Frontend invitation acceptance views**
   - Update NEW user flow to show pending invitations
   - Ensure consistent UI between NEW and EXISTING users

### Backward Compatibility
- Existing contracts and users remain unchanged
- Only affects NEW invitation registrations going forward
- No database migrations required

## Testing Plan

### Security Test Cases
1. **NEW User Registration**: Verify company NOT visible before manual acceptance
2. **Company Switching**: Verify user cannot switch to companies before acceptance
3. **Contract Linking**: Verify contracts properly linked after manual acceptance
4. **EXISTING User Flow**: Verify no regression in existing secure flow

### Validation Queries
```ruby
# Verify no premature CompanyUserRole creation
user = User.find_by_email('<EMAIL>')
user.company_user_roles.count # Should be 0 before acceptance

# Verify pending contracts exist
Contract.where(email: '<EMAIL>', user_id: nil).count # Should be > 0

# Verify proper linking after acceptance
Contract.where(email: '<EMAIL>', user_id: user.id).count # After acceptance
```

## Implementation Steps

1. **Create branch**: `fix/tym-46-unauthorized-company-access`
2. **Modify invitation service**: Remove auto-acceptance for NEW users
3. **Update frontend flows**: Ensure consistent invitation UI
4. **Add security tests**: Verify no premature access
5. **Create PR**: With comprehensive testing and documentation

## Success Criteria

- [ ] NEW users cannot see companies before manual acceptance
- [ ] NEW users cannot switch to companies before manual acceptance  
- [ ] NEW users must manually accept invitations like EXISTING users
- [ ] Contract linking works properly for both user types
- [ ] No regression in EXISTING user secure flow
- [ ] All security tests pass

---

**Priority**: Critical - Security vulnerability affects all NEW user invitations  
**Impact**: Medium - Changes workflow but improves security significantly  
**Effort**: Small - Removes problematic code rather than adding complexity