# Multi-Company Invitation Workflow Investigation Report

**Last Updated**: 2025-01-24  
**Status**: Investigation Complete  
**Purpose**: Analysis of multi-company invitation conflicts and workflow behavior

## Investigation Summary

Analysis of what happens when a user is invited by multiple companies but hasn't accepted the first invitation yet. Investigation reveals the system generally handles this correctly but has some user experience edge cases.

## Core Models and Data Flow

**Key Files:**
- `app/models/invitation.rb` - Invitation tracking
- `app/models/contract.rb` - Contract-based invitations  
- `app/models/user.rb` - User management
- `app/services/invitation_service.rb` - Main invitation logic
- `app/controllers/api/v1/company_connections_controller.rb` - Acceptance API

## Step-by-Step Workflow Analysis

### Scenario: User invited by Company A, then Company B (user doesn't exist yet)

#### STEP 1: Company <NAME_EMAIL>

1. **Contract Creation** (`contract.rb:23`)
   - Contract created with `user_id: nil`, `email: '<EMAIL>'`
   - Triggers `after_create :send_invitation`

2. **Invitation Process** (`invitation_service.rb:26-34`)
   - Checks `User.find_by(email: email)` → **nil** (user doesn't exist)
   - Calls `handle_new_user_invitation()`

3. **New User Invitation** (`invitation_service.rb:178-205`)
   - Creates `Invitation` record with unique token
   - Sends invitation email with registration link
   - User does NOT exist in database yet

#### STEP 2: Company B <NAME_EMAIL>

1. **Contract Creation**
   - Second contract created with `user_id: nil`, same email
   - Triggers second invitation process

2. **Invitation Process**
   - Again checks `User.find_by(email: email)` → **still nil**
   - Creates **second** `Invitation` record for Company B

3. **Database State After Both Invitations:**
```ruby
# Two pending invitations exist
Invitation.where(email: '<EMAIL>', status: 'pending')
# Returns 2 records - one for each company

# Two pending contracts exist  
Contract.where(email: '<EMAIL>', user_id: nil)
# Returns 2 records - one for each company
```

## Critical Findings & Potential Issues

### ✅ WORKS CORRECTLY:

1. **Multiple Invitations Are Allowed**
   - Invitation model validates uniqueness on `[:company_id, :status]` scope
   - Same email can have pending invitations from different companies
   - No conflicts in invitation creation

2. **Independent Invitation Tokens**
   - Each company gets separate invitation token
   - Each invitation has separate expiration (7 days)
   - Tokens don't interfere with each other

### ⚠️ POTENTIAL PROBLEM AREAS:

1. **User Registration Race Condition**
   - User could register via Company A link, then try to use Company B link
   - After registration, user exists → Company B invitation becomes "existing user" flow
   - This is handled correctly by the service

2. **Token Validation Issues**
   - If user registers via Company A token, Company B token remains valid
   - User could theoretically complete registration multiple times (prevented by email uniqueness)

3. **Company Connection Logic**
   - `InvitationService.accept_company_connection()` handles this correctly
   - Checks for existing `CompanyUserRole` before creating new one
   - Multiple acceptances are prevented

## Step-by-Step Resolution Workflow

### User receives two emails → registers via Company A link:

1. **Registration** (`invitation_service.rb:81-135`)
   - User clicks Company A invitation link
   - Completes registration via `complete_user_registration()`
   - User created in database
   - Company A invitation marked as 'accepted'

2. **User tries Company B link later:**
   - `validate_token()` finds valid Company B invitation
   - User already exists, email matches
   - `accept_company_connection()` creates second `CompanyUserRole`
   - Both companies now connected to user

### Alternative: User logs in normally before accepting either:

1. **After Registration** 
   - User exists in database  
   - Both companies have pending contracts: `Contract.where(email: user.email, user_id: nil)`

2. **Login & Fetch Pending Invitations**
   - API call: `GET /api/v1/company_connections/fetch`
   - Returns both pending contracts from both companies
   - User sees both companies in invitation list

3. **Manual Acceptance**
   - User clicks accept for Company A: `POST /api/v1/company_connections/:contract_id/accept`
   - Creates `CompanyUserRole`, links contract: `contract.update(user: current_user)`
   - Repeat for Company B

## Where It Could Break

### 🚨 CRITICAL BREAK POINTS:

1. **Authentication Lock-out** (`docs/fixes/02_existing_user_invitation_fix.md`)
   - JWT login requires `confirmed_at` OR `invitation_accepted_at`
   - New users only get `invitation_accepted_at` after accepting invitation
   - If user registers but doesn't accept, they can't login

2. **Email Uniqueness in Registration**
   - User tries to register via Company B link after already registering via Company A
   - Will fail with email validation error
   - No clear error message for user

3. **Token Expiration Edge Case**
   - Company A token expires while Company B token still valid
   - User could register via expired Company A link (fails) then try Company B
   - Confusing user experience

### ✅ ROBUST AREAS:

1. **Database Constraints** 
   - Proper uniqueness constraints prevent duplicate invitations per company
   - Race condition protection in `Invitation.create_jwt_invitation!`

2. **Multi-Tenant Safety**
   - `ActsAsTenant.without_tenant` used in acceptance flow
   - Prevents tenant bleeding between companies

3. **Contract Linking**
   - Smart `user_id: nil` pattern allows pending invitations
   - Clean transition from pending to accepted state

## Technical Implementation Details

### Invitation Model Constraints
```ruby
# app/models/invitation.rb:13-17
validates :email, 
  uniqueness: { 
    scope: [:company_id, :status], 
    conditions: -> { where(status: 'pending') }
  }
```

### Contract Invitation Trigger
```ruby
# app/models/contract.rb:23
after_create :send_invitation, if: :email_present?
```

### Service Layer Logic
```ruby
# app/services/invitation_service.rb:26-34
def send_invitation(email:, first_name:, last_name:, sender:, company:)
  existing_user = User.find_by(email: email)
  
  if existing_user
    return handle_existing_user_invitation(existing_user, first_name, last_name, sender, company)
  else
    return handle_new_user_invitation(email, first_name, last_name, sender, company)
  end
end
```

### API Endpoints for Manual Acceptance
```ruby
# app/controllers/api/v1/company_connections_controller.rb
def fetch
  ActsAsTenant.without_tenant do
    pending_contracts = Contract.where(email: current_user.email, user_id: nil)
    render json: pending_contracts.as_json(include: { company: { only: [:id, :name] } })
  end
end

def accept
  ActsAsTenant.without_tenant do
    contract = Contract.find(params[:id])
    company_user_role = CompanyUserRole.find_or_initialize_by(
      user: current_user,
      company: contract.company,
      active: true
    )
    company_user_role.role = Role.find_by(name: 'employee')
    
    if company_user_role.save
      contract.update(user: current_user)
      render json: { success: true, message: 'Invitation accepted' }
    end
  end
end
```

## Conclusion

**The system generally handles multiple company invitations correctly** but has some **user experience edge cases**:

- ✅ Technical implementation is sound
- ✅ Database integrity maintained  
- ✅ Security boundaries preserved
- ⚠️ User experience could be confusing with expired/duplicate registrations
- ⚠️ Authentication requirements can lock users out temporarily

**The invitation system is architecturally robust but needs better error handling and user guidance for edge cases.**

## Recommendations for Improvement

1. **Better Error Messaging**
   - Clear messages when user tries to register with existing email
   - Guidance for users with expired tokens

2. **Token Management**
   - Consider automatically expiring other company tokens when user registers
   - Or provide clear workflow for handling multiple pending invitations

3. **User Experience Enhancement**
   - Dashboard showing all pending invitations after registration
   - Single-click acceptance for remaining company invitations

4. **Authentication Flow**
   - Ensure smooth login after registration regardless of acceptance status
   - Clear documentation of authentication requirements