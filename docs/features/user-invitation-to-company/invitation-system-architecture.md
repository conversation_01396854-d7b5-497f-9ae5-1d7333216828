# Company Invitation System Architecture

**Last Updated**: 2025-07-25  
**Status**: Complete Feature Documentation  
**Purpose**: Comprehensive reference for the company invitation and contract system

## Overview

The company invitation system allows companies to invite users to join their workspace by creating detailed "draft" contracts that serve as formal job offers. This system operates across multiple companies (cross-tenant) to enable users to discover and accept invitations from any company.

## Core Architecture

### Multi-Tenant Design
- **Primary Model**: `CompanyUserRole` - Establishes active user-company relationships
- **Secondary Model**: `Contract` - Contains detailed invitation/job offer information
- **Cross-Tenant Discovery**: Users can see pending invitations from ALL companies
- **Tenant Isolation**: Active users are scoped to their current company context

### Key Models & Relationships

```ruby
User
├── has_many :company_user_roles
├── has_many :companies (through: company_user_roles)
├── has_many :contracts
└── primary_company (CompanyUserRole with is_primary: true)

Company
├── has_many :company_user_roles
├── has_many :users (through: company_user_roles)
├── has_many :contracts
└── acts_as_tenant

Contract (Draft Job Offer)
├── belongs_to :company
├── belongs_to :user (optional - nil for pending invitations)
├── email (invitation target)
├── first_name, last_name
├── job_title, work_place, contract_type
├── valid_since, valid_through
└── status (active/suspended/terminated)

CompanyUserRole (Active Relationship)
├── belongs_to :user
├── belongs_to :company
├── belongs_to :role
├── active (boolean - determines visibility)
├── is_primary (boolean - determines current tenant)
└── default_scope { where(active: true) }
```

## Invitation Workflow

### 1. Company Creates Invitation
```ruby
# Company creates a detailed "draft" contract
contract = company.contracts.create!(
  email: "<EMAIL>",
  first_name: "John",
  last_name: "Doe",
  job_title: "Software Developer",
  work_place: "Prague Office",
  contract_type: "employee",
  user_id: nil  # Pending invitation
)
```

### 2. User Discovers Invitations
```ruby
# Cross-tenant query to find all pending invitations
ActsAsTenant.without_tenant do
  pending_contracts = Contract.where(
    email: current_user.email, 
    user_id: nil
  )
end
```

### 3. User Reviews Invitation Details
Users see complete job offer information:
- Company name
- Job title and position details
- Workplace location
- Contract type and terms
- Invitation date

### 4. User Accepts Invitation
```ruby
# Creates active company relationship
company_user_role = CompanyUserRole.create!(
  user: current_user,
  company: contract.company,
  role: Role.find_by(name: 'employee'),
  active: true,
  is_primary: true  # If first company
)

# Links contract to user
contract.update!(user: current_user)
```

## API Endpoints

### Company Connections (Cross-Tenant)
```
GET /api/v1/company_connections/fetch
- Purpose: Discover pending invitations across ALL companies
- Scope: Cross-tenant (ActsAsTenant.without_tenant)
- Filter: current_user.email AND user_id: nil
- Returns: Contract details + company info

POST /api/v1/company_connections/:id/accept
- Purpose: Accept invitation and create active relationship
- Creates: CompanyUserRole with active: true
- Updates: Contract.user_id = current_user.id
```

### Company Management (Tenant-Scoped)
```
GET /api/v1/companies
- Purpose: List user's active companies
- Scope: User's active CompanyUserRoles only
- Returns: Companies where user has active roles

POST /api/v1/companies/switch_company
- Purpose: Change current tenant context
- Updates: User's primary company
- Generates: New JWT with updated company_id
```

## Frontend Components

### CompanyConnections.vue
- **Purpose**: Display and manage pending invitations
- **Usage**: Standalone page + embedded in dashboard
- **API Call**: `/api/v1/company_connections/fetch`
- **Features**: 
  - Shows company name and job details
  - Accept invitation functionality
  - Responsive design (embedded/standalone modes)

### CompanyIndex.vue
- **Purpose**: Display user's active companies
- **Usage**: Company switching interface
- **API Call**: `/api/v1/companies`
- **Features**:
  - List active company memberships
  - Switch between companies
  - Company logos and role information

## Security Considerations

### Intended Business Flow
- **Cross-tenant access is by design** - Users must discover invitations from multiple companies
- **Detailed contract exposure is intentional** - Companies create job offers with specific details
- **Information disclosure is authorized** - Companies choose what details to include

### Security Boundaries
- **User isolation**: Users only see their own pending invitations (filtered by email)
- **Company isolation**: Active users are scoped to current tenant
- **Role-based access**: CompanyUserRole determines permissions within companies

### Architecture Decisions
- `ActsAsTenant.without_tenant` is necessary for cross-company invitation discovery
- Contract model serves dual purpose: invitation template + active employment record
- JWT tokens contain company_id only for active relationships, not pending invitations

## Data Flow Examples

### New User Invitation
1. Company creates Contract with user email (user_id: nil)
2. User registers/logs in
3. User calls `/company_connections/fetch` - sees pending invitation
4. User reviews job details (title, workplace, etc.)
5. User accepts - creates CompanyUserRole, updates Contract

### Existing User Invitation
1. Company creates Contract with existing user's email
2. User sees invitation in dashboard or company connections page
3. User reviews and accepts invitation
4. System creates new CompanyUserRole for additional company

### Company Switching
1. User has multiple active CompanyUserRoles
2. User switches company via `/companies/switch_company`
3. System updates primary company and JWT context
4. User operates within new company tenant scope

## Future Considerations

### Potential Enhancements
- Invitation expiration handling
- Bulk invitation management
- Custom invitation templates
- Invitation analytics and tracking

### Architectural Notes
- Consider separating invitation and contract models for cleaner separation of concerns
- Evaluate need for invitation-specific permissions vs contract permissions
- Monitor performance of cross-tenant queries as system scales
