# Linear Issue Template - Contract Disconnection Bug

**Issue Title**: Fix Contract disconnection when user leaves company (TYM-52 related)

**Labels**: `bug`, `high-priority`, `user-management`, `multi-tenant`

**Description**:

## Summary
When a user leaves a company, their contracts remain showing as "active" and "connected" in the contract details, despite the user losing actual access to the company. This creates data inconsistency and misleading UI information.

## Root Cause
Multi-tenant scoping bug in `User#leave_company` method. The Contract model has `acts_as_tenant(:company)` which automatically scopes queries to the current tenant. When a user leaves a company different from their current tenant context, the contract termination query returns empty results.

**Buggy Code Location**: `app/models/user.rb:106-109`
```ruby
# This fails when leaving non-current tenant company
contracts = Contract.where(user_id: self.id, company_id: company.id)
contracts.each { |contract| contract.terminate! }
```

## Current vs Expected Behavior

### Current (Incorrect) Behavior:
- ✅ CompanyUserRole.active → false 
- ❌ Contract.status → remains 'active'
- ❌ UI shows "Status: Aktivní" and "Přístup: Připojen"
- ❌ No audit trail of disconnection

### Expected (Correct) Behavior:
- ✅ CompanyUserRole.active → false
- ✅ Contract.status → 'terminated'
- ✅ UI shows disconnection status with timestamp
- ✅ Complete audit trail maintained

## Steps to Reproduce
1. Create user with access to multiple companies (Company A as primary, Company B as secondary)
2. While logged into Company A context, leave Company B
3. Check contract details for the user in Company B
4. Observe: Contract still shows as "active" and "connected"

## Evidence from Investigation
**Test Case Logs**:
```
JWT tenant context set: company_id=6 for user=6 with role=owner
Started POST "/companies/4/leave"
CompanyUserRole Update All - active=false ✓ (Works correctly)
Contract query result: 0 contracts found ❌ (Fails due to scoping)
```

## Impact Assessment
- **Severity**: High - Affects core user-company relationship logic
- **Scope**: All users who have left companies 
- **Business Impact**: 
  - Misleading information about user access status
  - Incomplete audit trails
  - Potential confusion about user permissions

## Technical Solution

### 1. Immediate Fix
```ruby
def leave_company(company)
  ActiveRecord::Base.transaction do
    # ... existing code ...
    
    # FIX: Use unscoped to bypass tenant scoping
    contracts = Contract.unscoped
      .where(user_id: self.id, company_id: company.id)
      .where.not(status: :terminated) # Don't re-terminate
    
    contracts.each do |contract|
      contract.terminate!
    end
    
    # ... rest of existing code ...
  end
end
```

### 2. UI Enhancement
Update ContractShow.vue to properly display disconnection status:
```vue
<div v-if="contract.status === 'terminated'" class="text-red-600">
  {{ $t('disconnected', 'Odpojen') }}
  <span v-if="contract.disconnected_at" class="text-xs">
    ({{ disconnectedDate }})
  </span>
</div>
```

## Testing Requirements
- [ ] Unit tests for `User#leave_company` with multi-tenant scenarios
- [ ] Integration tests for full leave/rejoin flow  
- [ ] E2E tests for UI behavior
- [ ] Test edge cases (multiple contracts, owner leaving, etc.)

## Acceptance Criteria
- [ ] Contracts are terminated when user leaves company
- [ ] UI correctly shows disconnection status
- [ ] No tenant scoping issues in multi-company scenarios
- [ ] Users can rejoin companies they previously left
- [ ] Complete audit trail for leave/rejoin events
- [ ] All edge cases have test coverage

## Related Documentation
- Investigation findings: `/docs/features/user-invitation-to-company/02-contract-disconnection-investigation.md`
- Edge cases analysis: `/docs/fixes/04_contract_disconnection_edge_cases.md`
- Architecture reference: `/docs/architecture/architecture_and_implementation_guide.md`

## Priority Justification
This is a high-priority data integrity issue affecting the core user-company relationship functionality. The bug creates misleading information and incomplete audit trails, which could lead to confusion about user access permissions and compliance issues.

---

**Referenced Issues**: TYM-52 (User leaving company functionality)