# Contract Disconnection Investigation - User Leave Company Analysis

**Investigation Date**: 2025-07-25  
**Status**: Investigation Complete - Bug Identified  
**Related Issues**: TYM-52 (User leaving company functionality)
**Priority**: High - Data Integrity Issue

## Investigation Summary

During investigation of user leave company functionality, discovered that users show as "active" and "connected" in contract details after leaving company, despite CompanyUserRole being properly deactivated.

## Discovery Process & Key Questions

### Q: What does "Status: Active" and "Connection: Connected" mean in ContractShow component?

**Discovery Process:**
1. Analyzed ContractShow.vue component structure
2. Traced data source for status and connection fields
3. Identified logic determining display states

**Answer:**
- **Status** (`contract.status`): Contract enum value - `'active'`, `'suspended'`, `'terminated'`, `'deleted'`
- **Connection** (`contract.user_id` presence): 
  - If `user_id` exists → "Připojen" (Connected)
  - If `user_id` is null → "Čeká na potvrzení" (Waiting for confirmation)

**Code Reference:**
```vue
<!-- ContractShow.vue:50-80 -->
<p v-if="contract.user_id" class="text-green-600 font-medium">
  {{ $t('connected', 'Připojen') }}
</p>
<p v-else class="text-yellow-600 font-medium">
  {{ $t('waiting_for_confirmation', 'Čeká na potvrzení') }}
</p>
```

### Q: What is CompanyConnection and how does it work?

**Discovery Process:**
1. Searched codebase for CompanyConnection references
2. Analyzed controllers and models
3. Traced invitation acceptance flow

**Answer:**
CompanyConnection is **not a model** but a **feature concept** managing user invitations:

**Components:**
- **CompanyUserRole** - Primary connection (access & roles)
- **Contract** - Secondary connection (work assignments) 
- **CompanyConnectionsController** - Invitation acceptance logic

**Flow:**
1. Company creates Contract with email, no user_id (pending invitation)
2. User receives invitation, logs in, sees pending contracts via `/api/v1/company_connections/fetch`
3. User clicks accept → `CompanyConnectionsController#accept`
4. Creates/reactivates CompanyUserRole + links Contract.user_id

**Code Reference:**
```ruby
# CompanyConnectionsController#accept
company_user_role = CompanyUserRole.find_or_initialize_by(
  user: current_user,
  company: contract.company,
  active: true
)
contract.update(user: current_user) # Links contract to user
```

### Q: What happens when user leaves company?

**Discovery Process:**
1. Analyzed `User#leave_company` method implementation
2. Traced execution flow in logs
3. Identified multi-tenant scoping issue

**Answer - Root Cause Found:**
```ruby
# User#leave_company (line 106-109) - BUGGY CODE
contracts = Contract.where(user_id: self.id, company_id: company.id)
contracts.each { |contract| contract.terminate! }
```

**The Problem:**
- User (id=6) tries to leave company id=4
- JWT tenant context is set to user's primary company (id=6)
- Contract model has `acts_as_tenant(:company)` which auto-scopes queries
- Query looks for contracts in company=4 but is scoped to company=6
- **Result: No contracts found, none terminated**

**Evidence from Logs:**
```
JWT tenant context set: company_id=6 for user=6 with role=owner
Started POST "/companies/4/leave"
CompanyUserRole Update All - active=false ✓ (This works)
Contract termination - 0 contracts found ❌ (This fails)
```

### Q: What should happen vs what actually happens?

**Expected Flow:**
1. CompanyUserRole.active → false ✓
2. Contract.status → 'terminated' ❌ 
3. Contract.user_id → remains (for audit) ✓
4. UI shows "Disconnected" status ❌

**Actual Flow:**
1. CompanyUserRole.active → false ✓
2. Contract.status → still 'active' ❌
3. Contract.user_id → still present ✓
4. UI shows "Connected" status ❌

### Q: What about reconnection scenarios?

**Discovery Process:**
1. Analyzed CompanyConnectionsController acceptance logic
2. Reviewed CompanyUserRole with_inactive scope
3. Identified rejoining flow

**Answer - Reconnection Scenarios:**

**Scenario 1: User Rejoins Same Company**
- Company sends new invitation (creates new Contract)
- CompanyConnectionsController uses `find_or_initialize_by` 
- Can reactivate existing inactive CompanyUserRole
- Creates new Contract for new work relationship

**Scenario 2: Multiple Leave/Rejoin Cycles**
- Each invitation creates new Contract
- Previous terminated contracts remain for history
- CompanyUserRole can be reactivated multiple times

**Code Reference:**
```ruby
# Supports rejoining via find_or_initialize_by
company_user_role = CompanyUserRole.find_or_initialize_by(
  user: current_user,
  company: contract.company,
  active: true # Will reactivate if found
)
```

## Technical Root Cause Analysis

### Multi-Tenant Scoping Bug
The `Contract` model has `acts_as_tenant(:company)` which automatically scopes all queries to the current tenant. When a user leaves a company different from their current tenant context, the contract termination query returns empty results.

### Fix Required
```ruby
# User#leave_company - Fixed version
def leave_company(company)
  ActiveRecord::Base.transaction do
    # ... existing code ...
    
    # FIX: Use unscoped to bypass tenant scoping
    contracts = Contract.unscoped
      .where(user_id: self.id, company_id: company.id)
      .where.not(status: :terminated) # Don't re-terminate
    
    contracts.each do |contract|
      contract.terminate!
    end
    
    # ... rest of code ...
  end
end
```

## Data Integrity Impact

### Current State Issues
1. **Contract Status Inconsistency**: Contracts remain 'active' after user leaves
2. **UI Misleading Information**: Shows "Connected" when user has no access
3. **Audit Trail Incomplete**: No record of when disconnection occurred
4. **Business Logic Confusion**: Active contracts for users with no company access

### Risk Assessment
- **Severity**: High - Affects core user-company relationship logic
- **Scope**: All users who have left companies (likely widespread)
- **Business Impact**: Confusion about user access status, potential security concerns

## Recommended Solution Strategy

### Phase 1: Fix Immediate Bug
1. Update `User#leave_company` to use unscoped Contract query
2. Add comprehensive tests for multi-tenant scenarios
3. Verify fix in staging environment

### Phase 2: Enhance UI Feedback
1. Update ContractShow component to show disconnection status
2. Add timestamp for when user left company
3. Distinguish between "never connected" vs "disconnected" states

### Phase 3: Data Cleanup
1. Identify existing incorrect contract states
2. Create migration to fix historical data
3. Add monitoring to prevent regression

## Next Steps
1. **Immediate**: Create Linear issue for tracking implementation
2. **Short-term**: Implement fix and testing
3. **Medium-term**: UI enhancements and data cleanup
4. **Long-term**: Enhanced audit logging for leave/rejoin events

---

This investigation revealed a critical multi-tenant scoping bug affecting contract disconnection integrity. The issue requires immediate attention to ensure proper user-company relationship management and data consistency.