# Email Notifications - Technical Specification

## Architecture Overview

### Current Infrastructure Analysis

**Existing Email System**:
- ActionMailer with Resend delivery service
- Multi-language support (Czech/Slovak)
- Template-based email generation
- CRON job infrastructure via `CronController`

**Current Models Integration**:
- `DailyLog`: Work session tracking with start/end times
- `DailyActivity`: Detailed activity tracking with work assignments
- `Work`: Job/task management with status lifecycle
- `Company`: Multi-tenant with subscription tiers
- `CompanySetting`: Company-level preferences including `daily_team_reports`

## New Components Required

### 1. Mailers

#### WorkActivityMailer
```ruby
class WorkActivityMailer < ApplicationMailer
  # Daily work activity summary for owners/managers
  def daily_summary(recipient, company, activities_data, date)
  
  # Work status change notification
  def work_status_changed(recipient, work, old_status, new_status)
  
  # Unfinished work alert
  def unfinished_work_alert(recipient, company, overdue_works, date)
end
```

#### WorkAnalyticsMailer
```ruby
class WorkAnalyticsMailer < ApplicationMailer
  # Weekly performance report
  def weekly_performance_report(recipient, company, analytics_data, week_range)
  
  # Monthly business intelligence report
  def monthly_analytics_dashboard(recipient, company, monthly_data, month)
end
```

#### WorkAnomalyMailer
```ruby
class WorkAnomalyMailer < ApplicationMailer
  # Real-time anomaly alerts
  def anomaly_alert(recipient, company, anomaly_type, details)
end
```

### 2. Services

#### WorkActivityReportService
```ruby
class WorkActivityReportService
  def self.send_daily_summaries
    # Process all Plus/Premium companies
    # Generate daily activity summaries
    # Send to eligible recipients (owners/managers)
  end
  
  private
  
  def self.generate_activity_summary(company, date)
    # Aggregate daily activities by employee
    # Calculate work progress metrics
    # Identify incomplete activities
  end
end
```

#### WorkAnalyticsService
```ruby
class WorkAnalyticsService
  def self.send_weekly_reports
    # Generate weekly performance analytics
  end
  
  def self.send_monthly_reports
    # Generate monthly business intelligence
  end
  
  private
  
  def self.calculate_productivity_metrics(company, period)
    # Work completion rates
    # Time allocation analysis
    # Employee performance metrics
  end
end
```

#### WorkAnomalyDetectionService
```ruby
class WorkAnomalyDetectionService
  def self.detect_and_alert
    # Scan for anomalies in real-time
    # Send immediate alerts
  end
  
  private
  
  def self.detect_hanging_activities
    # Find Daily Activities without end_time > 12 hours
  end
  
  def self.detect_unusual_patterns
    # Work started outside business hours
    # Excessive break times
    # Location anomalies
  end
end
```

### 3. Database Extensions

#### CompanySetting Enhancements
```ruby
# Add email notification preferences
add_column :company_settings, :work_activity_emails, :boolean, default: false
add_column :company_settings, :work_anomaly_alerts, :boolean, default: false
add_column :company_settings, :weekly_analytics_emails, :boolean, default: false
add_column :company_settings, :monthly_analytics_emails, :boolean, default: false
```

#### UserSetting Enhancements
```ruby
# Add individual email preferences
add_column :user_settings, :email_notifications_enabled, :boolean, default: true
add_column :user_settings, :work_status_notifications, :boolean, default: true
add_column :user_settings, :anomaly_notifications, :boolean, default: true
```

### 4. CRON Job Extensions

#### New CronController Actions
```ruby
class CronController < ApplicationController
  # Daily work activity summaries - 6:00 PM local time
  def daily_work_summaries
    WorkActivityReportService.send_daily_summaries
  end
  
  # Unfinished work alerts - 8:00 AM local time
  def unfinished_work_alerts
    WorkActivityReportService.send_unfinished_alerts
  end
  
  # Weekly performance reports - Monday 7:00 AM
  def weekly_analytics
    WorkAnalyticsService.send_weekly_reports
  end
  
  # Monthly analytics - 1st of month, 8:00 AM
  def monthly_analytics
    WorkAnalyticsService.send_monthly_reports
  end
  
  # Anomaly detection - Every 2 hours
  def detect_anomalies
    WorkAnomalyDetectionService.detect_and_alert
  end
end
```

## Data Aggregation Queries

### Daily Activity Summary Query
```sql
-- Aggregate daily activities by employee and work
SELECT 
  c.first_name, c.last_name,
  w.title as work_title,
  da.activity_type,
  SUM(da.duration) as total_duration,
  COUNT(da.id) as activity_count,
  MIN(da.start_time) as first_activity,
  MAX(da.end_time) as last_activity
FROM daily_activities da
JOIN contracts c ON da.contract_id = c.id
LEFT JOIN works w ON da.work_id = w.id
WHERE da.start_time::date = ?
  AND c.company_id = ?
GROUP BY c.id, w.id, da.activity_type
ORDER BY c.first_name, w.title;
```

### Unfinished Work Detection Query
```sql
-- Find works that should be completed but aren't
SELECT w.*, 
  COUNT(da.id) as activity_count,
  MAX(da.end_time) as last_activity
FROM works w
LEFT JOIN daily_activities da ON w.id = da.work_id
WHERE w.company_id = ?
  AND w.status IN ('scheduled', 'in_progress')
  AND (
    w.scheduled_end_date < CURRENT_DATE OR
    (da.end_time IS NULL AND da.start_time < NOW() - INTERVAL '2 days') OR
    w.scheduled_start_date < CURRENT_DATE AND w.status = 'scheduled'
  )
GROUP BY w.id;
```

### Anomaly Detection Queries
```sql
-- Hanging daily activities (>12 hours without end)
SELECT da.*, c.first_name, c.last_name, w.title
FROM daily_activities da
JOIN contracts c ON da.contract_id = c.id
LEFT JOIN works w ON da.work_id = w.id
WHERE da.end_time IS NULL 
  AND da.start_time < NOW() - INTERVAL '12 hours'
  AND c.company_id = ?;

-- Daily logs not properly closed
SELECT dl.*, c.first_name, c.last_name
FROM daily_logs dl
JOIN contracts c ON dl.contract_id = c.id
WHERE dl.end_time IS NULL 
  AND dl.start_time < NOW() - INTERVAL '12 hours'
  AND c.company_id = ?;
```

## Email Templates Structure

### Template Hierarchy
```
app/views/work_activity_mailer/
├── daily_summary.html.erb
├── daily_summary.text.erb
├── work_status_changed.html.erb
├── work_status_changed.text.erb
└── unfinished_work_alert.html.erb

app/views/work_analytics_mailer/
├── weekly_performance_report.html.erb
├── weekly_performance_report.text.erb
├── monthly_analytics_dashboard.html.erb
└── monthly_analytics_dashboard.text.erb

app/views/work_anomaly_mailer/
├── anomaly_alert.html.erb
└── anomaly_alert.text.erb
```

### Localization Keys
```yaml
# config/locales/mailer.cs.yml
work_activity_mailer:
  from: 'Týmbox Práce <<EMAIL>>'
  daily_summary:
    subject: 'Denní přehled práce'
    title: 'Přehled pracovních aktivit'
    no_activities: 'Žádné aktivity'
    total_hours: 'Celkem hodin'
    
  work_status_changed:
    subject: 'Změna stavu práce'
    status_changed: 'Stav práce byl změněn'
    
  unfinished_work_alert:
    subject: 'Nedokončené práce'
    overdue_works: 'Práce po termínu'
```

## Performance Considerations

### Query Optimization
- Add database indexes for time-based queries
- Use materialized views for complex analytics
- Implement query result caching for repeated calculations

### Email Delivery
- Use `deliver_later` for non-critical notifications
- Implement email queue monitoring
- Add retry logic for failed deliveries

### Resource Management
- Batch process companies to avoid memory issues
- Implement rate limiting for email sending
- Monitor CRON job execution times

## Security & Privacy

### Data Access Control
- Respect company multi-tenancy boundaries
- Verify recipient permissions before sending
- Sanitize sensitive data in email content

### Subscription Validation
- Verify Plus/Premium subscription status
- Check subscription expiry dates
- Gracefully handle subscription downgrades

## Testing Strategy

### Unit Tests
- Test each mailer with sample data
- Verify email content and recipients
- Test subscription tier filtering

### Integration Tests
- Test complete CRON job workflows
- Verify database query performance
- Test email delivery in staging environment

### Preview Classes
```ruby
class WorkActivityMailerPreview < ActionMailer::Preview
  def daily_summary
    # Generate sample data for preview
  end
  
  def work_status_changed
    # Generate sample work status change
  end
end
```

## Monitoring & Analytics

### Email Metrics
- Track delivery rates by notification type
- Monitor open rates and engagement
- Measure impact on user retention

### System Health
- Monitor CRON job execution success
- Track email queue performance
- Alert on failed email deliveries

### Business Impact
- Measure work completion rate improvements
- Track subscription tier upgrades
- Monitor user satisfaction scores

## Rollout Plan

### Phase 1: Core Infrastructure (Week 1-2)
- Implement base mailers and services
- Add database migrations
- Create basic email templates

### Phase 2: Daily Notifications (Week 3-4)
- Deploy daily activity summaries
- Implement unfinished work alerts
- Add anomaly detection

### Phase 3: Analytics Reports (Week 5-6)
- Weekly performance reports
- Monthly analytics dashboard
- Advanced metrics calculation

### Phase 4: Optimization (Week 7-8)
- Performance tuning
- User feedback integration
- A/B testing for email content

This technical specification provides a comprehensive foundation for implementing the email notification system while maintaining code quality, performance, and user experience standards.
