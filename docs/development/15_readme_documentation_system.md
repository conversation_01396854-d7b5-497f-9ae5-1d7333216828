# DOCUMENTATION SYSTEM OVERVIEW

**Problem**: Too much documentation to load into context, causing agents to miss critical information
**Solution**: Hierarchical routing system - load only what you need

## HOW THE SYSTEM WORKS

### 1. Start with CLAUDE.md
- Points you to `docs/MASTER_DOC_INDEX.md`
- Contains minimal routing rules
- Forces you to check the index

### 2. Check MASTER_DOC_INDEX.md  
- Routes you to specific docs based on task type
- Organized by task categories (Security, Testing, UI, etc.)
- Tells you exactly what to load

### 3. Use QUICK_DOC_DECISION_TREE.md
- 30-second decision tree
- Visual flowchart for what to load
- Red flag keywords that require more docs

### 4. Load Task-Specific Documentation
- Each category has primary docs that cover 90% of needs
- Additional docs only loaded when primary doc references them
- Prevents context overload

## KEY DOCUMENTS BY PRIORITY

### Tier 1: Always Check
1. `CLAUDE.md` - Project rules and routing
2. `docs/MASTER_DOC_INDEX.md` - Documentation router

### Tier 2: Task-Specific
- **Security**: `MANDATORY_TESTING_PROTOCOL.md`
- **Testing**: `CONSOLIDATED_TESTING_GUIDE.md`  
- **UI**: `ui_styling_guide.md`
- **API**: `spa_endpoints.md`

### Tier 3: Load Only When Referenced
- Individual testing guides
- Specific feature documentation
- Historical context (like TYM-46 lessons)

## WHY THIS MATTERS

**TYM-46 Security Vulnerability** was missed because:
- Agent couldn't load all testing documentation
- Cherry-picked wrong guides
- Missed critical security testing requirements

This system ensures agents load the RIGHT documentation for their specific task.

## FOR FUTURE AGENTS

1. **Never skip the routing system** - It exists because skipping caused security vulnerabilities
2. **When in doubt about security** - Load ALL security docs, not just some
3. **Trust the consolidated guides** - They contain 90% of what you need
4. **The master index is maintained** - If docs are added/removed, index is updated

Remember: 2 minutes checking the right docs saves hours of fixing security vulnerabilities.