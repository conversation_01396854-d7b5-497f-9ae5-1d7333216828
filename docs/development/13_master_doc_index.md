# MASTER DOCUMENTATION INDEX - LOAD ONLY WHAT YOU NEED

**Purpose**: Route agents/developers to the RIGHT documentation for their task

## CRITICAL RULE: CHECK THIS INDEX FIRST

Before starting ANY task, identify your task type below and load ONLY the relevant documentation.

## 🚨 SECURITY TASKS
**If your task mentions**: vulnerability, security, unauthorized access, permissions
**MUST LOAD**:
- `docs/testing/MANDATORY_TESTING_PROTOCOL.md` - How to test security issues
- `test/support/security-log-monitor.ts` - Security log monitoring tool
- `POLICIES.md` - Authorization rules

**Signs you need this**:
- Linear issue mentions "security" or "vulnerability"
- Task involves user permissions or access control
- Anything about who can see/do what

## 🧪 TESTING TASKS
**Task type**: Writing or fixing tests
**PRIMARY GUIDE**: `docs/testing/CONSOLIDATED_TESTING_GUIDE.md` - Covers 90% of testing needs

**Additional docs ONLY if needed**:
- Security vulnerability testing → Also load `<PERSON>NDATORY_TESTING_PROTOCOL.md`
- First time with <PERSON><PERSON> → Reference original `playwright_testing_guide.md`
- Complex log analysis → Check `log-aware_testing_guide.md`

## 🎨 UI/FRONTEND TASKS
**If your task involves**: UI, styling, Vue components, frontend
**MUST LOAD**:
- `docs/development/ui_styling_guide.md`
- `docs/architecture/architecture_and_implementation_guide.md` (Vue sections only)

## 🔐 AUTHENTICATION/API TASKS
**If your task involves**: JWT, login, API endpoints
**MUST LOAD**:
- `docs/development/spa_endpoints.md`
- `docs/architecture/architecture_and_implementation_guide.md` (Auth sections only)

## 🌍 TRANSLATION TASKS
**If your task involves**: i18n, translations, locale
**MUST LOAD**:
- `docs/development/translations.md`

## 📊 DATABASE/MODELS
**If your task involves**: database, migrations, models
**MUST LOAD**:
- `docs/architecture/architecture_and_implementation_guide.md` (Database sections)
- Check for feature-specific docs in `docs/features/`

## DECISION TREE FOR AGENTS

```
START
  |
  Is it a security/vulnerability issue?
  YES → Load ALL security docs + MANDATORY_TESTING_PROTOCOL
  NO ↓
  
  Is it testing related?
  YES → What kind of test?
        - E2E/Integration → Playwright guides
        - Security → Security test docs
        - Unit → RSpec guides
  NO ↓
  
  Is it UI/Frontend?
  YES → UI styling guide + Vue sections of architecture
  NO ↓
  
  Is it API/Auth?
  YES → SPA endpoints + Auth architecture
  NO ↓
  
  Check docs/features/ for existing similar features
```

## MANDATORY CHECKS

1. **BEFORE ANY TASK**: Check if similar work exists in `docs/features/`
2. **FOR BUGS**: Check `docs/fixes/` for similar issues
3. **FOR SECURITY**: ALWAYS load security testing protocol

## RED FLAGS - LOAD MORE DOCS

If you see these keywords, load additional documentation:
- "unauthorized", "security", "vulnerability" → ALL security docs
- "test", "TDD", "verify" → Testing methodology docs
- "style", "UI", "design" → UI guidelines
- "JWT", "token", "auth" → Authentication docs

## DOC CONSOLIDATION NOTES

### Overlapping Documentation Found:
1. Multiple testing guides with similar content
2. Security testing split across multiple files
3. Architecture info duplicated in various guides

### Consolidated Structure:
- `MANDATORY_TESTING_PROTOCOL.md` - Security testing requirements
- `playwright_testing_guide.md` - General E2E testing
- `architecture_and_implementation_guide.md` - Single source of truth

Remember: It's better to load one extra relevant doc than to miss critical information.