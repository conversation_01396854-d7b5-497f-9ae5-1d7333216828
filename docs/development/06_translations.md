## Internationalization (i18n)

### Architecture
- default language is Czech - cs locale, second is Slovak - sk locale
- Rails-side: Standard Rails i18n with locale YAML files in `config/locales/`
- Vue-side: vue-i18n integrated with Rails i18n via API endpoint
- Translation loading: Fetched from server via `/api/v1/translations/:locale` endpoint
- Locale detection: Path-based strategy (`/:locale/` in URL paths) with cookie fallback

### Translation Structure
- Frontend translations in YAML under `front:` namespace (in `config/locales/*.yml`)
- Feature-specific translations grouped under feature keys (e.g., `booking:`, `works:`, etc.)
- Common, reusable words at root level (e.g., `front.cancel`, `front.save`)

### Usage in Vue Components
- Access translations with `$t('key', 'Default text')`
- Use feature-scoped keys: `$t('booking.expected_duration', 'Předpokládané trvaní')`
- Use fallback text for resilience and clarity during development

### Translation Guidelines
- Keep keys short and semantic (e.g., use `cancel` not `cancel_booking`)
- Use snake_case for all translation keys
- Add gender suffixes when needed (e.g., `cancelled_f` for feminine form)
- Place common UI terms at root level for reusability
- Never use the `front` prefix in component usage
- Never include the component name in translation keys
- Never put comments into the locale yml files 