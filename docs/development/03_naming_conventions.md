# Naming Conventions

## Language-Specific Conventions

### Ruby
- **Variables/Methods**: snake_case
- **Classes**: CamelCase
- **Constants**: SCREAMING_SNAKE_CASE
- **Files**: snake_case.rb

### JavaScript/Vue
- **Variables/Methods**: camelCase
- **Components**: PascalCase
- **Files**: PascalCase.vue for components, camelCase.js for utilities
- **Props**: camelCase in JavaScript, kebab-case in templates

### CSS
- **Classes**: Use BEM methodology within _variables.scss
- **Variables**: kebab-case with meaningful prefixes
- **Files**: snake_case.scss

## File Organization

### Vue Components
- **Location**: app/frontend/components/
- **Structure**: Organize by feature
- **Naming**: PascalCase (e.g., EventForm.vue, UserProfile.vue)

### Vuex Stores
- **Location**: app/frontend/store/
- **Modules**: Feature-based organization
- **Actions**: camelCase verbs (e.g., fetchUsers, updateProfile)

### Rails Files
- **Models**: snake_case.rb (e.g., user_profile.rb)
- **Controllers**: snake_case_controller.rb
- **Services**: snake_case_service.rb
- **Policies**: snake_case_policy.rb

## Evergreen Naming Rules

### Avoid Temporal References
- ❌ **NEVER use**: 'improved', 'new', 'enhanced', 'updated', 'refactored'
- ✅ **USE instead**: Descriptive, functional names

### Examples
- ❌ `NewUserForm` → ✅ `UserRegistrationForm`
- ❌ `ImprovedDashboard` → ✅ `ExecutiveDashboard`
- ❌ `EnhancedSearch` → ✅ `AdvancedSearch`

### Method Naming
- **Actions**: Use verbs (create, update, delete, fetch)
- **Queries**: Use question format (isActive?, hasPermission?)
- **Getters**: Use descriptive nouns (currentUser, activeContracts)

## Translation Keys

### Structure
- **Format**: snake_case
- **Scope**: Feature-scoped (e.g., `booking.cancel`, `works.assign`)
- **Common terms**: Root level (e.g., `cancel`, `save`, `delete`)

### Examples
```yaml
# Good
front:
  cancel: "Zrušit"
  save: "Uložit"
  booking:
    expected_duration: "Předpokládané trvání"
    cancel: "Zrušit rezervaci"
  works:
    assign: "Přiřadit práci"
```

## Data Attributes

### Vue Components
- **Component root**: `data-vue-component="component-name"`
- **Actions**: `data-action="action-name"`
- **Navigation**: `data-nav-link="page-name"`
- **Testing**: `data-testid="unique-identifier"`

### Form Elements
- **Inputs**: `data-field="field-name"`
- **Forms**: `data-form="form-name"`
- **Buttons**: `data-action="submit-form-name"`

## API Endpoints

### Naming Pattern
- **Namespace**: Always use `/api/v1/`
- **Resources**: Plural nouns (e.g., `/api/v1/users`, `/api/v1/events`)
- **Actions**: RESTful verbs (GET, POST, PUT, DELETE)

### Examples
- ✅ `/api/v1/works/assigned`
- ✅ `/api/v1/events/upcoming`
- ❌ `/works/assigned` (missing API namespace)
- ❌ `/api/v1/getUsers` (non-RESTful)
