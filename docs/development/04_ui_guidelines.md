# UI Guidelines & Styling

## STYLING, UI and UX

### Core UX Principles
- **App is UX and that means FAST**: no lag in clicks, no lag in display
- Proper user feedback is important but can not be overwhelming and cannot disrupt the first UX principle which is speed
- UI CSS elements must avoid shadows, animations and other CSS attributes that slow down the web page processing
- UI must be minimalist and fit the overall apps simple design and text readability, proper contrast
- UI must avoid unnecessary elements that disrupt attention
- UI must prefer first level visible buttons or action-links whenever possible not hiding them behind the dropdown menus - unless explicitly instructed
- App is for small slightly more conservative businesses - we do not use EMOJIS for other reasons than own development debugging
- Do not use hover change on buttons

### CSS Guidelines
- Use BEM methodology within _variables.scss
- Avoid shadows, animations and other performance-heavy CSS attributes
- Prioritize text readability and proper contrast
- Keep design minimalist and clean

## Button System Guidelines

### Button Hierarchy & Usage

**Primary Buttons (`.btn-primary`)**
- Use ONLY for main actions: form submissions, save operations, primary CTAs
- Should be prominent on the page but controlled - only one primary action per view section
- Examples: "<PERSON><PERSON><PERSON><PERSON>", "Vyt<PERSON><PERSON>it", "Potvrdit"

**Small Buttons (`.btn-small`)**
- Use for secondary actions that appear frequently on the page
- Perfect for: approve/decline, start/end, quick actions
- Should be neat and not distract from primary content
- Examples: "Začít", "Ukončit", "Schválit", "Detaily"

### Button Classes

**Base Class**
- `.btn` - Universal base styling with border radius and font-weight

**Sizes**
- `.btn-primary` - Default size for main actions (1rem font, 0.5rem 1rem padding)
- `.btn-small` - Small size for frequent secondary actions (0.875rem font, 0.5rem 0.75rem padding)

**Colors & Variants**
- `.btn-secondary` - Gray background for neutral actions
- `.btn-outline` - Outlined buttons for secondary actions
- `.btn-light` - Light blue background for subtle primary actions
- `.btn-danger-light` - Light red background for destructive actions
- `.btn-danger` - Red background for critical destructive actions
- `.btn-warning` - Yellow background for warning actions
- `.btn-success` - Green background for success actions

**Special Types**
- `.text-link-action` - Text-styled button for non-intrusive actions (not for use inside text content!)
- `.btn-icon-subtle` - Subtle icon-only buttons (collapse toggles, etc.)

### Button Usage Examples

```html
<!-- Primary form submission -->
<button type="submit" class="btn btn-primary">Uložit</button>

<!-- Secondary actions -->
<button class="btn btn-small btn-secondary">Detaily</button>
<button class="btn btn-small btn-outline">Zrušit</button>

<!-- Destructive actions -->
<button class="btn btn-small btn-danger-light">Smazat</button>

<!-- Text-style actions -->
<button class="text-link-action">Zobrazit vše</button>

<!-- Icon toggles -->
<button class="btn-icon-subtle">
  <ChevronDown :size="18" />
</button>
```

### Button States
- `:disabled` - Automatic disabled styling via CSS
- `:hover` - Subtle color changes (no animations)
- `:active` - Brief visual feedback on click

### ❌ Deprecated Button Classes (DO NOT USE)
- `.form-button`, `.form-button-secondary`, `.form-button-outline` - Use standard `.btn` classes instead
- `.btn-sm` - Use `.btn-small` instead
- `.btn-lg` - Use `.btn-primary` instead
- `.btn-blue`, `.btn-red` - Use semantic color classes instead
- `.btn-text` - Use `.text-link-action` instead

### Migration Guidelines
When updating existing components:
1. Replace deprecated classes with standard `.btn` + modifier classes
2. Ensure primary actions use `.btn-primary` (limit one per section)
3. Convert frequent secondary actions to `.btn-small`
4. Use `.text-link-action` for non-intrusive text-style actions
5. Maintain semantic meaning: danger for destructive, warning for caution, etc.

## Authorization & Subscription

### Component Architecture
- AdvancedFeature component checks subscription via `/api/v1/subscription_status`
- Company subscription is at company level, not user level
- Plus tier should show in Topbar, not Sidebar

### Common Issues
- currentPlan computed property conflicts in components using authorizationMixin
- 406 errors: Add `Accept: application/json` headers to non-API endpoints

## API Response Handling

### Response Structure
- Many Rails endpoints return wrapped responses (e.g., `{ colleagues: [...] }`)
- Always check response structure: `response.data.colleagues || response.data`
- Add Accept headers for JSON: `{ headers: { 'Accept': 'application/json' } }`

### Specific Examples
- MeetingForm expects: `/contracts/colleagues` → `{ colleagues: [...], current_user_contract_id: ... }`

### Error Handling
- Use flash messages via app/frontend/utils/flashMessage.js
- Provide clear user feedback without overwhelming the interface
- Maintain speed as the primary UX principle