# Pre-Flight Checks Guide forTýmbox

This guide documents various pre-flight checks that can be implemented to verify critical infrastructure before deployments or major changes.

## Existing Pre-Flight Checks

### 1. JWT Authentication Pre-Flight Check ✅
**Command**: `bin/rails jwt:preflight_check`  
**Purpose**: Verify all JWT authentication paths are working  
**What it tests**:
- JWT Login
- API Access with JWT
- Company Switching
- Token Refresh
- JWT Logout & Revocation

**When to use**:
- Before enabling JWT-only mode
- After authentication-related deployments
- When debugging auth issues

## Recommended Future Pre-Flight Checks

### 2. Controller JWT-Only Verification ✅
**Command**: `bin/rails auth:verify_jwt_only`
**Purpose**: Verify all authenticated controllers are JWT-only
**What it tests**:
- Scans all controllers that require authentication.
- Reports any controllers that contain legacy session-based code (e.g., `user_signed_in?`, direct `session` access).
**When to use**:
- Before final deployment of JWT-only mode.
- To ensure no new code accidentally re-introduces session logic.

### 3. PWA Pre-Flight Check
**Command**: `bin/rails pwa:preflight_check` (to be implemented)  
**Purpose**: Verify PWA readiness and functionality  
**What to test**:
```ruby
# lib/tasks/pwa_preflight_check.rake
namespace :pwa do
  desc "Pre-flight check for PWA functionality"
  task preflight_check: :environment do
    checks = []
    
    # Check HTTPS in production
    checks << check_https_enabled
    
    # Check manifest.json exists and is valid
    checks << check_web_app_manifest
    
    # Check service worker registration
    checks << check_service_worker
    
    # Check offline page exists
    checks << check_offline_fallback
    
    # Check cache headers for assets
    checks << check_asset_caching
    
    # Display results...
  end
end
```

### 4. Database Health Check
**Command**: `bin/rails db:health_check` (to be implemented)  
**Purpose**: Verify database performance and health  
**What to test**:
```ruby
# lib/tasks/db_health_check.rake
namespace :db do
  desc "Database health and performance check"
  task health_check: :environment do
    puts "🏥 Database Health Check"
    
    # Connection pool status
    pool = ActiveRecord::Base.connection_pool
    puts "- Pool size: #{pool.size}"
    puts "- Connections in use: #{pool.connections.count}"
    
    # Check for long-running queries
    long_queries = ActiveRecord::Base.connection.execute(
      "SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
       FROM pg_stat_activity 
       WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'"
    )
    
    # Check table sizes
    large_tables = ActiveRecord::Base.connection.execute(
      "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
       FROM pg_tables WHERE schemaname = 'public' 
       ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC LIMIT 5"
    )
    
    # Check index usage
    unused_indexes = ActiveRecord::Base.connection.execute(
      "SELECT schemaname, tablename, indexname, idx_scan
       FROM pg_stat_user_indexes
       WHERE idx_scan = 0 AND indexname NOT LIKE '%_pkey'"
    )
  end
end
```

### 5. Redis Health Check
**Command**: `bin/rails redis:health_check` (to be implemented)  
**Purpose**: Verify Redis performance and memory usage  
**What to test**:
```ruby
# lib/tasks/redis_health_check.rake
namespace :redis do
  desc "Redis health and memory check"
  task health_check: :environment do
    puts "🏥 Redis Health Check"
    
    # Basic connectivity
    puts "- Connected: #{Redis.current.ping == 'PONG' ? '✅' : '❌'}"
    
    # Memory usage
    info = Redis.current.info
    puts "- Memory used: #{info['used_memory_human']}"
    puts "- Memory peak: #{info['used_memory_peak_human']}"
    
    # Key distribution by namespace
    RedisNamespacing::NAMESPACES.each do |name, prefix|
      count = Redis.current.keys("#{prefix}:*").count
      puts "- #{name}: #{count} keys"
    end
    
    # Check for memory fragmentation
    fragmentation = info['mem_fragmentation_ratio'].to_f
    status = fragmentation > 1.5 ? '⚠️ High fragmentation' : '✅'
    puts "- Memory fragmentation: #{fragmentation} #{status}"
  end
end
```

### 6. Email Delivery Check
**Command**: `bin/rails email:test_delivery` (to be implemented)  
**Purpose**: Verify email configuration and delivery  
**What to test**:
```ruby
# lib/tasks/email_test_delivery.rake
namespace :email do
  desc "Test email delivery configuration"
  task test_delivery: :environment do
    test_email = ENV['TEST_EMAIL'] || '<EMAIL>'
    
    begin
      # Send test email
      TestMailer.test_email(test_email).deliver_now
      puts "✅ Email sent successfully to #{test_email}"
      
      # Check SMTP settings
      config = ActionMailer::Base.smtp_settings
      puts "SMTP Config:"
      puts "- Server: #{config[:address]}:#{config[:port]}"
      puts "- Auth: #{config[:authentication]}"
      puts "- TLS: #{config[:enable_starttls_auto]}"
      
    rescue => e
      puts "❌ Email delivery failed: #{e.message}"
    end
  end
end
```

### 7. Performance Baseline Check
**Command**: `bin/rails perf:baseline` (to be implemented)  
**Purpose**: Establish performance baselines  
**What to test**:
```ruby
# lib/tasks/performance_baseline.rake
namespace :perf do
  desc "Run performance baseline checks"
  task baseline: :environment do
    require 'benchmark'
    
    puts "🏎️  Performance Baseline Check"
    
    # Test critical endpoints
    endpoints = [
      { name: "Login", action: -> { User.first.valid_password?('test') } },
      { name: "Dashboard Load", action: -> { DailyLog.where(user_id: 1).recent.limit(10).to_a } },
      { name: "Company Switch", action: -> { Company.find(1).users.count } }
    ]
    
    endpoints.each do |endpoint|
      time = Benchmark.realtime { endpoint[:action].call }
      status = time < 0.1 ? '✅' : (time < 0.5 ? '⚠️' : '❌')
      puts "#{status} #{endpoint[:name]}: #{(time * 1000).round(2)}ms"
    end
    
    # Memory snapshot
    puts "\nMemory Usage:"
    puts "- Total: #{(`ps -o rss= -p #{Process.pid}`.to_i / 1024.0).round(2)} MB"
  end
end
```

### 8. Security Scan Check
**Command**: `bin/rails security:scan` (to be implemented)  
**Purpose**: Basic security checks  
**What to test**:
```ruby
# lib/tasks/security_scan.rake
namespace :security do
  desc "Run basic security checks"
  task scan: :environment do
    puts "🔒 Security Scan"
    
    # Check for default passwords
    default_users = User.where(email: ['<EMAIL>', '<EMAIL>'])
    puts "- Default users: #{default_users.any? ? '❌ Found!' : '✅ None'}"
    
    # Check HTTPS enforcement
    https_enforced = Rails.application.config.force_ssl
    puts "- HTTPS enforced: #{https_enforced ? '✅' : '⚠️ Only in production'}"
    
    # Check secret key base
    secret_present = Rails.application.credentials.secret_key_base.present?
    puts "- Secret key base: #{secret_present ? '✅' : '❌ Missing!'}"
    
    # Check for exposed tokens in logs
    log_filtering = Rails.application.config.filter_parameters
    puts "- Log filtering: #{log_filtering.include?(:token) ? '✅' : '❌'}"
  end
end
```

## Best Practices for Pre-Flight Checks

1. **Keep them fast** - Should complete in under 30 seconds
2. **Make them idempotent** - Running multiple times should be safe
3. **Clear output** - Use ✅ ❌ ⚠️ symbols for quick scanning
4. **Exit codes** - Return 0 for success, 1 for failure (CI/CD integration)
5. **Actionable errors** - Tell users exactly what's wrong and how to fix it

## Integration Examples

### GitHub Actions
```yaml
- name: Run Pre-flight Checks
  run: |
    bundle exec rails auth:verify_jwt_only
    bundle exec rails jwt:preflight_check
    bundle exec rails db:health_check
    bundle exec rails redis:health_check
```

### Deployment Script
```bash
#!/bin/bash
echo "Running pre-deployment checks..."

if bundle exec rails auth:verify_jwt_only; then
  echo "✅ Controller implementation check passed"
else
  echo "❌ Controller implementation check failed"
  exit 1
fi

if bundle exec rails jwt:preflight_check; then
  echo "✅ JWT auth check passed"
else
  echo "❌ JWT auth check failed"
  exit 1
fi

if bundle exec rails db:health_check; then
  echo "✅ Database check passed"
else
  echo "❌ Database check failed"
  exit 1
fi

echo "All checks passed! Proceeding with deployment..."
```

## Creating New Pre-Flight Checks

Template for new checks:
```ruby
namespace :feature do
  desc "Pre-flight check for [feature name]"
  task preflight_check: :environment do
    checker = FeaturePreflightChecker.new
    results = checker.run_all_checks
    
    # Display results
    puts "\n📊 [Feature] Pre-Flight Check Results:"
    puts "=" * 50
    
    results.each do |check_name, result|
      status = result[:success] ? "✅ PASS" : "❌ FAIL"
      puts "#{status} #{check_name}"
      puts "   #{result[:message]}" if result[:message]
    end
    
    # Exit with appropriate code
    exit results.all? { |_, r| r[:success] } ? 0 : 1
  end
end
```

## Conclusion

Pre-flight checks are invaluable for maintaining system reliability. The JWT pre-flight check has proven this by catching configuration issues before they became problems. Implement similar checks for other critical systems to ensure smooth operations and confident deployments.