General commands for implementing a Linear issue:
  Key Rules
  - Always start from latest master
  - One issue = one clean branch = one PR = one commit (ideally)
  - No <PERSON> references in commit messages
  - Test first (TDD)

  **Starting Implementation**
  # 1. Sync with latest master
  git checkout master
  git pull origin master
  # 2. Create feature branch from clean master
  git checkout -b fix/issue-name-from-linear
  # 3. Follow TDD process
  bundle exec rspec spec/path/to/relevant_tests.rb  # Run existing tests
  # Write failing test first
  bundle exec rspec spec/path/to/new_test.rb        # Confirm test fails
  # Implement fix
  bundle exec rspec spec/path/to/new_test.rb        # Confirm test passes
  
  **During Implementation**
  # Check status frequently
  git status
  git diff

  **Stage and commit incrementally (if needed)**
  git add file1 file2
  git commit -m "Descriptive commit message without Claude references"

  **Finishing Implementation**
  # 1. Final test run
  bundle exec rspec spec/path/to/relevant_tests.rb
  # 2. Stage all changes
  git add .
  # 3. Create single commit (if not done incrementally)
  git commit -m "Fix issue description (LINEAR-ID)"
  # 4. Push clean branch
  git push -u origin fix/issue-name-from-linear
  # 5. Create PR
  gh pr create --title "Fix: Issue description (LINEAR-ID)" --body "Description..."
  # 6. Update Linear issue to "In Review"
  # Use Linear MCP tools to move issue status


This is general instruction, but do not clean the branch at the end
  # 7. After PR approval, clean up
  git checkout master
  git pull origin master
  git branch -D fix/issue-name-from-linear
  git push origin --delete fix/issue-name-from-linear