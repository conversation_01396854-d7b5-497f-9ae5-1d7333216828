# Testing & Debugging Guide

## ✅ **ENHANCED: Claude Code Testing & Debugging**

### 🚀 **PRIMARY TESTING FLOW** (ALWAYS USE FIRST)

### Basic Setup
- Server runs with: `foreman start -f Procfile.dev`
- Server runs rails and vite on: http://192.168.1.51:5100
- <PERSON><PERSON> manages both Rails and Vite development servers
- Vite provides hot module replacement for Vue.js components
- Console forwarding enabled for real-time debugging
- LAN access configured for testing across devices

**1. Enhanced Capybara+Cuprite Testing**
- ✅ **Primary Tool**: `bundle exec rspec spec/features/` 
- ✅ **Network Testing**: `REMOTE_TESTING=true bundle exec rspec spec/features/`
- ✅ **Visual Debug**: `HEADLESS=false bundle exec rspec spec/features/test_spec.rb`
- ✅ **LAN Optimized**: Configured for 192.168.1.51:5100 development setup

**2. Claude Test Helpers** (in `spec/support/claude_test_helpers.rb`)
```ruby
# Authentication helpers
sign_in_test_user                    # Use <EMAIL>:123456
sign_in_as('admin')                  # Create and sign in as admin
sign_out_current_user               # Sign out current user

# Navigation helpers  
navigate_to('events')               # Navigate to events page
navigate_to('dashboard')            # Navigate to dashboard

# Vue component interaction
click_vue_component('header-nav', 'toggle')  # Click Vue component action
expect_vue_component_visible('sidebar')      # Assert component visibility

# Data attribute helpers (for testing elements with data attributes)
click_button_with_data_attribute('action', 'submit-event')
expect_element_with_data_attribute('testid', 'nav-dashboard')

# Domain-specific helpers
create_test_event(title: 'Test Event')      # Create test event
fill_event_form(title: 'New Event')         # Fill event form
submit_event_form                            # Submit form with data attributes

# Debug helpers
debug_page_state                     # Print current page state info
screenshot_for_claude('debug_name')  # Save screenshot for debugging
wait_for_vite_assets                 # Wait for Vue.js components to load
```

### 🔧 **PRIMARY DEBUGGING FLOW**

**1. Browser Console Forwarding (ALWAYS USE FIRST)**
- Browser console logs appear in Rails terminal with `[Browser]` prefix
- Real-time error monitoring during development and feature implementation  
- Automatic validation of implementations via console output
- **NO PUPPETEER NEEDED** - Use Vite console forwarding instead

**2. Rails Log Monitoring**
```bash
# Monitor Rails logs in real-time
tail -f log/development.log

# Filter for specific patterns
tail -f log/development.log | grep "\[SECURITY\]"
tail -f log/development.log | grep "ERROR"

# Monitor authentication events
rails auth:monitor
```

#### Quick Debug Setup for New Features/Testing
Add these simple console logs to components for immediate debugging:

```javascript
// Vue component lifecycle debugging
created() {
  console.log('🚀 ComponentName created');
},

// API call debugging  
async fetchData() {
  console.log('📡 Fetching data...');
  try {
    const response = await axios.get('/api/endpoint');
    console.log('✅ API Response:', response.data);
  } catch (error) {
    console.error('❌ API Error:', error);
  }
},

// User action debugging
handleClick() {
  console.log('👆 Button clicked', { userId: this.user.id });
},

// State change debugging
watch: {
  someData: {
    handler(newVal, oldVal) {
      console.log('🔄 Data changed:', { from: oldVal, to: newVal });
    },
    deep: true
  }
}
```

**Best Practices**:
- Use emojis for quick visual scanning in terminal
- Include relevant data objects, not just strings
- Remove debug logs before committing (unless documenting a fix)
- Watch your `foreman` terminal for real-time `[Browser]` output

### 🎯 **MANDATORY: Data Attributes for Vue Components**

**ALWAYS add these data attributes when creating/modifying Vue components:**

```vue
<!-- Component root -->
<div data-vue-component="component-name">

<!-- Interactive elements -->
<button data-action="submit-form" data-testid="submit-button">
<input data-field="user-email" data-testid="email-input">
<form data-form="event-form">

<!-- Navigation elements -->
<LocalizedLink data-nav-link="dashboard" data-testid="nav-dashboard">

<!-- Examples from implemented components -->
<nav data-vue-component="header-nav">
<button data-action="submit-event" data-testid="submit-event-form">
<input data-event-type="meeting" data-testid="event-type-meeting">
```

**Critical Elements That Must Have Data Attributes:**
- All form submit buttons: `data-action="submit-[form-name]"`
- All navigation links: `data-nav-link="[page-name]"`  
- All Vue component roots: `data-vue-component="[component-name]"`
- All interactive buttons: `data-action="[action-name]"`
- All form inputs: `data-field="[field-name]"`
- All test-critical elements: `data-testid="[unique-id]"`

### 🚫 **DEPRECATED: Puppeteer Usage**

**❌ Do NOT use Puppeteer for:**
- Feature testing (use Capybara+Cuprite)
- Browser automation (use enhanced test helpers)  
- Console debugging (use Vite console forwarding)
- Screenshots (use Capybara `screenshot_for_claude`)

**✅ Puppeteer archived to `test/archived_puppeteer/`**

### JWT Pre-Flight Check (USE BEFORE ANY MAJOR CHANGES!)
```bash
# Run this before enabling JWT-only mode or deploying
bin/rails jwt:preflight_check

# Quick health check
bin/rails jwt:health
```

### Other Useful Commands
```bash
# Check routes
bin/rails routes | grep [endpoint]

# Monitor authentication in real-time
rails auth:monitor

# View security events
tail -f log/development.log | grep "\[SECURITY\]"

# Monitor browser console logs (filtered)
# Look for [Browser] prefix in your foreman terminal
```

## TDD Implementation Process

### We practice TDD. That means:
- Write tests before writing the implementation code
- Only write enough code to make the failing test pass
- Update the code continuously while ensuring tests still pass

### TDD Implementation Process
1. Write a failing test that defines a desired function or improvement
2. Run the test to confirm it fails as expected
3. Write minimal code to make the test pass
4. Run the test to confirm success
5. Refactor code to improve design while keeping tests green
6. Repeat the cycle for each new feature or bugfix

### TDD Best Practices
- Start with the simplest possible test case
- Write tests that clearly express the intended behavior
- Keep test code clean and maintainable
- Use descriptive test names that explain what is being tested
- Test one thing at a time

## NOTIFICATION PROTOCOL

### IMPORTANT: Before any state where you are awaiting confirmation, awaiting input, awaiting decision, requesting permission, or waiting for review:

1. Send email via Gmail MCP with:
   - Subject: "Claude Code: [STATE] - [BRIEF_CONTEXT]"
   - Body: "Action required in terminal. Check your Claude Code session."
2. Then display the full details and request in the terminal as normal
3. Do not include command details or sensitive information in the email
4. Keep the email brief - just a notification that attention is needed

### EXAMPLES:
- Subject: "Claude Code: Awaiting Confirmation - File Deletion"
- Subject: "Claude Code: Awaiting Input - Database Connection"
- Subject: "Claude Code: Requesting Permission - System Modification"
