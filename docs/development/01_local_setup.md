# Local Development Setup

## Server Configuration

### Authorization & Subscription 
- AdvancedFeature component checks subscription via `/api/v1/subscription_status`
- Company subscription is at company level, not user level
- Plus tier should show in Topbar, not Sidebar
- Common issues:
  - currentPlan computed property conflicts in components using authorizationMixin
  - 406 errors: Add `Accept: application/json` headers to non-API endpoints

### API Response Handling
- Many Rails endpoints return wrapped responses (e.g., `{ colleagues: [...] }`)
- Always check response structure: `response.data.colleagues || response.data`
- Add Accept headers for JSON: `{ headers: { 'Accept': 'application/json' } }`
- MeetingForm expects: `/contracts/colleagues` → `{ colleagues: [...], current_user_contract_id: ... }`

