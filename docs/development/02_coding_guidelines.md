# Coding Guidelines & Conventions

## Code Style Guidelines

### Ruby
- Follow standard Rails conventions (MVC architecture)
- Models: Use validations, callbacks sparingly, prefer service objects for complex logic
- Controllers: Keep thin, use policies for authorization

### Vue.js
- Use Options API 
- We have full SPA and full JWT token auth application using router, vuex, axios
- Components: Organize by feature in app/frontend/components/
- State management: Use Vuex stores in app/frontend/store/

### Error Handling
- Use flash messages via app/frontend/utils/flashMessage.js

## Writing Code Principles

### Core Philosophy
- We prefer simple, clean, maintainable solutions over clever or complex ones, even if the latter are more concise or performant. Readability and maintainability are primary concerns.
- Make the smallest reasonable changes to get to the desired outcome. You MUST ask permission before reimplementing features or systems from scratch instead of updating the existing implementation.

### Change Management
- NEVER make code changes that aren't directly related to the task you're currently assigned. If you notice something that should be fixed but is unrelated to your current task, document it in the project BACKLOG.md.
- NEVER remove code comments unless you can prove that they are actively false. Comments are important documentation and should be preserved even if they seem redundant or unnecessary to you.

### Documentation Requirements
- All code files should start with a brief 2 line comment explaining what the file does. Each line of the comment should start with the string "ABOUTME: " to make it easy to grep for.
- When writing comments, avoid referring to temporal context about refactors or recent changes. Comments should be evergreen and describe the code as it is, not how it evolved or was recently changed.

### Naming Conventions
- **Ruby**: snake_case for variables/methods, CamelCase for classes
- **JavaScript**: camelCase for variables/methods, PascalCase for components
- **CSS**: Use BEM methodology within _variables.scss
- NEVER name things as 'improved' or 'new' or 'enhanced', etc. Code naming should be evergreen. What is new today will be "old" someday.

## TOOLING

### Development Tools
- Use context7 if needed up-to-date, version-specific documentation and code examples straight from the source - Context7 fetches up-to-date code examples and documentation right into the context.
- Always double check critical tasks, plans or implementation with Gemini
- We use standard ruby on rails with vue.js app, if you stuck on the circle issue, try to search the web for the similar issue, there is very good chance you will find existing solution  

### Local Development
- Server runs with: `foreman start -f Procfile.dev`
- Server runs rails and vite on: http://************:5100

## DOCUMENTING

### Comprehensive System Management and Documentation                     
- Keep detailed records of configurations, processes, and key learnings
- Maintain a systematic approach to capturing and organizing technical insights
- All document files must be placed into docs/ folder under respective folder 

### When Linear Issue Moves to "Done"
- Update Linear project with:
  - What was changed (user impact)
  - New scope/dependencies if any
- Update local feature README with:
  - Technical changes made
  - New file structure if changed
  - Updated setup/usage instructions

## DOCUMENTATION MAP
- **Architecture**: docs/architecture/
- **Features implemented**: docs/features/
- **Bug fixes**: docs/fixes/
- **Migration plans**: docs/migrations/
- **API documentation**: docs/api/

## BEFORE ANY TASK:
1. Check current architecture → docs/architecture/
2. Check if similar feature exists → docs/features/
3. Check if similar bug was fixed → docs/fixes/
4. ONLY THEN search/modify code
