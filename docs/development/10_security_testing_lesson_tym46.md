# SECURITY TESTING LESSON: TYM-46

**THE FAILURE**: <PERSON> missed a critical security vulnerability where users could access companies without accepting invitations.

**WHY IT HAPPENED**: Tested database state instead of actual user behavior.

## THE CRITICAL LESSON IN 30 SECONDS

```
WRONG APPROACH:
- Query database: "Does user have access?" → No
- Conclusion: "Vulnerability doesn't exist" ❌

RIGHT APPROACH:  
- Act as user: Click invitation → Login → Switch company
- See employee data? → VULNERABILITY EXISTS ✓
```

## WHAT TO DO DIFFERENTLY

1. **Test the EXACT scenario described**
   - User gets invitation email → Clicks link → Creates account → Can they access without accepting?

2. **Watch the logs**
   ```
   Company switch: User 129 switched to company 98  ← This proved vulnerability existed
   ```

3. **Test what users SEE, not database STATE**
   - Can user click company switcher?
   - Does employee data load?
   - What appears on screen?

## ONE-LINE SUMMARY

**Test runtime behavior, not static analysis - the database lies, user experience tells truth.**

## PREVENT THIS: USE SECURITY MONITOR

```typescript
const securityMonitor = attachSecurityMonitor(page);
// Test user flow
securityMonitor.assertCompanySwitchAuthorized(userId, companyId);
```

This would have caught TYM-46 immediately.