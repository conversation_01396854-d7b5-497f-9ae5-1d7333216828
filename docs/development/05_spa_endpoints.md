# SPA Endpoint Guidelines

## 🚨 **MANDATORY for all new features and API calls** 🚨

### Endpoint Rules
- **ALL data endpoints MUST use `/api/v1/` namespace**
- **NEVER use main controller endpoints** (e.g., `/works/assigned`, `/bookings/fetch`)
- **ALWAYS use API endpoints** (e.g., `/api/v1/works/assigned`, `/api/v1/bookings`)

### Correct Pattern Examples
✅ **CORRECT:**
- `/api/v1/works/assigned`
- `/api/v1/events`
- `/api/v1/employees`
- `/api/v1/bookings`
- `/api/v1/contracts/colleagues`

❌ **WRONG:**
- `/works/assigned`
- `/bookings/fetch`
- `/company_connections/fetch`
- `/contracts/colleagues` (without API namespace)

### Authentication Requirements
- API endpoints use JWT token authentication
- All API calls must include: `{ headers: { 'Accept': 'application/json' } }`
- JWT tokens are automatically handled by the SPA authentication system

### Request Headers
```javascript
// Required headers for all API calls
const headers = {
  'Accept': 'application/json',
  'Content-Type': 'application/json'
};

// Example API call
const response = await axios.get('/api/v1/works/assigned', { headers });
```

### Legacy Cleanup
- When updating existing features, migrate main controller calls to API endpoints
- Main controllers are for SSR legacy support only, not for SPA features
- Always check if an API endpoint exists before creating new main controller actions

### SPA Architecture Integration
- Vue: we have full SPA and full JWT token auth application using router, vuex, axios
- Components: Organize by feature in app/frontend/components/
- State management: Use Vuex stores in app/frontend/store/
- All data fetching should go through API endpoints with proper error handling