# QUICK DOCUMENTATION DECISION TREE

**Use this to decide which docs to load in 30 seconds**

```
Is your task about SECURITY/VULNERABILITY/PERMISSIONS?
  ├─ YES → Load:
  │   ├─ docs/testing/MANDATORY_TESTING_PROTOCOL.md
  │   ├─ test/support/security-log-monitor.ts
  │   └─ POLICIES.md
  │
  └─ NO → Continue ↓

Is your task about TESTING?
  ├─ YES → Load:
  │   ├─ docs/testing/CONSOLIDATED_TESTING_GUIDE.md (start here)
  │   └─ Only load more if guide references them
  │
  └─ NO → Continue ↓

Is your task about UI/FRONTEND/STYLING?
  ├─ YES → Load:
  │   └─ docs/development/ui_styling_guide.md
  │
  └─ NO → Continue ↓

Is your task about API/AUTH/JWT?
  ├─ YES → Load:
  │   └─ docs/development/spa_endpoints.md
  │
  └─ NO → Continue ↓

Is your task about TRANSLATIONS/I18N?
  ├─ YES → Load:
  │   └─ docs/development/translations.md
  │
  └─ NO → Check docs/features/ for similar work
```

## RED FLAG WORDS - LOAD MORE DOCS

If you see these words in the task, load security docs immediately:
- vulnerability
- unauthorized
- security
- exploit
- bypass
- access control
- permission

## QUICK REFERENCE

| Task Type | Primary Doc | When to Load More |
|-----------|-------------|-------------------|
| Security | MANDATORY_TESTING_PROTOCOL.md | Always for security |
| Testing | CONSOLIDATED_TESTING_GUIDE.md | 90% complete, rarely need more |
| UI/Style | ui_styling_guide.md | Complete guide |
| API/Auth | spa_endpoints.md | Complete guide |
| I18n | translations.md | Complete guide |

## THE GOLDEN RULE

**When in doubt about security → Load ALL security docs**

Better to spend 2 minutes loading the right docs than miss a critical vulnerability.