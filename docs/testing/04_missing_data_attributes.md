# Missing Data Attributes for Testing

This file tracks missing `data-testid` and `data-*` attributes needed for reliable Playwright testing across the AttendifyApp codebase.

## Purpose
- **Testability**: Enable reliable element selection in automated tests
- **Maintainability**: Reduce test brittleness from UI changes
- **Documentation**: Provide clear mapping of UI elements to test selectors

## Format
```
- Element: Description of the element
- Location: File path and approximate location
- Suggested Attribute: data-testid="suggested-name"
- Priority: High/Medium/Low
- Test Use Case: Why this element needs testing
```

---

## AUTHENTICATION & USER MANAGEMENT

### User Menu/Profile Elements
- **Element**: User initials circle and dropdown menu (bottom left)
- **Location**: Likely in main layout component
- **Current**: Shows "CL" initials and email `<EMAIL>`
- **Suggested Attribute**: `data-testid="user-menu"`
- **Priority**: High
- **Test Use Case**: Verify user authentication status, access logout, profile settings

### Logout Button
- **Element**: Logout option in user menu dropdown
- **Location**: User menu dropdown component
- **Suggested Attribute**: `data-testid="logout-button"`
- **Priority**: High
- **Test Use Case**: Test logout functionality

---

## DASHBOARD/MAINBOX ELEMENTS

### Company Selector
- **Element**: Company dropdown showing "Claude Company" with "Plus" badge
- **Location**: Sidebar area
- **Suggested Attribute**: `data-testid="company-selector"`
- **Priority**: Medium
- **Test Use Case**: Test multi-company switching, verify plus tier indicators

### Plus Tier Badge
- **Element**: "Plus" badge next to company name
- **Location**: Company header area
- **Suggested Attribute**: `data-testid="plus-tier-badge"`
- **Priority**: Medium
- **Test Use Case**: Verify plus tier features are available

---

## NAVIGATION ELEMENTS

### Main Navigation Items
- **Element**: "Titulka" (Dashboard) navigation item
- **Location**: Left sidebar navigation
- **Suggested Attribute**: `data-testid="nav-dashboard"` or `data-nav-link="dashboard"`
- **Priority**: Medium
- **Test Use Case**: Navigate between sections

### Calendar Navigation
- **Element**: "Kalendář" navigation item
- **Location**: Left sidebar under "VAŠE PODNIKÁNÍ"
- **Suggested Attribute**: `data-testid="nav-calendar"`
- **Priority**: Medium
- **Test Use Case**: Access calendar functionality

### Works/Orders Navigation  
- **Element**: "Zakázky" navigation item
- **Location**: Left sidebar
- **Suggested Attribute**: `data-testid="nav-works"`
- **Priority**: High
- **Test Use Case**: Critical for TYM-28 type testing (works management)

### Meeting Scheduler Navigation
- **Element**: "Plánovač schůzek" navigation item
- **Location**: Left sidebar
- **Suggested Attribute**: `data-testid="nav-meeting-scheduler"`
- **Priority**: Medium
- **Test Use Case**: Test meeting/booking functionality

---

## WORKS/TODAY'S WORK SECTION

### Today's Work Container
- **Element**: "Dnešní práce" (Today's Work) section container
- **Location**: Main dashboard area
- **Current**: Has `data-vue-component="today-work-section"` ✅ (Already exists)
- **Priority**: High
- **Test Use Case**: Core functionality for TYM-28 testing

### Individual Work Cards
- **Element**: Work item cards within Today's Work
- **Location**: TodayWorkSection.vue component
- **Suggested Attribute**: `data-testid="work-card"` or `data-work-id="{work.id}"`
- **Priority**: High  
- **Test Use Case**: Test work assignment display, interaction with specific works

### Work Action Buttons
- **Element**: "Začít" (Start) buttons on work cards
- **Location**: Work card components
- **Suggested Attribute**: `data-testid="start-work-button"` or `data-action="start-work"`
- **Priority**: High
- **Test Use Case**: Test work start functionality

### Work Details Buttons
- **Element**: "Detaily" (Details) buttons on work cards
- **Location**: Work card components  
- **Suggested Attribute**: `data-testid="work-details-button"`
- **Priority**: Medium
- **Test Use Case**: Test work detail viewing

---

## ACTIVITY TRACKING

### Activity Records Section
- **Element**: "Záznamy aktivit" (Activity Records) dropdown
- **Location**: Dashboard area
- **Suggested Attribute**: `data-testid="activity-records-section"`
- **Priority**: Medium
- **Test Use Case**: Test activity logging and time tracking

---

## REPORTS SECTION

### Monthly Report
- **Element**: "Měsíční výkaz" (Monthly Report) navigation
- **Location**: Left sidebar under "REPORTY"
- **Suggested Attribute**: `data-testid="nav-monthly-report"`
- **Priority**: Low
- **Test Use Case**: Test reporting functionality

### Activities Report
- **Element**: "Aktivity" navigation item
- **Location**: Left sidebar under "REPORTY"  
- **Suggested Attribute**: `data-testid="nav-activities"`
- **Priority**: Low
- **Test Use Case**: Test activity reporting

---

## ORGANIZATION SECTION

### Team Navigation
- **Element**: "Tým" (Team) navigation item
- **Location**: Left sidebar under "ORGANIZACE"
- **Suggested Attribute**: `data-testid="nav-team"`
- **Priority**: Medium
- **Test Use Case**: Test team management (admin/owner features)

### Invitations Navigation
- **Element**: "Pozvání" (Invitations) navigation item
- **Location**: Left sidebar
- **Suggested Attribute**: `data-testid="nav-invitations"`
- **Priority**: Medium
- **Test Use Case**: Test user invitation functionality

---

## SUCCESS MESSAGES & NOTIFICATIONS

### Success Banner
- **Element**: Blue success message "Přihlášení bylo úspěšné" (Login successful)
- **Location**: Appears after successful login
- **Suggested Attribute**: `data-testid="success-message"`
- **Priority**: Medium
- **Test Use Case**: Verify successful operations

---

## IMPLEMENTATION PRIORITY

### HIGH PRIORITY (Immediate Need)
1. `data-testid="user-menu"` - User authentication verification
2. `data-testid="work-card"` - Core work management testing  
3. `data-testid="start-work-button"` - Primary work actions
4. `data-testid="nav-works"` - Works section navigation
5. `data-testid="logout-button"` - Authentication flow

### MEDIUM PRIORITY (Next Phase)
1. `data-testid="company-selector"` - Multi-company testing
2. `data-testid="plus-tier-badge"` - Feature tier testing
3. `data-testid="nav-dashboard"` - Navigation testing
4. `data-testid="work-details-button"` - Work detail functionality

### LOW PRIORITY (Future Enhancement)
1. Report navigation items
2. Advanced activity tracking elements
3. Administrative function elements

---

## NOTES FOR IMPLEMENTATION

### Conventions to Follow
- Use descriptive, consistent naming: `data-testid="element-action"` 
- For navigation: `data-nav-link="section-name"`
- For actions: `data-action="action-name"`
- For form fields: `data-field="field-name"`

### Vue.js Integration
Since this is a Vue.js app, data attributes can be added dynamically:
```vue
<button 
  :data-testid="`start-work-${work.id}`"
  @click="startWork(work)"
>
  Začít
</button>
```

### Testing Benefits
Once implemented, tests become much more reliable:
```typescript
// Instead of fragile selectors
await page.locator('text=Začít').click(); // ❌ Fragile

// Reliable data-attribute selectors  
await page.locator('[data-testid="start-work-button"]').click(); // ✅ Stable
```

---

**Last Updated**: 2025-07-22  
**Status**: Initial documentation based on authentication testing  
**Next Review**: After high-priority attributes are implemented