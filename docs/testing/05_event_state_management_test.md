# Event State Management Testing Guide

## Test Cases for Event Deletion and Rejection

### 1. Event Deletion in CalendarSidebar ✅

**Test Steps:**
1. Open CalendarSidebar with an event selected
2. Click the delete button on the EventCard
3. Verify the event is deleted from the backend
4. Verify the EventCard is immediately removed from the DOM
5. Verify the sidebar closes automatically
6. Verify the selectedItem is cleared from the store

**Expected Behavior:**
- Event is removed from `state.events` array
- `selectedItem` is set to `null`
- `selectedItemType` is set to `null`
- `sidebarView` is set to `'unscheduled'`
- Sidebar closes immediately

### 2. Rejected Event Styling in CalendarEventItem ✅

**Test Steps:**
1. Create an event as a regular user
2. <PERSON><PERSON> as a manager and reject the event
3. <PERSON><PERSON> back as the regular user
4. View the calendar and locate the rejected event

**Expected Visual Indicators:**
- Event has reduced opacity (0.7)
- Event has red border (#ffcdd2)
- Event title has strikethrough text
- Event shows "REJECTED" badge in red
- Event background is grayed out (#fafafa)

### 3. Rejected Event Visibility Rules ✅

**Test Case A: Event Owner View**
1. Create an event as User A
2. Manager rejects the event
3. <PERSON>gin as User A
4. Verify the rejected event is visible in calendar with rejection styling

**Test Case B: Manager View**
1. User A creates an event
2. Login as Manager and reject the event
3. Stay logged in as Manager
4. Verify the rejected event is NOT visible in the calendar

**Test Case C: Other User View**
1. User A creates an event
2. Manager rejects the event
3. Login as User B (different user, not manager)
4. Verify the rejected event is NOT visible in the calendar

### 4. Meeting Conflict Exclusion ✅

**Test Steps:**
1. Create an event as User A
2. Manager rejects the event
3. Try to create a meeting that overlaps with the rejected event time
4. Verify the rejected event does NOT appear as a conflict
5. Verify the meeting can be created successfully

**API Endpoints to Test:**
- `/meetings/conflicts` - should exclude rejected events
- `/private_meetings/:token/meeting` - should exclude rejected events

### 5. Store State Management ✅

**Test the following store mutations and getters:**

**Mutations:**
- `REMOVE_EVENT` - removes event and clears selectedItem if needed
- `UPDATE_EVENT_STATUS` - updates event status to 'rejected'

**Getters:**
- `itemsByDate` - filters rejected events based on ownership
- `filteredEvents` - filters rejected events based on ownership
- `pendingEvents` - only shows pending events (excludes rejected)

**Actions:**
- `deleteEvent` - calls DELETE endpoint and REMOVE_EVENT mutation
- `rejectEvent` - calls POST /reject endpoint and UPDATE_EVENT_STATUS mutation

## Manual Testing Checklist

### As Regular User (Event Owner):
- [ ] Can see delete button on own events
- [ ] Cannot see delete button on other users' events
- [ ] Can see own rejected events with visual indicators
- [ ] Cannot see other users' rejected events
- [ ] Deleted events disappear immediately from calendar
- [ ] Sidebar closes after deleting event

### As Manager/Owner:
- [ ] Can see approve/reject buttons on pending events
- [ ] Cannot see delete button on other users' events
- [ ] Can only see delete button on own events
- [ ] Cannot see rejected events from other users
- [ ] Can see own rejected events with visual indicators
- [ ] Rejected events don't appear in meeting conflicts

### Cross-Component Consistency:
- [ ] Filtering works in NewMonthlyCalendar
- [ ] Filtering works in WeeklyCalendar
- [ ] Filtering works in MonthlyEventTable
- [ ] Filtering works in CalendarSidebar
- [ ] EventCard shows proper delete button visibility
- [ ] CalendarEventItem shows proper rejection styling

## Code Coverage

### Frontend Components Updated:
- ✅ `CalendarEventItem.vue` - Added rejection styling and status handling
- ✅ `CalendarSidebar.vue` - Fixed delete action (already correct)
- ✅ `MonthlyEventTable.vue` - Added event filtering
- ✅ `EventList.vue` - Added calendar store integration

### Store Updates:
- ✅ `calendarStore.js` - Enhanced filtering logic and selectedItem management
- ✅ Fixed `REMOVE_EVENT` mutation to clear selectedItem
- ✅ Updated `rejectEvent` action to use correct endpoint

### Backend Updates:
- ✅ `events_controller.rb` - Added reject endpoint
- ✅ `meetings_controller.rb` - Excluded rejected events from conflicts
- ✅ `public_meetings_controller.rb` - Excluded rejected events from conflicts
- ✅ `routes.rb` - Added reject route
- ✅ `cs.yml` - Added translation

## Known Issues Fixed

1. ✅ **EventCard not removed from DOM** - Fixed by updating REMOVE_EVENT mutation
2. ✅ **Wrong component for styling** - Moved styling from EventCard to CalendarEventItem
3. ✅ **Rejected events visible to managers** - Fixed filtering logic in store getters
4. ✅ **Wrong API endpoint for rejection** - Created proper reject endpoint
5. ✅ **Missing translation** - Added confirm_reject_event translation
6. ✅ **Meeting conflicts include rejected events** - Updated backend controllers

## Performance Considerations

- Event filtering is done in store getters for optimal performance
- CSS uses efficient properties (transform, opacity) instead of expensive box-shadow
- Filtering logic is consistent across all components
- Store mutations are minimal and focused
