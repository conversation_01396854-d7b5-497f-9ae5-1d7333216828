# CO<PERSON><PERSON><PERSON><PERSON>ED TESTING GUIDE - SINGLE SOURCE OF TRUTH

**Purpose**: Unified testing documentation to prevent context overload while maintaining critical information

## 1. TESTING HIERARCHY

### 🚨 SECURITY TESTING (HIGHEST PRIORITY)
**When**: Any task mentioning vulnerability, unauthorized access, permissions
**Required Reading**: 
- `MANDATORY_TESTING_PROTOCOL.md` - Security-specific testing requirements
- `test/support/security-log-monitor.ts` - Automated security log analysis

**Key Requirements**:
1. Test EXACT user flows, not database state
2. Monitor logs in real-time
3. Use `attachSecurityMonitor(page)` in all security tests
4. Verify actual runtime behavior

### 🧪 GENERAL E2E TESTING
**When**: Standard feature testing, UI workflows
**Required Reading**: 
- This guide's E2E section below
- Existing test examples in `test/e2e/`

**Key Requirements**:
1. Use Playwright with auth fixtures
2. Follow user workflows exactly
3. Use data-testid attributes
4. Monitor console for errors

### 🔧 TDD IMPLEMENTATION
**When**: Building new features with TDD approach
**Process**:
1. Write failing Playwright test first
2. Implement minimal code to pass
3. Refactor while keeping tests green
4. Add RSpec unit tests for complex logic

## 2. PLAYWRIGHT E2E TESTING ESSENTIALS

### Authentication Setup
```typescript
import { authenticatedOwner, authenticatedAdmin, authenticatedEmployee } from '../support/auth';

test('feature test', async ({ page }) => {
  await authenticatedOwner(page); // or authenticatedAdmin, authenticatedEmployee
  // Your test here
});
```

### Data Attributes (MANDATORY)
```html
<!-- Always add these for testability -->
<button data-testid="submit-button" data-action="submit">
<div data-vue-component="ContractForm">
<a data-nav-link="dashboard">
```

### Log Monitoring
```typescript
// Always monitor logs for issues
const logs: string[] = [];
page.on('console', msg => logs.push(msg.text()));
page.on('pageerror', error => logs.push(`ERROR: ${error.message}`));
```

## 3. TESTING PATTERNS BY SCENARIO

### Testing Form Submissions
```typescript
test('form submission', async ({ page }) => {
  await page.fill('[data-testid="name-input"]', 'Test Name');
  await page.click('[data-testid="submit-button"]');
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
});
```

### Testing API Interactions
```typescript
// Monitor network for API calls
const apiResponses: any[] = [];
page.on('response', response => {
  if (response.url().includes('/api/v1/')) {
    apiResponses.push({
      url: response.url(),
      status: response.status()
    });
  }
});
```

### Testing Data Display
```typescript
// Verify data appears correctly
await expect(page.locator('[data-testid="employee-list"] tr')).toHaveCount(3);
await expect(page.locator('[data-testid="employee-name"]').first()).toHaveText('John Doe');
```

## 4. COMMON PITFALLS TO AVOID

### ❌ DON'T: Test Database State
```ruby
# WRONG - This doesn't test user experience
user = User.find_by(email: '<EMAIL>')
expect(user.companies).not_to include(unauthorized_company)
```

### ✅ DO: Test Actual User Experience
```typescript
// RIGHT - Test what user actually sees
await page.click('[data-testid="company-switcher"]');
const companies = await page.locator('[data-testid="company-option"]').allTextContents();
expect(companies).not.toContain('Unauthorized Company');
```

### ❌ DON'T: Skip Log Monitoring
```typescript
// WRONG - Missing critical information
test('security test', async ({ page }) => {
  // Test without log monitoring
});
```

### ✅ DO: Always Monitor Logs
```typescript
// RIGHT - Logs reveal security issues
test('security test', async ({ page }) => {
  const securityMonitor = attachSecurityMonitor(page);
  // Your test
  console.log(securityMonitor.generateSecurityReport());
});
```

## 5. DEBUGGING FAILED TESTS

### Step 1: Check Logs
```bash
# During test failure, check Rails logs
tail -f log/test.log | grep -E "ERROR|WARN|Company switch|JWT"
```

### Step 2: Use Playwright Debug Mode
```bash
# Run with headed browser to see what's happening
npm run test:e2e -- --headed --debug
```

### Step 3: Screenshot on Failure
```typescript
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== 'passed') {
    await page.screenshot({ path: `screenshots/${testInfo.title}.png` });
  }
});
```

## 6. CRITICAL REMINDERS

1. **Security Tests**: ALWAYS test the vulnerability scenario exactly as described
2. **User Flows**: Follow the exact path a user would take - no shortcuts
3. **Log Analysis**: Logs often reveal issues tests miss
4. **Real Data**: Test with realistic data scenarios
5. **Error States**: Always test error conditions and edge cases

## 7. WHEN TO LOAD ADDITIONAL DOCS

Load specialized docs only when needed:
- **Security vulnerability** → `MANDATORY_TESTING_PROTOCOL.md`
- **Complex log analysis** → `log-aware_testing_guide.md`
- **First time with Playwright** → `playwright_testing_guide.md`
- **Specific methodology questions** → `CRITICAL_testing_methodology_guidelines.md`

Remember: This guide covers 90% of testing needs. Load additional docs only for specific scenarios.