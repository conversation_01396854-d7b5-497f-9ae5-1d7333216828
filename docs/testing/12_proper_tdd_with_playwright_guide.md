# 🚨 PROPER TDD WITH PLAYWRIGHT - PREVENTING FALSE CONFIDENCE

**⚠️ CRITICAL: This guide addresses the false confidence problem where tests pass but the application is broken ⚠️**

## The False Confidence Problem

### What Happened (Real Example)
```typescript
// ❌ WRONG - This test passed but was USELESS
test('calendar day buttons work', async ({ page }) => {
  const htmlContent = `
  <!DOCTYPE html>
  <html>
  <head>
    <style>
      .day-create-buttons { display: flex; }
      .create-event-btn { background: blue; }
    </style>
  </head>
  <body>
    <div class="calendar-day-cell">
      <div class="day-create-buttons">
        <button class="create-event-btn">Event</button>
      </div>
    </div>
  </body>
  </html>
  `;
  await page.setContent(htmlContent);
  
  // This passed because it tested MOCK HTML, not the real app
  await expect(page.locator('.day-create-buttons')).toBeVisible();
});
```

**Result**: ✅ Test passed, ❌ Real application broken with import errors

### Why This Is Catastrophic
1. **No Server Testing**: Never touched the actual Rails server
2. **No Vue.js Testing**: Never loaded real Vue components  
3. **No Import Validation**: Missed critical LocalizedLink import errors
4. **No API Testing**: Never tested actual endpoints
5. **False Confidence**: "All tests passing, production ready!" while app was broken

## MANDATORY TDD PRINCIPLES

### 1. 🚫 NEVER USE `page.setContent()` FOR FEATURE TESTS

**❌ FORBIDDEN PATTERNS:**
```typescript
// NEVER DO THIS - Creates fake HTML
await page.setContent('<div>Mock content</div>');

// NEVER DO THIS - Tests isolated CSS
await page.setContent(`<style>.my-class { color: red; }</style>`);

// NEVER DO THIS - Mocks Vue components
await page.setContent('<div data-vue-component="fake">Mock</div>');
```

**✅ CORRECT APPROACH:**
```typescript
// ALWAYS DO THIS - Test real application
await page.goto('/calendar');
await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible();
```

### 2. 🎯 TEST REAL USER JOURNEYS

**❌ WRONG - Testing Mock Interactions:**
```typescript
test('button works', async ({ page }) => {
  await page.setContent('<button onclick="alert()">Click</button>');
  await page.click('button'); // Tests nothing real
});
```

**✅ RIGHT - Testing Real Application:**
```typescript
test('calendar day click opens event form', async ({ authenticatedOwner }) => {
  const page = authenticatedOwner.page;
  
  // Navigate to real application
  await page.goto('/calendar');
  
  // Wait for real Vue component to load
  await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible();
  
  // Click real calendar day
  await page.click('[data-testid="calendar-day-15"]');
  
  // Verify real event form opens
  await expect(page.locator('[data-vue-component="event-form"]')).toBeVisible();
  
  // Test real form submission
  await page.fill('[data-testid="event-title"]', 'Test Event');
  await page.click('[data-testid="save-event"]');
  
  // Verify real API call and response
  await expect(page.locator('.flash-success')).toBeVisible();
});
```

### 3. 🔍 VERIFY SERVER CONNECTIVITY

**Every test MUST verify the server is working:**
```typescript
test('feature test', async ({ authenticatedEmployee }) => {
  const page = authenticatedEmployee.page;
  
  // This will fail if server is broken
  await page.goto('/mainbox');
  
  // This will fail if Vue.js has import errors
  await expect(page.locator('[data-vue-component="today-work-section"]')).toBeVisible();
  
  // This will fail if API endpoints are broken
  const response = await page.waitForResponse('/api/v1/works/today');
  expect(response.status()).toBe(200);
});
```

## PROPER TDD WORKFLOW

### Step 1: Write Failing E2E Test First
```typescript
test('user can schedule work on calendar day', async ({ authenticatedAdmin }) => {
  const page = authenticatedAdmin.page;
  
  // Navigate to real calendar
  await page.goto('/calendar');
  
  // This will fail initially - good!
  await page.click('[data-testid="calendar-day-20"]');
  await page.click('[data-testid="create-work-btn"]');
  
  // Fill work form
  await page.fill('[data-testid="work-title"]', 'New Work');
  await page.click('[data-testid="save-work"]');
  
  // Verify work appears on calendar
  await expect(page.locator('[data-testid="work-item"]')).toContainText('New Work');
});
```

### Step 2: Implement Minimal Code
- Add the missing Vue component
- Add the missing API endpoint
- Add the missing database migration
- **Only what's needed to pass the test**

### Step 3: Verify Test Passes
```bash
npm run test:e2e -- --grep "user can schedule work"
```

### Step 4: Refactor While Keeping Tests Green
- Improve code quality
- Add error handling
- Optimize performance
- **Tests must stay green throughout**

## AUTHENTICATION SETUP

Use the existing fixtures - they're already optimized:

```typescript
import { test, expect } from '../fixtures/auth';

// For owner-level features
test('company management', async ({ authenticatedOwner }) => {
  const page = authenticatedOwner.page;
  await page.goto('/companies/settings');
  // Test real company management features
});

// For admin-level features  
test('work assignment', async ({ authenticatedAdmin }) => {
  const page = authenticatedAdmin.page;
  await page.goto('/works/new');
  // Test real work creation
});

// For employee-level features
test('daily workflow', async ({ authenticatedEmployee }) => {
  const page = authenticatedEmployee.page;
  await page.goto('/mainbox');
  // Test real employee dashboard
});
```

## MANDATORY TESTING CHECKLIST

Before any test is considered valid:

### ☐ 1. Tests Real Application
- Uses `page.goto()` to real URLs
- Never uses `page.setContent()`
- Loads actual Vue.js components

### ☐ 2. Tests Server Integration
- Verifies server responds
- Tests real API endpoints
- Validates database interactions

### ☐ 3. Tests Complete User Journey
- Follows actual user workflow
- Tests from start to finish
- No shortcuts or mocks

### ☐ 4. Verifies Real Data Flow
- Tests actual form submissions
- Verifies real API responses
- Checks real database changes

### ☐ 5. Catches Import/Build Errors
- Would fail if components don't load
- Would fail if imports are broken
- Would fail if server crashes

## RED FLAGS - WHEN YOU'RE DOING IT WRONG

### ❌ Danger Phrases in Tests:
- "Let me test this CSS in isolation"
- "I'll create mock HTML for this test"
- "Let me use setContent to test the styling"
- "I'll test the component without the server"

### ❌ Danger Patterns in Code:
```typescript
// RED FLAG: Creating fake HTML
await page.setContent('<div>...</div>');

// RED FLAG: Testing without server
const mockComponent = '<div data-vue-component="fake">Mock</div>';

// RED FLAG: Isolated CSS testing
await page.addStyleTag({ content: '.my-class { color: red; }' });
```

### ✅ Correct Phrases:
- "Let me test the real user workflow"
- "I'll navigate to the actual page"
- "Let me verify the complete journey works"
- "I'll test against the real application"

## RUNNING TESTS PROPERLY

### Development Testing
```bash
# Start the development server first
foreman start -f Procfile.dev

# In another terminal, run tests
npm run test:e2e

# Run specific test
npx playwright test test/e2e/calendar-workflow.spec.ts

# Debug mode (see what's happening)
npx playwright test --headed --debug
```

### Debugging Failed Tests
```bash
# Run with trace for detailed debugging
npx playwright test --trace on

# Generate HTML report
npx playwright show-report
```

## INTEGRATION WITH EXISTING INFRASTRUCTURE

This guide works with your existing setup:
- ✅ Uses existing auth fixtures (`authenticatedOwner`, `authenticatedAdmin`, `authenticatedEmployee`)
- ✅ Uses existing test users (<EMAIL>, etc.)
- ✅ Uses existing base URL (localhost:5100)
- ✅ Follows existing data-testid patterns
- ✅ Integrates with existing Vue.js components

## SUMMARY

**The Golden Rule**: If your test would still pass when the server is completely broken, your test is useless.

**Remember**: The goal of TDD is to drive development of working software, not to create false confidence with mock tests.

## EXAMPLES OF PROPER TDD TESTS

### Example 1: Calendar Feature TDD
```typescript
// test/e2e/calendar-day-interaction.spec.ts
import { test, expect } from '../fixtures/auth';

test.describe('Calendar Day Interaction', () => {
  test('admin can create work by clicking calendar day', async ({ authenticatedAdmin }) => {
    const page = authenticatedAdmin.page;

    // Step 1: Navigate to real calendar
    await page.goto('/calendar');

    // Step 2: Wait for real Vue component to load
    await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible();

    // Step 3: Click on a specific calendar day
    await page.click('[data-testid="calendar-day-15"]');

    // Step 4: Verify day-create-buttons appear (this would fail if CSS/JS broken)
    await expect(page.locator('.day-create-buttons')).toBeVisible();

    // Step 5: Click create work button
    await page.click('[data-testid="create-work-btn"]');

    // Step 6: Verify work form modal opens
    await expect(page.locator('[data-vue-component="work-form"]')).toBeVisible();

    // Step 7: Fill and submit form
    await page.fill('[data-testid="work-title"]', 'Test Work');
    await page.fill('[data-testid="work-description"]', 'Test Description');
    await page.click('[data-testid="save-work"]');

    // Step 8: Verify success and work appears on calendar
    await expect(page.locator('.flash-success')).toBeVisible();
    await expect(page.locator('[data-testid="work-item"]')).toContainText('Test Work');
  });
});
```

### Example 2: Mobile Scheduling TDD
```typescript
// test/e2e/mobile-scheduling.spec.ts
import { test, expect } from '../fixtures/auth';

test.describe('Mobile Scheduling Workflow', () => {
  test('employee can schedule work from mobile panel', async ({ authenticatedEmployee }) => {
    const page = authenticatedEmployee.page;

    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Navigate to calendar
    await page.goto('/calendar');

    // Wait for mobile components to load
    await expect(page.locator('[data-vue-component="monthly-event-table"]')).toBeVisible();
    await expect(page.locator('[data-vue-component="mobile-unscheduled-panel"]')).toBeVisible();

    // Click unscheduled work item
    await page.click('[data-testid="unscheduled-work-item"]');

    // Click calendar day to schedule
    await page.click('[data-testid="calendar-day-20"]');

    // Verify work is scheduled
    await expect(page.locator('[data-testid="scheduled-work-item"]')).toBeVisible();
  });
});
```

## MONITORING AND DEBUGGING

### Log Monitoring During Tests
```typescript
test('feature with log monitoring', async ({ authenticatedOwner }) => {
  const page = authenticatedOwner.page;

  // Capture console logs
  const logs: string[] = [];
  page.on('console', msg => logs.push(`${msg.type()}: ${msg.text()}`));
  page.on('pageerror', error => logs.push(`ERROR: ${error.message}`));

  // Capture network requests
  const apiCalls: any[] = [];
  page.on('response', response => {
    if (response.url().includes('/api/v1/')) {
      apiCalls.push({
        url: response.url(),
        status: response.status(),
        method: response.request().method()
      });
    }
  });

  // Run your test
  await page.goto('/calendar');
  await page.click('[data-testid="some-action"]');

  // Verify no errors in logs
  const errors = logs.filter(log => log.includes('ERROR'));
  expect(errors).toHaveLength(0);

  // Verify expected API calls were made
  expect(apiCalls).toContainEqual(
    expect.objectContaining({
      url: expect.stringContaining('/api/v1/works'),
      status: 200,
      method: 'GET'
    })
  );

  // Print logs for debugging if test fails
  if (errors.length > 0) {
    console.log('Console logs:', logs);
    console.log('API calls:', apiCalls);
  }
});
```

### Screenshot on Failure
```typescript
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status !== 'passed') {
    // Take screenshot
    await page.screenshot({
      path: `test-results/failure-${testInfo.title}-${Date.now()}.png`,
      fullPage: true
    });

    // Save page HTML for debugging
    const html = await page.content();
    require('fs').writeFileSync(
      `test-results/failure-${testInfo.title}-${Date.now()}.html`,
      html
    );
  }
});
```

## PERFORMANCE CONSIDERATIONS

### Fast Test Execution
```typescript
// Use API auth (already configured) - 1 second vs 10+ seconds for UI login
test('fast test', async ({ authenticatedEmployee }) => {
  // Authentication already done via API - super fast
  const page = authenticatedEmployee.page;
  await page.goto('/mainbox');
  // Test continues immediately
});

// Parallel test execution
test.describe.configure({ mode: 'parallel' });
```

### Efficient Waiting
```typescript
// Wait for specific elements, not arbitrary timeouts
await expect(page.locator('[data-vue-component="calendar"]')).toBeVisible({ timeout: 10000 });

// Wait for network requests to complete
await page.waitForResponse('/api/v1/works/today');

// Wait for specific text to appear
await expect(page.locator('text=Work created successfully')).toBeVisible();
```

## CONTINUOUS INTEGRATION

### CI Configuration
```yaml
# .github/workflows/e2e-tests.yml
name: E2E Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Start Rails server
        run: |
          bundle install
          RAILS_ENV=test bundle exec rails db:setup
          RAILS_ENV=test bundle exec rails server -p 5100 &
          sleep 10

      - name: Run Playwright tests
        run: npm run test:e2e

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

---

**Last Updated**: 2025-07-23
**Status**: Mandatory methodology for all TDD
**Next Action**: Apply this methodology to all new feature development
