# DOCUMENTATION CLEANUP NOTES

## Current Testing Documentation Structure

### Primary Guides (Keep)
- `CONSOLIDATED_TESTING_GUIDE.md` - Main testing reference (covers 90% of needs)
- `MANDATORY_TESTING_PROTOCOL.md` - Critical for security testing only

### Specialized Guides (Keep but reference only when needed)
- `playwright_testing_guide.md` - Detailed Playwright setup
- `log-aware_testing_guide.md` - Advanced log monitoring
- `CRITICAL_testing_methodology_guidelines.md` - TDD methodology details

### Historical Context (Archive potential)
- `TYM46_LESSONS_LEARNED.md` - Specific incident analysis
- `BUG_REPORT_invitation_accepted_at_missing.md` - Specific bug report
- `event_state_management_test.md` - Specific feature test
- `missing_data_attributes.md` - Specific issue

### New Routing System (Essential)
- `/docs/MASTER_DOC_INDEX.md` - Routes to all documentation
- `/docs/QUICK_DOC_DECISION_TREE.md` - Visual decision guide
- `/docs/README_DOCUMENTATION_SYSTEM.md` - Explains the system

## Recommended Actions

1. **No immediate deletion** - Keep all docs but use routing system
2. **Future consolidation** - As patterns emerge, merge similar guides
3. **Archive old bugs** - Move resolved bug reports to archive folder after 6 months
4. **Update index** - Keep MASTER_DOC_INDEX.md current as docs change

## The Key Achievement

We've solved the context overload problem without losing information:
- Before: Agent loads random docs, misses critical ones
- After: Agent follows routing, loads exactly what's needed

The TYM-46 failure won't happen again because security tasks now FORCE loading all security documentation.