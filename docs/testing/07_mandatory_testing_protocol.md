# <PERSON><PERSON><PERSON><PERSON>Y TESTING PROTOCOL - CRITICAL SECURITY VULNERABI<PERSON>ITY PREVENTION

**Created**: 2025-07-23  
**Purpose**: Prevent critical testing failures like TYM-46 vulnerability miss

## THE FAILURE THAT CREATED THIS PROTOCOL

Claude Code completely missed a critical security vulnerability (TYM-46) despite:
- Having comprehensive testing documentation
- Clear instructions to follow user workflows
- Explicit guidelines to monitor logs
- TDD principles requirement

**What went wrong**: Static analysis (database queries) instead of dynamic testing (actual user flows)

## MANDATORY TESTING CHECKLIST - NO EXCEPTIONS

### 1. ✅ REPRODUCE THE EXACT USER FLOW
```
WRONG: Analyze database state with Rails console
RIGHT: Click through the exact steps a user would take
```

### 2. ✅ MONITOR LOGS IN REAL-TIME
```
WRONG: Run tests and assume they're comprehensive
RIGHT: tail -f log/development.log during EVERY test execution
```

### 3. ✅ TEST WHAT THE ISSUE DESCRIBES
```
WRONG: Test related functionality
RIGHT: Test the EXACT scenario in the issue description
```

### 4. ✅ VERIFY ACTUAL BEHAVIOR
```
WRONG: Check if CompanyUser<PERSON>ole exists in database
RIGHT: Actually switch companies and see what data is exposed
```

### 5. ✅ TEST BOTH PATHS
```
- Happy path (proper acceptance)
- Vulnerability path (bypassing steps)
```

## SECURITY VULNERABILITY TEST TEMPLATE

```typescript
// ABOUTME: Security test template for vulnerability verification
// ABOUTME: Always test the exact user flow, never make assumptions

test.describe('Security Vulnerability: [ISSUE_ID]', () => {
  test('vulnerability should be reproduced exactly as described', async ({ page }) => {
    // 1. Set up logging
    const logs: string[] = [];
    page.on('console', msg => logs.push(msg.text()));
    
    // 2. Reproduce EXACT steps from issue
    // DO NOT SKIP STEPS OR MAKE ASSUMPTIONS
    
    // 3. Verify actual behavior
    // CHECK WHAT USER ACTUALLY SEES, NOT DATABASE STATE
    
    // 4. Assert on logs
    // LOGS TELL THE TRUTH - READ THEM
  });
  
  test('fix should prevent the vulnerability', async ({ page }) => {
    // Test that the fix actually works
  });
});
```

## LOG MONITORING REQUIREMENTS

### During Development Testing
```bash
# ALWAYS run this in a separate terminal
tail -f log/development.log | grep -E "Company switch|JWT tenant|Unauthorized|ERROR"
```

### In Playwright Tests
```typescript
// MANDATORY: Capture and verify logs
const criticalLogs: string[] = [];
page.on('console', msg => {
  const text = msg.text();
  if (text.includes('Company switch') || 
      text.includes('JWT tenant') ||
      text.includes('Unauthorized')) {
    criticalLogs.push(text);
  }
});

// MANDATORY: Assert on security-relevant logs
expect(criticalLogs.some(log => 
  log.includes('Unauthorized access attempt')
)).toBe(true);
```

## CRITICAL TESTING PRINCIPLES

1. **NEVER TRUST STATIC ANALYSIS**
   - Database state ≠ Runtime behavior
   - Always test actual user experience

2. **LOGS ARE YOUR TRUTH SOURCE**
   - If you're not reading logs, you're not testing
   - Security vulnerabilities often only visible in logs

3. **TEST THE DESCRIBED SCENARIO**
   - Don't test what you think the issue means
   - Test EXACTLY what's described

4. **ASSUME THE VULNERABILITY EXISTS**
   - Start by trying to reproduce it
   - Only conclude it's fixed after failing to exploit it

## IMPLEMENTATION CHECKLIST FOR CLAUDE CODE

Before claiming any security issue is investigated:

- [ ] Created test that reproduces EXACT issue description
- [ ] Monitored logs during test execution
- [ ] Tested actual user flow (not database state)
- [ ] Verified what data user can actually see
- [ ] Tested both proper and improper flows
- [ ] Read ALL relevant logs
- [ ] Actually clicked through the UI myself
- [ ] Confirmed behavior matches issue description

## THE LESSON

The TYM-46 failure happened because:
1. Assumed database state = user experience
2. Didn't monitor logs showing "Company switch: User 129 switched to company 98"
3. Didn't test the actual flow: invitation link → login → switch company
4. Made conclusions without reproducing the issue

**NEVER AGAIN**

This protocol exists because hours of documentation were ignored. Follow it.