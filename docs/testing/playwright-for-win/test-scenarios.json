{"testSuites": {"authentication": {"priority": "critical", "scenarios": [{"name": "user_login_success", "description": "User logs in with valid credentials", "steps": ["Navigate to login page", "Enter valid email and password", "Click login button", "Verify redirect to dashboard", "Verify JWT token stored", "Verify user data loaded"], "testData": {"email": "<EMAIL>", "password": "password123"}, "expectedResults": ["User redirected to dashboard", "JWT token present in storage", "User menu shows correct name"], "selectors": {"emailInput": "[data-testid='login-email-input']", "passwordInput": "[data-testid='login-password-input']", "submitButton": "[data-testid='login-submit-button']", "dashboard": "[data-testid='nav-dashboard']"}}, {"name": "user_login_invalid_credentials", "description": "User attempts login with invalid credentials", "steps": ["Navigate to login page", "Enter invalid email/password", "Click login button", "Verify error message displayed", "Verify user remains on login page"], "testData": {"email": "<EMAIL>", "password": "wrongpassword"}, "expectedResults": ["Error message displayed", "User remains on login page", "No JWT token stored"]}, {"name": "user_registration_success", "description": "New user registers successfully", "steps": ["Navigate to registration page", "Fill registration form", "Submit form", "Verify confirmation message", "Check email confirmation required"], "testData": {"email": "<EMAIL>", "password": "password123", "passwordConfirmation": "password123"}}, {"name": "password_reset_flow", "description": "User resets forgotten password", "steps": ["Navigate to forgot password page", "Enter email address", "Submit request", "Verify confirmation message", "Check email sent"]}, {"name": "session_restoration", "description": "User session restored on page refresh", "steps": ["<PERSON><PERSON> successfully", "Refresh page", "Verify user still authenticated", "Verify data still loaded"]}, {"name": "logout_success", "description": "User logs out successfully", "steps": ["<PERSON><PERSON> successfully", "Click logout button", "Verify redirect to login page", "Verify JWT token cleared", "Verify session ended"]}]}, "timeTracking": {"priority": "high", "scenarios": [{"name": "start_daily_log", "description": "User starts daily time tracking", "steps": ["Navigate to mainbox/dashboard", "Click start tracking button", "Verify daily log created", "Verify timer started", "Verify UI shows active state"], "selectors": {"startButton": "[data-action='start-tracking']", "activeTimer": "[data-testid='active-timer']", "dailyLogStatus": "[data-testid='daily-log-status']"}, "apiEndpoints": ["POST /api/v1/daily_logs/start"]}, {"name": "track_activity", "description": "User tracks work activity", "steps": ["Start daily log", "Select work activity", "Start activity tracking", "Verify activity timer running", "Stop activity", "Verify activity logged"]}, {"name": "take_break", "description": "User takes a break", "steps": ["Start daily log", "Click break button", "Verify break timer started", "End break", "Verify break time logged"]}, {"name": "end_daily_log", "description": "User ends daily tracking", "steps": ["Start daily log", "Track some activities", "Click end tracking", "Verify summary displayed", "Confirm end tracking", "Verify log completed"]}]}, "eventManagement": {"priority": "high", "scenarios": [{"name": "create_vacation_event", "description": "User creates vacation event", "steps": ["Navigate to events/calendar", "Click add event button", "Select vacation type", "Set date range", "Add description", "Submit form", "Verify event created"], "testData": {"eventType": "vacation", "startDate": "2024-08-01", "endDate": "2024-08-05", "description": "Summer vacation"}, "selectors": {"addEventButton": "[data-action='add-event']", "eventTypeSelect": "[data-testid='event-type-select']", "startDateInput": "[data-testid='start-date-input']", "endDateInput": "[data-testid='end-date-input']", "submitButton": "[data-testid='submit-event-form']"}}, {"name": "create_illness_event", "description": "User reports illness", "steps": ["Open event form", "Select illness type", "Set date", "Submit form", "Verify event created with proper status"]}, {"name": "schedule_meeting", "description": "User schedules team meeting", "steps": ["Open meeting form", "Enter meeting title", "Select participants", "Choose available dates", "Set time slots", "Send invitations", "Verify meeting created"], "testData": {"title": "Team Standup", "description": "Weekly team meeting", "participants": ["<EMAIL>", "<EMAIL>"]}}, {"name": "reschedule_event", "description": "User reschedules existing event", "steps": ["Navigate to calendar", "Click existing event", "Click edit button", "Change date/time", "Save changes", "Verify event updated"]}]}, "workManagement": {"priority": "high", "scenarios": [{"name": "create_work_item", "description": "User creates new work assignment", "steps": ["Navigate to works section", "Click add work button", "Fill work details", "Assign team members", "Set schedule", "Submit form", "Verify work created"], "testData": {"title": "Fix heating system", "description": "Repair heating in office building", "location": "Main Office", "assignees": ["technician1", "technician2"]}, "selectors": {"addWorkButton": "[data-action='add-work']", "titleInput": "[data-testid='work-title-input']", "descriptionInput": "[data-testid='work-description-input']", "locationInput": "[data-testid='work-location-input']", "submitButton": "[data-testid='submit-work-form']"}}, {"name": "assign_work_to_team", "description": "Manager assigns work to team members", "steps": ["Create work item", "Open assignment section", "Select team members", "Set individual schedules", "Save assignments", "Verify notifications sent"]}, {"name": "update_work_status", "description": "Worker updates work progress", "steps": ["Navigate to assigned work", "Open work details", "Update status", "Add progress notes", "Save changes", "Verify status updated"]}, {"name": "complete_work_item", "description": "Worker marks work as completed", "steps": ["Open work item", "Click complete button", "Add completion notes", "Confirm completion", "Verify work marked complete", "Verify time tracking closed"]}]}, "companyManagement": {"priority": "medium", "scenarios": [{"name": "switch_company", "description": "User switches between companies", "steps": ["Login to multi-tenant account", "Open company selector", "Select different company", "Verify company switched", "Verify data updated for new company"], "apiEndpoints": ["POST /api/v1/companies/switch_company"]}, {"name": "manage_team_members", "description": "Admin manages team contracts", "steps": ["Navigate to contracts section", "View team members list", "Add new team member", "Set role and permissions", "Send invitation", "Verify member added"]}, {"name": "update_company_settings", "description": "Admin updates company settings", "steps": ["Navigate to company settings", "Update break duration", "Change timezone", "Toggle auto-break setting", "Save changes", "Verify settings applied"]}]}, "bookingSystem": {"priority": "medium", "scenarios": [{"name": "create_booking_link", "description": "User creates public booking link", "steps": ["Navigate to bookings section", "Click create booking link", "Set availability", "Configure booking options", "Generate link", "Verify link created"]}, {"name": "public_booking_flow", "description": "External client makes booking", "steps": ["Visit public booking link", "Select available time", "Fill contact details", "Submit booking request", "Verify confirmation message", "Check booking created in system"]}, {"name": "confirm_booking", "description": "User confirms client booking", "steps": ["View pending bookings", "Open booking details", "Set confirmed time", "Send confirmation", "Verify booking confirmed", "Check client notification sent"]}]}}, "errorScenarios": {"networkErrors": ["API endpoint unavailable", "Slow network response", "Connection timeout", "Invalid JSON response"], "validationErrors": ["Empty required fields", "Invalid email format", "Password too short", "Date in past", "Overlapping time slots"], "authorizationErrors": ["Expired JWT token", "Insufficient permissions", "Company access denied", "Resource not found"]}, "performanceScenarios": ["Load large calendar date range", "Handle 100+ team members", "Process multiple concurrent bookings", "Real-time updates with WebSocket", "File upload/download operations"]}