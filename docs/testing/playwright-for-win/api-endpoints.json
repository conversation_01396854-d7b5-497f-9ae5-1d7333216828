{"apiVersion": "v1", "baseUrl": "/api/v1", "authentication": {"type": "JWT", "header": "Authorization", "format": "Bearer {token}", "refreshEndpoint": "/api/v1/auth/refresh_token"}, "endpoints": {"authentication": {"login": {"method": "POST", "path": "/auth/jwt_login", "description": "Authenticate user with email and password", "requiresAuth": false, "requestFormat": {"email": "string (required)", "password": "string (required)"}, "responseFormat": {"success": "boolean", "user": {"email": "string", "role": "string", "roles": "array", "permissions": "object"}, "token": "string", "refresh_token": "string (httpOnly cookie)"}, "errorCodes": {"401": "Invalid credentials", "403": "<PERSON><PERSON> not confirmed", "422": "Validation errors"}, "testData": {"valid": {"email": "<EMAIL>", "password": "password123"}, "invalid": {"email": "<EMAIL>", "password": "wrongpassword"}}}, "register": {"method": "POST", "path": "/auth/jwt_register", "description": "Register new user account", "requiresAuth": false, "requestFormat": {"email": "string (required)", "password": "string (required)", "password_confirmation": "string (required)"}, "responseFormat": {"success": "boolean", "user": "object", "message": "string"}, "errorCodes": {"422": "Validation errors (email taken, password mismatch, etc.)"}}, "logout": {"method": "POST", "path": "/auth/jwt_logout", "description": "Logout user and revoke tokens", "requiresAuth": true, "responseFormat": {"success": "boolean", "message": "string"}}, "refreshToken": {"method": "POST", "path": "/auth/refresh_token", "description": "Refresh access token using refresh token", "requiresAuth": false, "responseFormat": {"token": "string", "expires_at": "datetime"}, "errorCodes": {"401": "Invalid or expired refresh token"}}, "passwordReset": {"method": "POST", "path": "/auth/password_reset", "description": "Reset user password with token", "requiresAuth": false, "requestFormat": {"token": "string (required)", "password": "string (required)", "password_confirmation": "string (required)"}}, "requestPasswordReset": {"method": "POST", "path": "/auth/request_password_reset", "description": "Request password reset email", "requiresAuth": false, "requestFormat": {"email": "string (required)"}}}, "user": {"getCurrentUser": {"method": "GET", "path": "/user", "description": "Get current authenticated user data", "requiresAuth": true, "responseFormat": {"email": "string", "role": "string", "roles": "array", "permissions": "object", "company": "object", "has_plus_plan": "boolean", "has_premium_plan": "boolean"}, "errorCodes": {"401": "Not authenticated", "403": "Access denied"}}, "getEmployees": {"method": "GET", "path": "/employees", "description": "Get company employees list", "requiresAuth": true, "responseFormat": {"employees": "array of employee objects"}}, "getSubscriptionStatus": {"method": "GET", "path": "/subscription_status", "description": "Get current subscription information", "requiresAuth": true, "responseFormat": {"current_plan": "string", "available_features": "array"}}}, "companies": {"switchCompany": {"method": "POST", "path": "/companies/switch_company", "description": "Switch to different company (multi-tenant)", "requiresAuth": true, "requestFormat": {"company_id": "integer (required)"}, "responseFormat": {"success": "boolean", "company": "object", "message": "string"}, "errorCodes": {"403": "Access denied to company", "404": "Company not found"}}}, "dailyLogs": {"index": {"method": "GET", "path": "/daily_logs", "description": "Get daily logs for current user", "requiresAuth": true, "queryParams": {"date": "string (optional, YYYY-MM-DD)", "limit": "integer (optional, default 25)"}, "responseFormat": {"daily_logs": "array of daily log objects"}}, "create": {"method": "POST", "path": "/daily_logs", "description": "Create new daily log", "requiresAuth": true, "requestFormat": {"date": "string (YYYY-MM-DD)", "start_time": "string (HH:MM)", "notes": "string (optional)"}, "responseFormat": {"daily_log": "object", "success": "boolean"}}, "start": {"method": "POST", "path": "/daily_logs/start", "description": "Start time tracking for today", "requiresAuth": true, "responseFormat": {"daily_log": "object", "started_at": "datetime"}}, "finish": {"method": "POST", "path": "/daily_logs/finish", "description": "End time tracking for today", "requiresAuth": true, "requestFormat": {"end_time": "string (HH:MM)", "notes": "string (optional)"}, "responseFormat": {"daily_log": "object", "finished_at": "datetime"}}}, "events": {"index": {"method": "GET", "path": "/events", "description": "Get calendar events", "requiresAuth": true, "queryParams": {"start_date": "string (YYYY-MM-DD)", "end_date": "string (YYYY-MM-DD)", "event_type": "string (optional)"}, "responseFormat": {"events": "array of event objects"}}, "create": {"method": "POST", "path": "/events", "description": "Create new calendar event", "requiresAuth": true, "requestFormat": {"event_type": "string (required: vacation, illness, travel, etc.)", "start_date": "string (YYYY-MM-DD)", "end_date": "string (YYYY-MM-DD)", "start_time": "string (HH:MM, optional)", "end_time": "string (HH:MM, optional)", "place": "string (optional)", "description": "string (optional)"}, "responseFormat": {"event": "object", "success": "boolean"}, "errorCodes": {"422": "Validation errors (invalid dates, overlapping events)"}}, "update": {"method": "PATCH", "path": "/events/{id}", "description": "Update existing event", "requiresAuth": true, "requestFormat": "Same as create", "errorCodes": {"404": "Event not found", "403": "Not authorized to update event"}}, "destroy": {"method": "DELETE", "path": "/events/{id}", "description": "Delete event", "requiresAuth": true, "errorCodes": {"404": "Event not found", "403": "Not authorized to delete event"}}}, "meetings": {"index": {"method": "GET", "path": "/meetings", "description": "Get meetings list", "requiresAuth": true, "responseFormat": {"meetings": "array of meeting objects"}}, "create": {"method": "POST", "path": "/meetings", "description": "Create new meeting", "requiresAuth": true, "requestFormat": {"title": "string (required)", "description": "string (optional)", "place": "string (optional)", "participant_contracts": "array of contract IDs", "additional_emails": "array of email strings", "day_options": "object with date/time options"}, "responseFormat": {"meeting": "object", "success": "boolean"}}, "show": {"method": "GET", "path": "/meetings/{id}", "description": "Get meeting details", "requiresAuth": true, "responseFormat": {"meeting": "object with participants and options"}}, "update": {"method": "PATCH", "path": "/meetings/{id}", "description": "Update meeting", "requiresAuth": true}, "destroy": {"method": "DELETE", "path": "/meetings/{id}", "description": "Delete meeting", "requiresAuth": true}}, "works": {"index": {"method": "GET", "path": "/works", "description": "Get work items list", "requiresAuth": true, "responseFormat": {"works": "array of work objects with assignments"}}, "create": {"method": "POST", "path": "/works", "description": "Create new work item", "requiresAuth": true, "requestFormat": {"title": "string (required)", "description": "string (optional)", "location": "string (optional)", "work_type": "string (optional)", "status": "string (default: unprocessed)", "scheduled_start_date": "string (YYYY-MM-DD)", "scheduled_end_date": "string (YYYY-MM-DD)", "confirmed_time": "string (HH:MM, optional)", "assigned_contracts": "array of contract IDs", "service_contract_id": "integer (optional)"}, "responseFormat": {"work": "object", "success": "boolean"}}, "show": {"method": "GET", "path": "/works/{id}", "description": "Get work item details", "requiresAuth": true}, "update": {"method": "PATCH", "path": "/works/{id}", "description": "Update work item", "requiresAuth": true}, "destroy": {"method": "DELETE", "path": "/works/{id}", "description": "Delete work item", "requiresAuth": true}}, "bookings": {"index": {"method": "GET", "path": "/bookings", "description": "Get bookings list", "requiresAuth": true, "responseFormat": {"bookings": "array of booking objects"}}, "create": {"method": "POST", "path": "/bookings", "description": "Create new booking", "requiresAuth": true, "requestFormat": {"client_name": "string (required)", "client_email": "string (required)", "client_phone": "string (optional)", "preferred_date": "string (YYYY-MM-DD)", "preferred_period": "string (morning/afternoon/evening)", "message": "string (optional)"}}, "show": {"method": "GET", "path": "/bookings/{id}", "description": "Get booking details", "requiresAuth": true}, "update": {"method": "PATCH", "path": "/bookings/{id}", "description": "Update booking (confirm, reschedule, etc.)", "requiresAuth": true}}, "contracts": {"fetch": {"method": "GET", "path": "/contracts/fetch", "description": "Get contracts list", "requiresAuth": true, "responseFormat": {"contracts": "array of contract objects"}}, "colleagues": {"method": "GET", "path": "/contracts/colleagues", "description": "Get colleague contracts", "requiresAuth": true, "responseFormat": {"colleagues": "array", "current_user_contract_id": "integer"}}}}, "errorHandling": {"commonErrors": {"400": "Bad Request - Invalid request format", "401": "Unauthorized - Invalid or missing JWT token", "403": "Forbidden - Insufficient permissions", "404": "Not Found - Resource doesn't exist", "422": "Unprocessable Entity - Validation errors", "500": "Internal Server Error - Server error"}, "errorResponseFormat": {"error": "string (error message)", "errors": "object (field-specific validation errors)", "message": "string (user-friendly message)"}}, "testingConsiderations": {"authenticationFlow": ["Test with valid JWT token", "Test with expired JWT token", "Test with invalid JWT token", "Test with missing Authorization header", "Test token refresh flow"], "dataValidation": ["Test required field validation", "Test data type validation", "Test business rule validation", "Test boundary conditions", "Test special characters"], "tenantIsolation": ["Verify company-scoped data access", "Test cross-company data leakage", "Verify permission-based access", "Test company switching"], "performanceConsiderations": ["Large date range queries", "Pagination handling", "Concurrent request handling", "Rate limiting behavior"]}}