# Playwright Setup Guide for Attendify App

## Prerequisites

### System Requirements
- **Node.js**: Version 18+ (LTS recommended)
- **npm/yarn**: Latest version
- **Git**: For version control
- **Ruby**: 3.1+ (for Rails backend)
- **PostgreSQL**: 13+ (for database)

### Development Environment
- **VS Code**: Recommended IDE with Playwright extension
- **Chrome/Chromium**: For browser testing
- **Firefox**: For cross-browser testing
- **Safari**: For macOS testing (if applicable)

## Installation Steps

### 1. Install Playwright
```bash
# Navigate to project root
cd /path/to/attendifyapp

# Install Playwright
npm init playwright@latest

# Or if using yarn
yarn create playwright

# Install browsers
npx playwright install

# Install system dependencies (Linux/macOS)
npx playwright install-deps
```

### 2. Project Structure Setup
```bash
# Create test directory structure
mkdir -p tests/e2e
mkdir -p tests/api
mkdir -p tests/fixtures
mkdir -p tests/page-objects
mkdir -p tests/utils
mkdir -p tests/screenshots
mkdir -p tests/reports
```

### 3. Configuration Files

#### playwright.config.js
```javascript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'tests/reports/results.json' }],
    ['junit', { outputFile: 'tests/reports/results.xml' }]
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    actionTimeout: 10000,
    navigationTimeout: 30000
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
});
```

#### .env.test
```bash
# Test environment configuration
BASE_URL=http://localhost:3000
API_BASE_URL=http://localhost:3000/api/v1

# Test database
DATABASE_URL=postgresql://test_user:test_password@localhost:5432/attendify_test

# Test user credentials
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=TestPassword123!
TEST_MANAGER_EMAIL=<EMAIL>
TEST_MANAGER_PASSWORD=TestPassword123!
TEST_EMPLOYEE_EMAIL=<EMAIL>
TEST_EMPLOYEE_PASSWORD=TestPassword123!

# JWT configuration for tests
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRATION=1h

# Feature flags for testing
ENABLE_DEBUG_LOGS=true
SKIP_EMAIL_VERIFICATION=true
ALLOW_TEST_DATA_CREATION=true
```

### 4. Page Object Models

#### Base Page Object
```javascript
// tests/page-objects/BasePage.js
export class BasePage {
  constructor(page) {
    this.page = page;
  }

  async goto(path = '') {
    await this.page.goto(`${process.env.BASE_URL}${path}`);
  }

  async waitForLoadState() {
    await this.page.waitForLoadState('networkidle');
  }

  async takeScreenshot(name) {
    await this.page.screenshot({ 
      path: `tests/screenshots/${name}.png`,
      fullPage: true 
    });
  }

  async waitForSelector(selector, options = {}) {
    return await this.page.waitForSelector(selector, {
      timeout: 10000,
      ...options
    });
  }

  async clickAndWait(selector, waitForSelector = null) {
    await this.page.click(selector);
    if (waitForSelector) {
      await this.waitForSelector(waitForSelector);
    }
  }
}
```

#### Authentication Page Object
```javascript
// tests/page-objects/AuthPage.js
import { BasePage } from './BasePage.js';

export class AuthPage extends BasePage {
  constructor(page) {
    super(page);
    this.emailInput = '[data-testid="login-email-input"]';
    this.passwordInput = '[data-testid="login-password-input"]';
    this.loginButton = '[data-testid="login-submit-button"]';
    this.registerButton = '[data-testid="register-submit-button"]';
    this.errorMessage = '[data-testid="error-message"]';
    this.successMessage = '[data-testid="success-message"]';
  }

  async login(email, password) {
    await this.goto('/cs/users/sign_in');
    await this.page.fill(this.emailInput, email);
    await this.page.fill(this.passwordInput, password);
    await this.clickAndWait(this.loginButton, '[data-testid="nav-dashboard"]');
  }

  async logout() {
    await this.page.click('[data-action="logout"]');
    await this.waitForSelector(this.emailInput);
  }

  async register(email, password, passwordConfirmation) {
    await this.goto('/cs/users/sign_up');
    await this.page.fill('[data-testid="register-email-input"]', email);
    await this.page.fill('[data-testid="register-password-input"]', password);
    await this.page.fill('[data-testid="register-password-confirm-input"]', passwordConfirmation);
    await this.clickAndWait(this.registerButton);
  }

  async getErrorMessage() {
    return await this.page.textContent(this.errorMessage);
  }

  async isLoggedIn() {
    try {
      await this.waitForSelector('[data-testid="user-menu"]', { timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}
```

### 5. Test Utilities

#### API Helper
```javascript
// tests/utils/apiHelper.js
export class ApiHelper {
  constructor(baseURL = process.env.API_BASE_URL) {
    this.baseURL = baseURL;
    this.token = null;
  }

  async login(email, password) {
    const response = await fetch(`${this.baseURL}/auth/jwt_login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    const data = await response.json();
    if (data.token) {
      this.token = data.token;
    }
    return data;
  }

  async request(endpoint, options = {}) {
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers
    });

    return response.json();
  }

  async createTestUser(userData) {
    return await this.request('/auth/jwt_register', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
  }

  async createTestEvent(eventData) {
    return await this.request('/events', {
      method: 'POST',
      body: JSON.stringify(eventData)
    });
  }

  async createTestWork(workData) {
    return await this.request('/works', {
      method: 'POST',
      body: JSON.stringify(workData)
    });
  }
}
```

#### Test Data Helper
```javascript
// tests/utils/testDataHelper.js
import testData from '../fixtures/sample-test-data.json';

export class TestDataHelper {
  static getUser(role = 'employee') {
    return testData.users[role] || testData.users.employee;
  }

  static getCompany(type = 'smallBusiness') {
    return testData.companies[type] || testData.companies.smallBusiness;
  }

  static getEvent(type = 'vacation') {
    return testData.events[type] || testData.events.vacation;
  }

  static getWork(type = 'hvacMaintenance') {
    return testData.works[type] || testData.works.hvacMaintenance;
  }

  static generateUniqueEmail(prefix = 'test') {
    const timestamp = Date.now();
    return `${prefix}+${timestamp}@test.com`;
  }

  static generateRandomString(length = 10) {
    return Math.random().toString(36).substring(2, length + 2);
  }

  static getFutureDate(daysFromNow = 7) {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date.toISOString().split('T')[0];
  }
}
```

### 6. Sample Test Files

#### Authentication Tests
```javascript
// tests/e2e/auth.spec.js
import { test, expect } from '@playwright/test';
import { AuthPage } from '../page-objects/AuthPage.js';
import { TestDataHelper } from '../utils/testDataHelper.js';

test.describe('Authentication', () => {
  let authPage;

  test.beforeEach(async ({ page }) => {
    authPage = new AuthPage(page);
  });

  test('should login with valid credentials', async () => {
    const user = TestDataHelper.getUser('employee');
    
    await authPage.login(user.email, user.password);
    
    expect(await authPage.isLoggedIn()).toBe(true);
    await expect(authPage.page).toHaveURL(/.*dashboard/);
  });

  test('should show error for invalid credentials', async () => {
    await authPage.login('<EMAIL>', 'wrongpassword');
    
    const errorMessage = await authPage.getErrorMessage();
    expect(errorMessage).toContain('Invalid credentials');
  });

  test('should logout successfully', async () => {
    const user = TestDataHelper.getUser('employee');
    
    await authPage.login(user.email, user.password);
    await authPage.logout();
    
    expect(await authPage.isLoggedIn()).toBe(false);
    await expect(authPage.page).toHaveURL(/.*sign_in/);
  });
});
```

### 7. Running Tests

#### Basic Commands
```bash
# Run all tests
npx playwright test

# Run specific test file
npx playwright test tests/e2e/auth.spec.js

# Run tests in headed mode (visible browser)
npx playwright test --headed

# Run tests in specific browser
npx playwright test --project=chromium

# Run tests with debug mode
npx playwright test --debug

# Generate test report
npx playwright show-report
```

#### CI/CD Integration
```yaml
# .github/workflows/playwright.yml
name: Playwright Tests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: 18
    - name: Install dependencies
      run: npm ci
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps
    - name: Run Playwright tests
      run: npx playwright test
    - uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30
```

### 8. Best Practices

#### Test Organization
- Group related tests in describe blocks
- Use descriptive test names
- Keep tests independent and isolated
- Use proper setup and teardown

#### Data Management
- Use test fixtures for consistent data
- Clean up test data after tests
- Use unique identifiers to avoid conflicts
- Separate test data from production data

#### Error Handling
- Use proper assertions and expectations
- Handle async operations correctly
- Add meaningful error messages
- Use retry mechanisms for flaky tests

#### Performance
- Run tests in parallel when possible
- Use page object models for reusability
- Optimize selectors for speed
- Use appropriate timeouts

### 9. Troubleshooting

#### Common Issues
- **Browser not found**: Run `npx playwright install`
- **Timeout errors**: Increase timeout values or improve selectors
- **Flaky tests**: Add proper waits and retry mechanisms
- **Authentication issues**: Verify JWT token handling

#### Debug Tools
- Use `--debug` flag for step-by-step debugging
- Add `await page.pause()` for manual inspection
- Use browser developer tools
- Check network requests and responses

### 10. Next Steps

1. **Setup Development Environment**: Install all dependencies
2. **Create Initial Tests**: Start with authentication flows
3. **Implement Page Objects**: Build reusable page models
4. **Add API Tests**: Validate backend endpoints
5. **Setup CI/CD**: Automate test execution
6. **Monitor and Maintain**: Regular test maintenance and updates
