{"users": {"companyOwner": {"email": "<EMAIL>", "password": "SecureOwnerPass123!", "firstName": "<PERSON>", "lastName": "Owner", "role": "owner", "permissions": ["manage_company", "manage_employees", "manage_works", "view_all_reports", "manage_bookings", "manage_contracts"], "phone": "+420123456789", "timezone": "Europe/Prague"}, "manager": {"email": "<EMAIL>", "password": "ManagerPass123!", "firstName": "<PERSON>", "lastName": "Manager", "role": "manager", "permissions": ["manage_works", "view_team_reports", "manage_team_schedule", "approve_events"], "phone": "+420987654321", "timezone": "Europe/Prague"}, "employee": {"email": "<EMAIL>", "password": "EmployeePass123!", "firstName": "<PERSON>", "lastName": "Worker", "role": "employee", "permissions": ["track_time", "view_own_data", "create_events", "view_assigned_works"], "phone": "+420555123456", "timezone": "Europe/Prague"}, "technician": {"email": "<EMAIL>", "password": "TechPass123!", "firstName": "<PERSON>", "lastName": "Technician", "role": "employee", "permissions": ["track_time", "view_own_data", "update_work_status"], "specialization": "HVAC", "phone": "+420777888999"}}, "companies": {"smallBusiness": {"name": "Small Business Ltd.", "address": "123 Main Street, Prague 1, 110 00, Czech Republic", "phone": "+420123456789", "web": "https://smallbusiness.cz", "description": "Small family business providing HVAC services", "settings": {"break_duration": 30, "auto_break": true, "auto_end": false, "allow_overtime": true, "timezone": "Europe/Prague", "approve_vacations": false, "daily_team_reports": false}, "plan": "basic", "employees_count": 5}, "enterpriseCorp": {"name": "Enterprise Corp s.r.o.", "address": "456 Business Avenue, Bratislava 811 01, Slovakia", "phone": "+421987654321", "web": "https://enterprise.sk", "description": "Large enterprise providing comprehensive facility management", "settings": {"break_duration": 45, "auto_break": false, "auto_end": true, "allow_overtime": false, "timezone": "Europe/Bratislava", "approve_vacations": true, "daily_team_reports": true}, "plan": "premium", "employees_count": 50}}, "events": {"vacation": {"event_type": "vacation", "start_date": "2024-08-15", "end_date": "2024-08-20", "description": "Summer vacation in Croatia - family trip", "place": "Dubrovnik, Croatia", "all_day": true, "status": "pending_approval"}, "illness": {"event_type": "illness", "start_date": "2024-07-25", "end_date": "2024-07-25", "description": "Flu symptoms - doctor recommended rest", "place": "Home", "all_day": true, "status": "approved"}, "businessTravel": {"event_type": "travel", "start_date": "2024-09-10", "end_date": "2024-09-12", "start_time": "08:00", "end_time": "18:00", "description": "Client meeting and system installation in Vienna", "place": "Vienna, Austria - Client Office", "all_day": false, "status": "approved"}, "doctorVisit": {"event_type": "day_care", "start_date": "2024-08-05", "end_date": "2024-08-05", "start_time": "14:00", "end_time": "16:00", "description": "Annual health checkup", "place": "Medical Center Prague", "all_day": false, "status": "approved"}, "familySick": {"event_type": "family_sick", "start_date": "2024-07-30", "end_date": "2024-07-31", "description": "Caring for sick child", "place": "Home", "all_day": true, "status": "approved"}}, "works": {"hvacMaintenance": {"title": "HVAC System Annual Maintenance", "description": "Complete maintenance of heating and cooling systems including filter replacement, system cleaning, and performance testing", "location": "Office Building A, Floor 3, Room 301", "work_type": "maintenance", "status": "scheduled", "scheduled_start_date": "2024-08-01", "scheduled_end_date": "2024-08-01", "confirmed_time": "09:00", "estimated_duration": 240, "priority": "normal", "client_contact": "Building Manager - +420111222333", "special_instructions": "Building access code: 1234. Contact security before entering."}, "emergencyRepair": {"title": "Emergency Plumbing Repair - <PERSON><PERSON>t Pipe", "description": "Urgent repair of burst pipe in basement causing water damage. Immediate response required.", "location": "Main Office Building, Basement Level B1", "work_type": "emergency", "status": "urgent", "scheduled_start_date": "2024-07-23", "confirmed_time": "14:30", "estimated_duration": 120, "priority": "urgent", "client_contact": "Facility Manager - +420999888777", "special_instructions": "Water main shutoff required. Bring emergency repair kit."}, "installationProject": {"title": "New Office HVAC Installation", "description": "Complete installation of new HVAC system for newly renovated office space", "location": "New Office Complex, Building C, Floors 1-3", "work_type": "installation", "status": "in_progress", "scheduled_start_date": "2024-08-10", "scheduled_end_date": "2024-08-15", "confirmed_time": "08:00", "estimated_duration": 2400, "priority": "high", "client_contact": "Project Manager - +420444555666", "special_instructions": "Multi-day project. Coordinate with construction team."}, "routineInspection": {"title": "Monthly Safety Inspection", "description": "Routine monthly inspection of all HVAC systems and safety equipment", "location": "Various locations - see checklist", "work_type": "inspection", "status": "unprocessed", "scheduled_start_date": "2024-08-30", "confirmed_time": "10:00", "estimated_duration": 180, "priority": "normal", "special_instructions": "Complete inspection checklist for each location."}}, "meetings": {"teamStandup": {"title": "Weekly Team Standup", "description": "Weekly team meeting to discuss progress, upcoming work, and any issues", "place": "Conference Room A", "participants": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "additional_emails": [], "day_options": {"2024-08-05": {"09:00": true, "10:00": true, "11:00": true}, "2024-08-06": {"09:00": true, "10:00": true}}, "duration": 60, "recurring": "weekly"}, "clientMeeting": {"title": "Client Project Review", "description": "Review project progress with client and discuss next phases", "place": "Client Office - Vienna", "participants": ["<EMAIL>"], "additional_emails": ["<EMAIL>", "<EMAIL>"], "day_options": {"2024-09-15": {"14:00": true, "15:00": true, "16:00": true}, "2024-09-16": {"09:00": true, "10:00": true, "11:00": true}}, "duration": 120}}, "bookings": {"hvacConsultation": {"client_name": "<PERSON>", "client_email": "<EMAIL>", "client_phone": "+420777123456", "preferred_date": "2024-08-20", "preferred_period": "morning", "message": "Need consultation for new HVAC system in our office. Building is 200 sqm, 2 floors.", "service_type": "consultation", "status": "pending", "estimated_duration": 90}, "emergencyCall": {"client_name": "<PERSON>", "client_email": "<EMAIL>", "client_phone": "+420888999000", "preferred_date": "2024-07-25", "preferred_period": "afternoon", "message": "Heating system not working. Office temperature too cold for employees.", "service_type": "emergency", "status": "confirmed", "confirmed_time": "14:00", "estimated_duration": 120}, "maintenanceBooking": {"client_name": "<PERSON><PERSON>", "client_email": "<EMAIL>", "client_phone": "+421777888999", "preferred_date": "2024-09-01", "preferred_period": "morning", "message": "Annual maintenance for our office building HVAC systems. 3 units need servicing.", "service_type": "maintenance", "status": "pending", "estimated_duration": 240}}, "serviceContracts": {"annualMaintenance": {"title": "Annual HVAC Maintenance Contract 2024", "description": "Comprehensive annual maintenance contract covering all HVAC systems", "status": "active", "client_name": "Office Complex Management", "start_date": "2024-01-01", "end_date": "2024-12-31", "value": 50000, "currency": "CZK"}, "emergencySupport": {"title": "24/7 Emergency Support Contract", "description": "Round-the-clock emergency support for critical HVAC systems", "status": "active", "client_name": "Hospital Facility Management", "start_date": "2024-01-01", "end_date": "2025-12-31", "value": 120000, "currency": "CZK"}}, "edgeCases": {"invalidEmails": ["invalid-email", "@domain.com", "user@", "<EMAIL>", "user@domain", ""], "invalidPhones": ["123", "abc-def-ghij", "+420", "++420123456789", "420-123-456-789-000"], "invalidDates": ["2024-02-30", "2024-13-01", "2023-12-32", "invalid-date", ""], "invalidTimes": ["25:00", "12:60", "ab:cd", "12:30:45:67", ""], "specialCharacters": {"names": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>小明", "<PERSON><PERSON>م<PERSON> العر<PERSON>ي"], "descriptions": ["Special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?", "Unicode: 🔧⚡🏢❄️🔥", "HTML: <script>alert('test')</script>", "SQL: '; DROP TABLE users; --"]}}}