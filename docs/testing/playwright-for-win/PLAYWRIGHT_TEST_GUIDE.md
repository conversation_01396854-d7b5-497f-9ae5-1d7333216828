# Playwright Testing Guide for Attendify App

## Overview

This comprehensive guide provides everything needed to implement Playwright testing for the Attendify application. The app is a Vue.js SPA with JWT authentication, multi-tenant architecture, and complex business workflows for time tracking, event management, and work scheduling.

## Application Architecture

### Technology Stack
- **Frontend**: Vue.js 3 (Options API), Vuex, Vue Router
- **Backend**: Ruby on Rails API
- **Authentication**: JWT-only with <PERSON>ttpOnly refresh tokens
- **Database**: Multi-tenant with company scoping
- **Styling**: Tailwind CSS + SCSS

### Key Features
1. **Authentication System**: Login, registration, password reset, email confirmation
2. **Time Tracking**: Daily logs, activities, work sessions
3. **Event Management**: Calendar events, meetings, scheduling
4. **Work Management**: Work assignments, scheduling, drag-and-drop
5. **Bookings**: Public booking links, appointment scheduling
6. **Company Management**: Multi-tenant company switching, settings
7. **User Management**: Contracts, roles, permissions

## Critical User Journeys

### 1. Authentication Flow
**Priority: CRITICAL**
- User registration with email confirmation
- Login with JWT token handling
- Password reset flow
- Session restoration on page refresh
- Logout and token cleanup

### 2. Time Tracking Workflow
**Priority: HIGH**
- Start daily log
- Track activities and breaks
- Switch between different work items
- End daily log with summary
- View historical logs

### 3. Event Management
**Priority: HIGH**
- Create calendar events (vacation, illness, travel)
- Schedule meetings with multiple participants
- Manage event conflicts and approvals
- Drag-and-drop rescheduling

### 4. Work Management
**Priority: HIGH**
- Create and assign work items
- Schedule work with date/time constraints
- Track work progress and completion
- Manage service contracts

### 5. Company Operations
**Priority: MEDIUM**
- Switch between companies (multi-tenant)
- Manage team members and contracts
- Configure company settings
- Handle company connections

## API Endpoints Reference

### Authentication Endpoints
```
POST /api/v1/auth/jwt_login          # Login with credentials
POST /api/v1/auth/jwt_register       # Register new user
POST /api/v1/auth/jwt_logout         # Logout and revoke tokens
POST /api/v1/auth/refresh_token      # Refresh access token
POST /api/v1/auth/password_reset     # Reset password
POST /api/v1/auth/confirm_email      # Confirm email address
```

### Core Business Endpoints
```
GET  /api/v1/user                    # Current user data
GET  /api/v1/employees               # Company employees
GET  /api/v1/subscription_status     # Subscription info
POST /api/v1/companies/switch_company # Company switching

# Time Tracking
GET  /api/v1/daily_logs              # Daily logs list
POST /api/v1/daily_logs              # Create daily log
POST /api/v1/daily_logs/start        # Start tracking
POST /api/v1/daily_logs/finish       # End tracking

# Events & Calendar
GET  /api/v1/events                  # Calendar events
POST /api/v1/events                  # Create event
GET  /api/v1/meetings                # Meetings list
POST /api/v1/meetings                # Create meeting

# Work Management
GET  /api/v1/works                   # Work items
POST /api/v1/works                   # Create work
PATCH /api/v1/works/:id              # Update work

# Bookings
GET  /api/v1/bookings                # Bookings list
POST /api/v1/bookings                # Create booking
```

### Error Response Patterns
- **401 Unauthorized**: Invalid or expired JWT token
- **403 Forbidden**: Insufficient permissions
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server errors

## Component Structure & Selectors

### Global Components
- `EventForm` - Event creation/editing
- `WorkForm` - Work item management
- `MeetingForm` - Meeting scheduling
- `ServiceContractForm` - Contract management
- `WorkShow` - Work item display

### Layout Components
- `DefaultLayout` - Authenticated user layout
- `AuthLayout` - Login/registration layout
- `Sidebar` - Main navigation
- `HeaderNav` - Top navigation
- `CentralModal` - Modal system

### Key Interactive Elements

#### Navigation
```html
<!-- Main navigation -->
<nav data-vue-component="header-nav">
  <LocalizedLink data-nav-link="dashboard" data-testid="nav-dashboard">
  <LocalizedLink data-nav-link="events" data-testid="nav-events">
  <LocalizedLink data-nav-link="contracts" data-testid="nav-contracts">
</nav>

<!-- Sidebar navigation -->
<div class="sidebar-content">
  <LocalizedLink data-testid="nav-dashboard" class="nav-item">
  <LocalizedLink data-testid="nav-mainbox" class="nav-item">
</div>
```

#### Forms
```html
<!-- Event Form -->
<form data-form="event-form">
  <select data-field="event-type" data-testid="event-type-select">
  <input data-field="start-time" data-testid="start-time-input">
  <button data-action="submit-event" data-testid="submit-event-form">
</form>

<!-- Work Form -->
<form data-form="work-form">
  <input data-field="work-title" data-testid="work-title-input">
  <textarea data-field="work-description" data-testid="work-description-input">
  <button data-action="submit-work" data-testid="submit-work-form">
</form>

<!-- Meeting Form -->
<form data-form="meeting-form">
  <input data-field="meeting-title" data-testid="meeting-title-input">
  <div data-testid="contract-selection" class="contracts-list">
  <button data-action="submit-meeting" data-testid="submit-meeting-form">
</form>
```

#### Authentication Forms
```html
<!-- Login Form -->
<form data-form="login-form">
  <input data-field="email" data-testid="login-email-input">
  <input data-field="password" data-testid="login-password-input">
  <button data-action="login" data-testid="login-submit-button">
</form>

<!-- Registration Form -->
<form data-form="register-form">
  <input data-field="email" data-testid="register-email-input">
  <input data-field="password" data-testid="register-password-input">
  <button data-action="register" data-testid="register-submit-button">
</form>
```

## Test Data Requirements

### User Data
```javascript
const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'password123',
    role: 'admin'
  },
  employee: {
    email: '<EMAIL>', 
    password: 'password123',
    role: 'employee'
  },
  manager: {
    email: '<EMAIL>',
    password: 'password123', 
    role: 'manager'
  }
};
```

### Company Data
```javascript
const testCompany = {
  name: 'Test Company Ltd.',
  address: '123 Test Street, Test City',
  phone: '+420123456789',
  web: 'https://test-company.com',
  settings: {
    break_duration: 30,
    auto_break: true,
    auto_end: true,
    timezone: 'Prague'
  }
};
```

### Event Types
```javascript
const eventTypes = [
  'travel',      // Celodenní pracovní cesta
  'vacation',    // Dovolená
  'illness',     // Pracovní neschopnost
  'family_sick', // Ošetřování člena rodiny
  'day_care',    // Návštěva lékaře
  'other'        // Jiná absence
];
```

## Testing Priority Matrix

### CRITICAL (Must Never Break)
1. User authentication and session management
2. JWT token handling and refresh
3. Company switching and tenant isolation
4. Daily log creation and time tracking
5. Data security and authorization

### HIGH (Core Business Logic)
1. Event creation and calendar management
2. Work assignment and scheduling
3. Meeting organization and invitations
4. Booking system functionality
5. Form validation and error handling

### MEDIUM (User Experience)
1. Navigation and routing
2. Modal interactions
3. Drag-and-drop functionality
4. Real-time updates
5. Mobile responsiveness

### LOW (Nice to Have)
1. Animations and transitions
2. Keyboard shortcuts
3. Advanced filtering
4. Export functionality
5. Accessibility features

## Common Test Scenarios

### Edge Cases
- Empty form submissions
- Invalid email formats
- Expired JWT tokens
- Network connectivity issues
- Concurrent user actions
- Large data sets
- Special characters in inputs
- Date/time boundary conditions

### Error States
- API endpoint failures
- Validation errors
- Permission denied scenarios
- Session timeout handling
- Network request failures
- File upload errors
- Database constraint violations

### Performance Considerations
- Large calendar date ranges
- Multiple concurrent users
- Heavy API response payloads
- Real-time WebSocket connections
- File upload/download operations

## Sample Test Data Examples

### Realistic User Scenarios
```javascript
// Test users with different roles and permissions
const testUsers = {
  companyOwner: {
    email: '<EMAIL>',
    password: 'SecurePass123!',
    firstName: 'John',
    lastName: 'Owner',
    role: 'owner',
    permissions: ['manage_company', 'manage_employees', 'manage_works']
  },
  manager: {
    email: '<EMAIL>',
    password: 'ManagerPass123!',
    firstName: 'Jane',
    lastName: 'Manager',
    role: 'manager',
    permissions: ['manage_works', 'view_reports', 'manage_team']
  },
  employee: {
    email: '<EMAIL>',
    password: 'EmployeePass123!',
    firstName: 'Bob',
    lastName: 'Worker',
    role: 'employee',
    permissions: ['track_time', 'view_own_data']
  }
};

// Test companies with different configurations
const testCompanies = {
  smallCompany: {
    name: 'Small Business Ltd.',
    address: '123 Main Street, Prague, Czech Republic',
    phone: '+420123456789',
    web: 'https://smallbusiness.cz',
    settings: {
      break_duration: 30,
      auto_break: true,
      auto_end: false,
      timezone: 'Europe/Prague',
      approve_vacations: false
    },
    plan: 'basic'
  },
  enterpriseCompany: {
    name: 'Enterprise Corp.',
    address: '456 Business Avenue, Bratislava, Slovakia',
    phone: '+421987654321',
    web: 'https://enterprise.sk',
    settings: {
      break_duration: 45,
      auto_break: false,
      auto_end: true,
      timezone: 'Europe/Bratislava',
      approve_vacations: true
    },
    plan: 'premium'
  }
};
```

### Event Test Data
```javascript
const eventTestData = {
  vacation: {
    event_type: 'vacation',
    start_date: '2024-08-15',
    end_date: '2024-08-20',
    description: 'Summer vacation in Croatia',
    place: 'Croatia'
  },
  illness: {
    event_type: 'illness',
    start_date: '2024-07-25',
    end_date: '2024-07-25',
    description: 'Flu symptoms',
    place: 'Home'
  },
  businessTravel: {
    event_type: 'travel',
    start_date: '2024-09-10',
    end_date: '2024-09-12',
    start_time: '08:00',
    end_time: '18:00',
    description: 'Client meeting in Vienna',
    place: 'Vienna, Austria'
  }
};
```

### Work Assignment Test Data
```javascript
const workTestData = {
  hvacRepair: {
    title: 'HVAC System Maintenance',
    description: 'Annual maintenance of heating and cooling systems',
    location: 'Office Building A, Floor 3',
    work_type: 'maintenance',
    status: 'scheduled',
    scheduled_start_date: '2024-08-01',
    scheduled_end_date: '2024-08-01',
    confirmed_time: '09:00',
    estimated_duration: 240, // minutes
    assignees: ['technician1', 'technician2']
  },
  emergencyRepair: {
    title: 'Emergency Plumbing Repair',
    description: 'Burst pipe in basement - urgent repair needed',
    location: 'Main Office Building, Basement',
    work_type: 'emergency',
    status: 'urgent',
    scheduled_start_date: '2024-07-23',
    confirmed_time: '14:30',
    estimated_duration: 120,
    assignees: ['plumber1']
  }
};
```

## Edge Cases and Boundary Conditions

### Date/Time Edge Cases
- **Leap Year Dates**: February 29th handling
- **Daylight Saving Time**: Clock changes and timezone shifts
- **Year Boundaries**: December 31st to January 1st transitions
- **Weekend Scheduling**: Saturday/Sunday work assignments
- **Holiday Conflicts**: Events scheduled on public holidays
- **Past Date Validation**: Preventing scheduling in the past
- **Far Future Dates**: Handling dates years in advance

### Data Validation Edge Cases
- **Email Formats**: Unicode characters, long domains, special cases
- **Phone Numbers**: International formats, extensions, invalid formats
- **Text Fields**: Maximum length limits, special characters, emoji
- **File Uploads**: Large files, invalid formats, corrupted files
- **Numeric Inputs**: Negative numbers, decimals, very large numbers

### Concurrency Edge Cases
- **Simultaneous Logins**: Same user logging in from multiple devices
- **Overlapping Events**: Multiple users scheduling conflicting events
- **Race Conditions**: Rapid form submissions, double-clicks
- **Session Conflicts**: Token refresh during active requests
- **Real-time Updates**: WebSocket message ordering

## Performance Testing Scenarios

### Load Testing Targets
- **Authentication**: 100 concurrent logins per minute
- **Calendar Views**: Loading 12-month date ranges with 500+ events
- **Work Assignments**: Bulk assignment of 50+ work items
- **Real-time Updates**: 20+ users with live WebSocket connections
- **File Operations**: Multiple simultaneous file uploads
- **Report Generation**: Complex queries with large datasets

### Memory and Resource Testing
- **Large Form Data**: Forms with extensive text content
- **Image Uploads**: High-resolution company logos and attachments
- **Extended Sessions**: 8+ hour active sessions without logout
- **Background Processes**: Long-running time tracking sessions
- **Cache Performance**: Repeated API calls and data caching

## Accessibility Testing Requirements

### WCAG 2.1 AA Compliance
- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Minimum 4.5:1 ratio for normal text
- **Focus Management**: Visible focus indicators and logical tab order
- **Alternative Text**: Images and icons with descriptive alt text
- **Form Labels**: All inputs properly labeled and associated

### Assistive Technology Testing
- **Screen Readers**: NVDA, JAWS, VoiceOver compatibility
- **Voice Control**: Dragon NaturallySpeaking support
- **High Contrast Mode**: Windows High Contrast compatibility
- **Zoom Functionality**: 200% zoom without horizontal scrolling
- **Reduced Motion**: Respect for prefers-reduced-motion settings

## Security Testing Considerations

### Authentication Security
- **JWT Token Security**: Proper token expiration and refresh handling
- **Session Management**: Secure logout and session cleanup
- **Password Security**: Strong password requirements and validation
- **Brute Force Protection**: Login attempt limiting and lockouts
- **CSRF Protection**: Cross-site request forgery prevention

### Data Security
- **Tenant Isolation**: Company data separation and access controls
- **Permission Enforcement**: Role-based access control validation
- **Data Sanitization**: XSS prevention and input sanitization
- **API Security**: Rate limiting and request validation
- **File Upload Security**: Malicious file detection and validation

## Next Steps

1. **Setup Playwright Environment**: Install dependencies and configure test runner
2. **Implement Authentication Tests**: Start with login/logout flows
3. **Create Page Object Models**: Structure tests with reusable components
4. **Add API Testing**: Validate backend endpoints
5. **Implement Visual Testing**: Screenshot comparisons for UI consistency
6. **Setup CI/CD Integration**: Automated test execution
7. **Performance Testing**: Load testing for critical paths
8. **Accessibility Testing**: WCAG compliance validation

## Additional Resources

- **API Documentation**: See `docs/features/*/api-reference.md`
- **Component Documentation**: See `docs/development/testing_debugging_guide.md`
- **Architecture Guide**: See `docs/architecture/architecture_and_implementation_guide.md`
- **Security Guidelines**: See `spec/security/endpoint_scanner_spec.rb`
- **Test Scenarios**: See `test-scenarios.json` in this directory
- **Selectors Guide**: See `selectors-guide.json` in this directory
- **API Endpoints**: See `api-endpoints.json` in this directory
