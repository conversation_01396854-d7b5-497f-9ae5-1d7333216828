{"selectorCategories": {"navigation": {"description": "Main navigation elements and routing", "selectors": {"headerNav": "[data-vue-component='header-nav']", "sidebar": "[data-vue-component='sidebar']", "navDashboard": "[data-nav-link='dashboard']", "navEvents": "[data-nav-link='events']", "navContracts": "[data-nav-link='contracts']", "navWorks": "[data-nav-link='works']", "navBookings": "[data-nav-link='bookings']", "navMeetings": "[data-nav-link='meetings']", "navReports": "[data-nav-link='reports']", "navSettings": "[data-nav-link='settings']", "companySelector": "[data-testid='company-selector']", "userMenu": "[data-testid='user-menu']", "logoutButton": "[data-action='logout']"}}, "authentication": {"description": "Login, registration, and auth-related forms", "selectors": {"loginForm": "[data-form='login-form']", "loginEmailInput": "[data-testid='login-email-input']", "loginPasswordInput": "[data-testid='login-password-input']", "loginSubmitButton": "[data-testid='login-submit-button']", "registerForm": "[data-form='register-form']", "registerEmailInput": "[data-testid='register-email-input']", "registerPasswordInput": "[data-testid='register-password-input']", "registerPasswordConfirmInput": "[data-testid='register-password-confirm-input']", "registerSubmitButton": "[data-testid='register-submit-button']", "forgotPasswordForm": "[data-form='forgot-password-form']", "forgotPasswordEmailInput": "[data-testid='forgot-password-email-input']", "forgotPasswordSubmitButton": "[data-testid='forgot-password-submit-button']", "resetPasswordForm": "[data-form='reset-password-form']", "resetPasswordInput": "[data-testid='reset-password-input']", "resetPasswordConfirmInput": "[data-testid='reset-password-confirm-input']", "resetPasswordSubmitButton": "[data-testid='reset-password-submit-button']"}}, "timeTracking": {"description": "Daily logs, activities, and time tracking", "selectors": {"dailyLogsSection": "[data-vue-component='daily-logs-app']", "startTrackingButton": "[data-action='start-tracking']", "endTrackingButton": "[data-action='end-tracking']", "activeTimer": "[data-testid='active-timer']", "dailyLogStatus": "[data-testid='daily-log-status']", "activitySelector": "[data-testid='activity-selector']", "breakButton": "[data-action='take-break']", "breakTimer": "[data-testid='break-timer']", "endBreakButton": "[data-action='end-break']", "activityLog": "[data-testid='activity-log']", "timeTrackingSummary": "[data-testid='time-tracking-summary']", "otherActivitiesSection": "[data-vue-component='other-activities-section']", "runningActivityBox": "[data-testid='running-activity-box']", "otherActivityField": "[data-testid='other-activity-field']"}}, "events": {"description": "Calendar events, meetings, and scheduling", "selectors": {"eventForm": "[data-form='event-form']", "eventTypeSelect": "[data-testid='event-type-select']", "eventStartDateInput": "[data-testid='event-start-date-input']", "eventEndDateInput": "[data-testid='event-end-date-input']", "eventStartTimeInput": "[data-testid='event-start-time-input']", "eventEndTimeInput": "[data-testid='event-end-time-input']", "eventPlaceInput": "[data-testid='event-place-input']", "eventDescriptionInput": "[data-testid='event-description-input']", "submitEventButton": "[data-testid='submit-event-form']", "cancelEventButton": "[data-action='cancel-event']", "calendarView": "[data-vue-component='monthly-event-table']", "calendarDay": "[data-testid^='calendar-day-']", "eventItem": "[data-testid^='event-item-']", "addEventButton": "[data-action='add-event']", "editEventButton": "[data-action='edit-event']", "deleteEventButton": "[data-action='delete-event']"}}, "meetings": {"description": "Meeting scheduling and management", "selectors": {"meetingForm": "[data-form='meeting-form']", "meetingTitleInput": "[data-testid='meeting-title-input']", "meetingDescriptionInput": "[data-testid='meeting-description-input']", "meetingPlaceInput": "[data-testid='meeting-place-input']", "contractSelection": "[data-testid='contract-selection']", "contractCheckbox": "[data-testid^='contract-checkbox-']", "additionalEmailInput": "[data-testid^='additional-email-']", "addEmailButton": "[data-action='add-email']", "removeEmailButton": "[data-action='remove-email']", "dateSelection": "[data-testid='date-selection']", "availableDate": "[data-testid^='available-date-']", "timeSlotSelection": "[data-testid='time-slot-selection']", "timeSlot": "[data-testid^='time-slot-']", "submitMeetingButton": "[data-testid='submit-meeting-form']", "nextStepButton": "[data-action='next-step']", "previousStepButton": "[data-action='previous-step']", "meetingStepIndicator": "[data-testid='meeting-step-indicator']"}}, "works": {"description": "Work management and assignments", "selectors": {"workForm": "[data-form='work-form']", "workTitleInput": "[data-testid='work-title-input']", "workDescriptionInput": "[data-testid='work-description-input']", "workLocationInput": "[data-testid='work-location-input']", "workTypeSelect": "[data-testid='work-type-select']", "workStatusSelect": "[data-testid='work-status-select']", "workStartDateInput": "[data-testid='work-start-date-input']", "workEndDateInput": "[data-testid='work-end-date-input']", "workTimeInput": "[data-testid='work-time-input']", "workAssigneeSelection": "[data-testid='work-assignee-selection']", "assigneeCheckbox": "[data-testid^='assignee-checkbox-']", "serviceContractSelect": "[data-testid='service-contract-select']", "submitWorkButton": "[data-testid='submit-work-form']", "worksList": "[data-testid='works-list']", "workItem": "[data-testid^='work-item-']", "workShow": "[data-vue-component='work-show']", "editWorkButton": "[data-action='edit-work']", "deleteWorkButton": "[data-action='delete-work']", "completeWorkButton": "[data-action='complete-work']"}}, "serviceContracts": {"description": "Service contract management", "selectors": {"serviceContractForm": "[data-form='service-contract-form']", "contractTitleInput": "[data-testid='contract-title-input']", "contractDescriptionInput": "[data-testid='contract-description-input']", "contractStatusSelect": "[data-testid='contract-status-select']", "contractClientSelect": "[data-testid='contract-client-select']", "submitContractButton": "[data-testid='submit-contract-form']", "cancelContractButton": "[data-action='cancel-contract']"}}, "bookings": {"description": "Booking system and public links", "selectors": {"bookingForm": "[data-form='booking-form']", "clientNameInput": "[data-testid='client-name-input']", "clientEmailInput": "[data-testid='client-email-input']", "clientPhoneInput": "[data-testid='client-phone-input']", "preferredDateInput": "[data-testid='preferred-date-input']", "preferredPeriodSelect": "[data-testid='preferred-period-select']", "bookingMessageInput": "[data-testid='booking-message-input']", "submitBookingButton": "[data-testid='submit-booking-form']", "bookingsList": "[data-testid='bookings-list']", "bookingItem": "[data-testid^='booking-item-']", "confirmBookingButton": "[data-action='confirm-booking']", "cancelBookingButton": "[data-action='cancel-booking']", "rescheduleBookingButton": "[data-action='reschedule-booking']", "bookingTimeConfirmModal": "[data-testid='booking-time-confirm-modal']", "confirmTimeButton": "[data-action='confirm-time']"}}, "companies": {"description": "Company management and settings", "selectors": {"companyForm": "[data-form='company-form']", "companyNameInput": "[data-testid='company-name-input']", "companyAddressInput": "[data-testid='company-address-input']", "companyPhoneInput": "[data-testid='company-phone-input']", "companyWebInput": "[data-testid='company-web-input']", "companyDescriptionInput": "[data-testid='company-description-input']", "companyLogoUpload": "[data-testid='company-logo-upload']", "submitCompanyButton": "[data-testid='submit-company-form']", "companySettingsForm": "[data-form='company-settings-form']", "breakDurationInput": "[data-testid='break-duration-input']", "autoBreakToggle": "[data-testid='auto-break-toggle']", "autoEndToggle": "[data-testid='auto-end-toggle']", "timezoneSelect": "[data-testid='timezone-select']", "saveSettingsButton": "[data-action='save-settings']"}}, "contracts": {"description": "Team member contracts and roles", "selectors": {"contractsList": "[data-testid='contracts-list']", "contractItem": "[data-testid^='contract-item-']", "addContractButton": "[data-action='add-contract']", "contractForm": "[data-form='contract-form']", "contractEmailInput": "[data-testid='contract-email-input']", "contractRoleSelect": "[data-testid='contract-role-select']", "contractFirstNameInput": "[data-testid='contract-first-name-input']", "contractLastNameInput": "[data-testid='contract-last-name-input']", "sendInvitationButton": "[data-action='send-invitation']", "suspendContractButton": "[data-action='suspend-contract']", "reactivateContractButton": "[data-action='reactivate-contract']", "terminateContractButton": "[data-action='terminate-contract']", "updateRoleButton": "[data-action='update-role']", "resendInvitationButton": "[data-action='resend-invitation']"}}, "modals": {"description": "Modal dialogs and overlays", "selectors": {"centralModal": "[data-vue-component='central-modal']", "modalOverlay": "[data-testid='modal-overlay']", "modalContent": "[data-testid='modal-content']", "modalHeader": "[data-testid='modal-header']", "modalTitle": "[data-testid='modal-title']", "modalBody": "[data-testid='modal-body']", "modalFooter": "[data-testid='modal-footer']", "closeModalButton": "[data-action='close-modal']", "confirmModalButton": "[data-action='confirm-modal']", "cancelModalButton": "[data-action='cancel-modal']"}}, "notifications": {"description": "Flash messages and notifications", "selectors": {"flashMessages": "[data-vue-component='flash-messages']", "successMessage": "[data-testid='success-message']", "errorMessage": "[data-testid='error-message']", "warningMessage": "[data-testid='warning-message']", "infoMessage": "[data-testid='info-message']", "dismissNotificationButton": "[data-action='dismiss-notification']"}}, "forms": {"description": "Common form elements and validation", "selectors": {"formGroup": ".form-group", "formLabel": ".form-label", "formInput": ".form-input", "formSelect": ".form-select", "formTextarea": ".form-textarea", "formError": ".error-message", "formSuccess": ".success-message", "requiredField": ".required", "submitButton": "[type='submit']", "cancelButton": "[data-action='cancel']", "resetButton": "[type='reset']"}}, "loading": {"description": "Loading states and spinners", "selectors": {"loadingSpinner": "[data-testid='loading-spinner']", "loadingOverlay": "[data-testid='loading-overlay']", "loadingText": "[data-testid='loading-text']", "skeletonLoader": "[data-testid='skeleton-loader']"}}}, "dynamicSelectors": {"description": "Selectors that include dynamic IDs or data", "patterns": {"calendarDay": "[data-testid='calendar-day-{date}']", "eventItem": "[data-testid='event-item-{eventId}']", "workItem": "[data-testid='work-item-{workId}']", "contractItem": "[data-testid='contract-item-{contractId}']", "bookingItem": "[data-testid='booking-item-{bookingId}']", "contractCheckbox": "[data-testid='contract-checkbox-{contractId}']", "assigneeCheckbox": "[data-testid='assignee-checkbox-{assigneeId}']", "additionalEmail": "[data-testid='additional-email-{index}']", "availableDate": "[data-testid='available-date-{date}']", "timeSlot": "[data-testid='time-slot-{time}']"}}, "stateSelectors": {"description": "Selectors for different UI states", "states": {"active": ".active, [data-state='active']", "disabled": ".disabled, [disabled], [data-state='disabled']", "loading": ".loading, [data-state='loading']", "error": ".error, [data-state='error']", "success": ".success, [data-state='success']", "selected": ".selected, [data-state='selected']", "expanded": ".expanded, [data-state='expanded']", "collapsed": ".collapsed, [data-state='collapsed']", "hidden": ".hidden, [data-state='hidden']", "visible": ".visible, [data-state='visible']"}}, "responsiveSelectors": {"description": "Selectors for mobile/desktop variants", "mobile": {"mobileNav": "[data-testid='mobile-nav']", "mobileMenu": "[data-testid='mobile-menu']", "mobileMenuToggle": "[data-testid='mobile-menu-toggle']", "mobileUnscheduledPanel": "[data-vue-component='mobile-unscheduled-panel']", "mobileWorkCard": "[data-vue-component='mobile-work-card']"}, "desktop": {"desktopSidebar": "[data-testid='desktop-sidebar']", "calendarSidebar": "[data-vue-component='calendar-sidebar']", "unscheduledWorkCard": "[data-vue-component='unscheduled-work-card']"}}, "testingHelpers": {"description": "Helper selectors for testing scenarios", "dataAttributes": {"vueComponent": "[data-vue-component]", "testId": "[data-testid]", "action": "[data-action]", "field": "[data-field]", "form": "[data-form]", "navLink": "[data-nav-link]"}, "waitForSelectors": {"pageLoaded": "body[data-page-loaded='true']", "apiLoaded": "[data-api-loaded='true']", "userLoaded": "[data-user-loaded='true']", "companyLoaded": "[data-company-loaded='true']"}}}