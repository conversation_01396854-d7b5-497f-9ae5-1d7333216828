# Multi-Tenant Context Validation Guide - TYM-30 Prevention

## Overview

This guide provides comprehensive patterns for preventing multi-tenant context mismatch issues that led to TYM-30 debugging hell. The problem occurred when test data was created in one company context while JWT authentication pointed to a different company, causing API calls to return empty results and leading to 2+ hours of debugging phantom problems.

## The TYM-30 Problem Pattern

### What Happened
```ruby
# Test data creation context
ActsAsTenant.with_tenant(Company.first)  # → Company 1 (PRŮMYSLOVÉ ČIŠTĚNÍ)
# Created events with Contract ID 191

# API authentication context  
JWT_TOKEN = "company_id: 3"              # → Company 3 (Byrd Ewing Associates)
# API searched Contract ID 80

# Result: API found 0 events, manual queries found 2 events
# = 2 hours of debugging non-existent bugs
```

### Impact
- **80% debugging time wasted** on phantom problems
- **False bug reports** created about working features  
- **Resource drain** from senior developer time
- **Pattern risk** - will happen again without prevention

## Prevention Tools

### 1. Context Validation Helper

Use `TenantContextValidator` to detect mismatches:

```ruby
# In your tests
include TenantContextValidator

# Validate token against current tenant
ensure_tenant_jwt_context_match(jwt_token)

# Create aligned test data and token
token = create_test_data_with_matching_jwt_context(user, company)

# Debug current context before API calls
debug_current_context(jwt_token)
```

### 2. Enhanced Factory Patterns

Use tenant-aware factory traits:

```ruby
# Detect potential mismatches with warnings
create(:contract, :tenant_aware, company: target_company)

# Create in current tenant context
create(:event, :in_current_tenant)

# Strict validation - raises error on mismatch
create(:event, :with_validated_context, company: target_company)

# Use helper for guaranteed alignment
create_in_tenant_context(:event, company: target_company)
```

### 3. Development Environment Validation

Use rake tasks to check context health:

```bash
# Check current development context
rake tenant_context:check

# Generate aligned JWT token  
rake tenant_context:generate_jwt[<EMAIL>,98]

# Validate specific token
rake tenant_context:validate_jwt[your_jwt_token]
```

### 4. Comprehensive Testing Patterns

Use the debugging utility for investigation:

```ruby
# Investigate context mismatches
TenantContextDebugger.investigate_context_mismatch(
  '<EMAIL>', 
  jwt_token: your_token
)

# Create test events with proper context
TenantContextDebugger.create_test_events_for_context_validation(
  company_id, 
  user_email
)

# Test conflict detection in proper context
TenantContextDebugger.test_conflict_detection_with_context(
  contract_id, 
  start_date, 
  end_date
)
```

## Testing Patterns

### RSpec Multi-Tenant Tests

```ruby
RSpec.describe 'Multi-tenant feature', type: :request do
  include TenantContextValidator
  include TenantAwareFactories
  
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  
  before do
    create(:company_user_role, user: user, company: company, role: create(:role))
  end
  
  it 'maintains context alignment' do
    ActsAsTenant.with_tenant(company) do
      # Create test data in tenant context
      contract = create(:contract, :with_validated_context, company: company)
      event = create(:event, :with_validated_context, contract: contract, company: company)
      
      # Generate aligned JWT token
      token = create_test_data_with_matching_jwt_context(user, company)
      
      # Make API call with validation
      get '/api/v1/events', headers: { 'Authorization' => "Bearer #{token}" }
      
      # Verify response contains the created event
      expect(response).to have_http_status(:success)
      events = JSON.parse(response.body)
      expect(events.map { |e| e['id'] }).to include(event.id)
    end
  end
end
```

### Playwright Context Validation

```typescript
import { test, expect } from './fixtures/auth';

test('should prevent TYM-30 scenario: API data aligns with UI', async ({ authenticatedEmployee }) => {
  const page = authenticatedEmployee.page;
  
  await page.goto('/events');
  
  // Capture API response
  let eventsApiResponse: any = null;
  page.on('response', async response => {
    if (response.url().includes('/api/v1/events')) {
      eventsApiResponse = await response.json();
    }
  });
  
  await page.waitForLoadState('networkidle');
  
  // Count visible events in UI
  const visibleEvents = await page.locator('.event-item').count();
  
  // Key validation: API response should match UI display
  if (eventsApiResponse.length === 0 && visibleEvents > 0) {
    throw new Error('🚨 TYM-30 SCENARIO: API returned 0 events but UI shows events!');
  }
  
  expect(eventsApiResponse.length).toBe(visibleEvents);
});
```

## Development Workflow

### Before Creating Test Data

1. **Check current context**:
   ```ruby
   puts "Current tenant: #{ActsAsTenant.current_tenant&.id}"
   ```

2. **Validate context alignment**:
   ```bash
   rake tenant_context:check
   ```

### Before Making API Calls

1. **Debug context alignment**:
   ```ruby
   debug_current_context(your_jwt_token)
   ```

2. **Ensure token matches data context**:
   ```ruby
   ensure_tenant_jwt_context_match(jwt_token, expected_company: target_company)
   ```

### When Creating Test Scenarios

1. **Use aligned factory helpers**:
   ```ruby
   scenario = create_test_scenario_with_jwt(user, company, {
     contract: { first_name: 'Test' },
     event: { event_type: 'illness' }
   })
   
   # scenario[:objects] contains created objects
   # scenario[:jwt_token] is aligned with the data
   ```

2. **Or use explicit tenant context**:
   ```ruby
   ActsAsTenant.with_tenant(target_company) do
     contract = create(:contract, company: target_company)
     event = create(:event, contract: contract, company: target_company)
     token = generate_jwt_for_company(user, target_company)
   end
   ```

## Error Messages and Solutions

### Context Mismatch Error

When you see:
```
🚨 TENANT CONTEXT MISMATCH DETECTED 🚨

JWT Token expects Company ID: 3 (Company B)  
ActsAsTenant context is: 1 (Company A)
```

**Solutions:**
1. **Align data to JWT**: Create data in JWT's company context
2. **Align JWT to data**: Generate new JWT for data's company
3. **Use helpers**: Use `create_test_data_with_matching_jwt_context`

### Factory Context Error

When you see:
```
🚨 EVENT FACTORY CONTEXT MISMATCH - TYM-30 SCENARIO DETECTED! 🚨

This is the EXACT configuration that caused 2+ hours of debugging hell!
```

**Solutions:**
1. **Use tenant context wrapper**:
   ```ruby
   ActsAsTenant.with_tenant(target_company) do
     event = create(:event, :with_validated_context)  
   end
   ```

2. **Use tenant-aware helper**:
   ```ruby
   event = create_in_tenant_context(:event, company: target_company)
   ```

## Best Practices

### ✅ DO

- **Always validate context** before API calls in tests
- **Use tenant-aware factory traits** for strict validation  
- **Create test data and JWT tokens together** for alignment
- **Monitor Rails logs** during testing for tenant context switches
- **Use the debugging utility** to investigate complex scenarios

### ❌ DON'T  

- **Never assume** test data and JWT context are aligned
- **Don't create** JWT tokens separately from test data
- **Don't ignore** factory context warnings in logs
- **Don't debug** context issues manually - use the provided tools
- **Don't mix** company contexts within a single test scenario

## Migration Guide

### From Manual Context Management

**Old pattern:**
```ruby
# Prone to TYM-30 issues
company = Company.first  
user = User.first
contract = create(:contract, company: company)
token = generate_jwt(user, company)  # Different helper, potential mismatch
```

**New pattern:**
```ruby
# TYM-30 prevention
scenario = create_test_scenario_with_jwt(user, company, {
  contract: { first_name: 'Test' }
})
contract = scenario[:objects][:contract]
token = scenario[:jwt_token]  # Guaranteed alignment
```

### From Implicit Context Assumptions

**Old pattern:**
```ruby
# Assumes context alignment
event = create(:event)
get '/api/v1/events', headers: auth_headers(token)
```

**New pattern:**  
```ruby
# Explicit context validation
ActsAsTenant.with_tenant(company) do
  event = create(:event, :with_validated_context, company: company)
  token = create_test_data_with_matching_jwt_context(user, company)
  
  get '/api/v1/events', headers: { 'Authorization' => "Bearer #{token}" }
  expect(response.body).to include(event.id.to_s)
end
```

## Monitoring and Alerts

### Log Patterns to Watch

Watch for these patterns in development logs:

```
[TENANT_CONTEXT] ⚠️  POTENTIAL MISMATCH DETECTED
[CONTRACT_FACTORY] ⚠️  Potential context mismatch detected!
[EVENT_FACTORY] ⚠️  Event context mismatch detected!  
```

### Continuous Integration

Add context validation to CI:

```ruby
# In spec_helper.rb or equivalent
RSpec.configure do |config|
  config.before(:each, type: :request) do
    # Ensure no stale tenant context between tests
    ActsAsTenant.current_tenant = nil
  end
  
  config.after(:each, type: :request) do  
    # Log any tenant context leaks
    if ActsAsTenant.current_tenant
      Rails.logger.warn "Test left tenant context set to #{ActsAsTenant.current_tenant.id}"
    end
  end
end
```

## Success Metrics

### Immediate (Next Sprint)
- [ ] Zero context mismatch issues in testing
- [ ] 90% reduction in false debugging time  
- [ ] Clear error messages when tenant context is wrong

### Medium Term (Next Month)
- [ ] Full test coverage with context validation
- [ ] Automated context alignment checks
- [ ] Developer confidence in testing environment

### Long Term (Next Quarter)  
- [ ] Zero phantom debugging sessions due to context issues
- [ ] New developers can identify context problems in under 5 minutes
- [ ] Pattern adoption across all multi-tenant features

## Related Documentation

- [Playwright Testing Guide](01_playwright_testing_guide.md) - Multi-role authentication setup
- [Critical Testing Methodology](03_critical_testing_methodology_guidelines.md) - Workflow testing principles  
- [Log-Aware Testing Guide](02_log_aware_testing_guide.md) - Monitoring logs during testing
- [Consolidated Testing Guide](09_consolidated_testing_guide.md) - Single source testing reference

---

**Remember**: Context mismatches cause phantom debugging problems. When in doubt, validate context alignment first!