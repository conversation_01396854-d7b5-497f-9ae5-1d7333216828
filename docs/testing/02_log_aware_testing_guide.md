# Log-Aware Testing Guide

This guide documents how to perform testing while monitoring application logs to diagnose failures in real-time.

## The Problem Solved

When Playwright tests fail, they often timeout without clear error messages. By monitoring Rails logs during test execution, we can see the actual server responses and error conditions that cause test failures.

## Example: Employee Login Failure Analysis

### Test Symptoms
```
TimeoutError: page.waitForURL: Timeout 10000ms exceeded.
waiting for navigation to "**/cs/dashboard" until "load"
```

### Rails Log Analysis
```
19:13:00 web.1  | Started POST "/api/v1/auth/jwt_login" for 192.168.1.56 at 2025-07-22 19:13:00 +0200
19:13:00 web.1  | Processing by Api::V1::AuthController#jwt_login as JSON
19:13:00 web.1  | Parameters: {"email"=>"<EMAIL>", "password"=>"[FILTERED]", "remember_me"=>false}
19:13:00 web.1  | User Load (0.5ms)  SELECT "users".* FROM "users" WHERE "users"."email" = $1 LIMIT $2
19:13:00 web.1  | Completed 403 Forbidden in 325ms
```

### Browser Console Analysis
```
JWT login failed: Request failed with status code 403
Login failed: Error: Login failed: E-mail nebyl potvrzen
```

### Root Cause
**403 Forbidden** with **"E-mail nebyl potvrzen"** = Email not confirmed for invited user.

### Solution
```ruby
# Rails console
user = User.find_by(email: "<EMAIL>")
user.confirmed_at = Time.current
user.save
```

## Log Monitoring Techniques

### 1. Rails Development Log
Monitor `log/development.log` or server output for:
- HTTP status codes (403, 422, 500)
- SQL queries and their results
- Controller processing messages
- Parameter filtering and validation errors

### 2. Browser Console Logs
Check browser developer tools for:
- JavaScript errors
- Network request failures
- Vue.js component errors
- Authentication token issues

### 3. Playwright Debug Output
Use these flags for detailed debugging:
```bash
# Debug mode with headed browser
npx playwright test --debug --headed

# Trace mode for step-by-step analysis  
npx playwright test --trace=on

# Screenshot on failure
npx playwright test --screenshot=on
```

## Common Issue Patterns

### Authentication Failures

**Symptom**: Redirect to login page instead of dashboard
**Log Pattern**: 
```
Completed 403 Forbidden
# or
JWT login failed: Request failed with status code 403
```

**Root Causes**:
1. Email not confirmed: `"E-mail nebyl potvrzen"`
2. Invalid credentials: `"Invalid email or password"`
3. Account locked/disabled
4. Missing company associations

### Database Issues

**Symptom**: 500 Internal Server Error
**Log Pattern**:
```
ActiveRecord::RecordNotFound
# or
PG::UndefinedColumn: ERROR: column "role" does not exist
```

**Root Causes**:
1. Missing database records
2. Schema mismatches
3. Association errors

### Permission Issues

**Symptom**: 403 Forbidden for specific actions
**Log Pattern**:
```
ActionPolicy::UnauthorizedError
# or
Access denied for action
```

**Root Causes**:
1. Insufficient user role permissions
2. Company membership issues
3. Policy restrictions

## Debugging Workflow

### Step 1: Capture the Failure
```typescript
// In test files, add debugging
test('employee login', async ({ page }) => {
  // ... test setup
  
  await page.waitForTimeout(3000); // Allow time for logs
  const currentUrl = page.url();
  console.log('Current URL:', currentUrl);
  
  // Check for error messages
  const errorMsg = await page.locator('.alert-danger').textContent().catch(() => null);
  if (errorMsg) console.log('Error:', errorMsg);
});
```

### Step 2: Analyze Rails Logs
Look for the request that corresponds to your test action:
- Check HTTP method and path
- Examine parameters
- Look at the response status code
- Check SQL queries for data issues

### Step 3: Check Database State
```ruby
# Rails console commands for common checks
user = User.find_by(email: "<EMAIL>")
puts "Confirmed: #{user.confirmed_at}"
puts "Companies: #{user.companies.pluck(:name)}"
puts "Roles: #{user.company_user_roles.pluck(:role_id)}"
```

### Step 4: Fix and Verify
1. Fix the root cause (email confirmation, permissions, etc.)
2. Re-run the specific test
3. Verify the fix in Rails console if needed

## Rails Console Quick Fixes

### Email Confirmation
```ruby
user = User.find_by(email: "<EMAIL>")
user.confirmed_at = Time.current
user.save
```

### Password Reset
```ruby
user = User.find_by(email: "<EMAIL>")
user.password = "123456"
user.password_confirmation = "123456"
user.save
```

### Company Association
```ruby
user = User.find_by(email: "<EMAIL>")
company = Company.find(98)
CompanyUserRole.create!(user: user, company: company, role_id: 2) # employee role
```

## Integration with Testing Framework

### Enhanced Test Helper
```typescript
// test/e2e/helpers/log-monitor.ts
export class LogMonitor {
  static async captureRailsLogs(testName: string) {
    // Implementation to capture logs during test execution
    // Could tail log files or use monitoring endpoints
  }
  
  static async checkBrowserErrors(page: Page) {
    const errors = await page.evaluate(() => {
      return window.console._logs || [];
    });
    return errors.filter(log => log.level === 'error');
  }
}
```

## Best Practices

### 1. Always Check Logs on Test Failure
- Don't just re-run failed tests
- Always examine the Rails logs first
- Look for patterns in browser console

### 2. Use Descriptive Test Names
```typescript
// Good: Describes what should happen
test('employee with confirmed email can log in to dashboard')

// Bad: Generic description  
test('employee login test')
```

### 3. Add Context to Test Output
```typescript
console.log('Testing login with:', testUsers.employee.email);
console.log('Expected redirect:', '/cs/dashboard');
console.log('Actual URL:', page.url());
```

### 4. Set Up Users Properly
```typescript
// Before running tests, verify user state
const user = await checkUserInDatabase(testUsers.employee.email);
if (!user.confirmed_at) {
  await confirmUserEmail(user);
}
```

## Summary

Log-aware testing transforms debugging from guesswork to systematic problem-solving:

1. **Symptom**: Test timeout or unexpected behavior
2. **Investigation**: Check Rails logs + browser console  
3. **Diagnosis**: Identify specific error messages and status codes
4. **Resolution**: Fix root cause in database/configuration
5. **Verification**: Re-run test to confirm fix

This approach enabled us to quickly identify and fix the employee email confirmation issue that was causing authentication failures.

---

**Last Updated**: 2025-07-22  
**Status**: Based on successful employee authentication debugging  
**Next Steps**: Integrate log monitoring into automated test pipeline