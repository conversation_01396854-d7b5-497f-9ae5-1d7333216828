require 'rails_helper'

RSpec.describe "Meeting Date Validation", type: :request do
  let(:company) { create(:company) }
  let(:user) { create(:user) }
  let!(:company_user_role) { create(:company_user_role, user: user, company: company, is_primary: true) }
  
  describe "GET /private_meetings/:token/meeting" do
    context "when meeting has mixed past and future dates" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          day_options: {
            (Date.today - 1.day).to_s => { '09:00' => true, '10:00' => true }, # Past date
            (Date.today + 1.day).to_s => { '09:00' => true, '10:00' => true }  # Future date
          },
          confirmed_date: nil
        )
      end
      
      let(:meeting_user) { create(:meeting_user, meeting: meeting, email: '<EMAIL>') }
      
      it "should NOT return completed status when some dates are still in future" do
        get "/private_meetings/#{meeting_user.token}/meeting"
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # Should not return completed status
        expect(json_response["status"]).not_to eq("completed")
        
        # Should return meeting data
        expect(json_response["meeting"]).to be_present
        expect(json_response["meeting"]["id"]).to eq(meeting.id)
      end
    end
    
    context "when meeting has all dates in the past" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          day_options: {
            (Date.today - 2.days).to_s => { '09:00' => true, '10:00' => true }, # Past date
            (Date.today - 1.day).to_s => { '09:00' => true, '10:00' => true }   # Past date
          },
          confirmed_date: nil
        )
      end
      
      let(:meeting_user) { create(:meeting_user, meeting: meeting, email: '<EMAIL>') }
      
      it "should return completed status when all dates have passed" do
        get "/private_meetings/#{meeting_user.token}/meeting"
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # Should return completed status
        expect(json_response["status"]).to eq("completed")
        
        # Should not return meeting data
        expect(json_response["meeting"]).to be_nil
      end
    end
    
    context "when meeting is confirmed" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          day_options: {
            (Date.today + 1.day).to_s => { '09:00' => true, '10:00' => true }  # Future date
          },
          confirmed_date: DateTime.now
        )
      end
      
      let(:meeting_user) { create(:meeting_user, meeting: meeting, email: '<EMAIL>') }
      
      it "should return completed status when meeting is confirmed" do
        get "/private_meetings/#{meeting_user.token}/meeting"
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # Should return completed status
        expect(json_response["status"]).to eq("completed")
        
        # Should not return meeting data
        expect(json_response["meeting"]).to be_nil
      end
    end
    
    context "when meeting has all dates in the future" do
      let(:meeting) do
        create(:meeting, 
          company: company, 
          created_by: user,
          day_options: {
            (Date.today + 1.day).to_s => { '09:00' => true, '10:00' => true },  # Future date
            (Date.today + 2.days).to_s => { '09:00' => true, '10:00' => true }  # Future date
          },
          confirmed_date: nil
        )
      end
      
      let(:meeting_user) { create(:meeting_user, meeting: meeting, email: '<EMAIL>') }
      
      it "should NOT return completed status when all dates are in future" do
        get "/private_meetings/#{meeting_user.token}/meeting"
        
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        
        # Should not return completed status
        expect(json_response["status"]).not_to eq("completed")
        
        # Should return meeting data
        expect(json_response["meeting"]).to be_present
        expect(json_response["meeting"]["id"]).to eq(meeting.id)
      end
    end
  end
end
