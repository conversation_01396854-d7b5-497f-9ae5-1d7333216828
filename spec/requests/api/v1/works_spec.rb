# ABOUTME: RSpec request tests for API V1 Works controller endpoints
# ABOUTME: Validates work assignment loading, deletion prevention, and force close functionality
require 'rails_helper'

RSpec.describe 'Api::V1::Works', type: :request do
  let(:company) { create(:company, name: "Test Company", subdomain: "test") }
  let(:user1) { create(:user, email: "<EMAIL>") }
  let(:user2) { create(:user, email: "<EMAIL>") }
  let(:contract1) do
    contract = build(:contract, company: company, user: user1, first_name: "Test1", last_name: "User1")
    allow(contract).to receive(:send_invitation).and_return(true)
    contract.save!
    contract
  end
  let(:contract2) do
    contract = build(:contract, company: company, user: user2, first_name: "Test2", last_name: "User2")
    allow(contract).to receive(:send_invitation).and_return(true)
    contract.save!
    contract
  end
  let(:work) do
    Work.create!(
      title: "Test Work with Multiple Assignments",
      company: company,
      status: "in_progress"
    )
  end
  let(:assignment1) do
    WorkAssignment.create!(
      work: work,
      contract: contract1,
      company: company,
      role: "worker",
      is_lead: true
    )
  end
  let(:assignment2) do
    WorkAssignment.create!(
      work: work,
      contract: contract2,
      company: company,
      role: "worker",
      is_lead: false
    )
  end

  before do
    # Set up current company context for ActsAsTenant
    ActsAsTenant.current_tenant = company
    
    # Create assignments to ensure the relationships exist
    assignment1
    assignment2
  end

  def jwt_auth_headers(user = user1)
    # Create JWT token with proper company context
    payload = {
      user_id: user.id,
      email: user.email,
      company_id: company.id
    }
    
    token = JwtService.encode(payload, 1.hour.from_now)
    auth_headers(token)
  end

  describe 'GET /api/v1/works/assigned' do
    context 'when user is assigned to work' do
      it 'returns works with ALL work_assignments' do
        get '/api/v1/works/assigned', headers: jwt_auth_headers(user1)
        
        expect(response).to have_http_status(:success)
        response_data = JSON.parse(response.body)
        
        # Should return the work since user1 is assigned
        expect(response_data.length).to eq(1)
        
        work_data = response_data.first
        expect(work_data["id"]).to eq(work.id)
        
        # Critical test: Should include BOTH work_assignments
        work_assignments = work_data["work_assignments"]
        expect(work_assignments.length).to eq(2), "Should load ALL work_assignments, not just current user's"
        
        # Verify both contracts are present
        contract_ids = work_assignments.map { |wa| wa.dig("contract", "id") }
        expect(contract_ids).to include(contract1.id)
        expect(contract_ids).to include(contract2.id)
      end

      it 'matches console behavior for work assignment data' do
        get '/api/v1/works/assigned', headers: jwt_auth_headers(user1)
        
        # Verify our test setup matches expected data structure
        expect(work.work_assignments.count).to eq(2)
        assignment_contract_ids = work.work_assignments.pluck(:contract_id)
        expect(assignment_contract_ids).to include(contract1.id)
        expect(assignment_contract_ids).to include(contract2.id)
        
        response_data = JSON.parse(response.body)
        work_data = response_data.first
        api_assignment_contract_ids = work_data["work_assignments"].map { |wa| wa.dig("contract", "id") }
        
        # API should return same assignments as direct model access
        expect(api_assignment_contract_ids.sort).to eq(assignment_contract_ids.sort)
      end
    end
  end

  describe 'DELETE /api/v1/works/:id' do
    context 'when work has no active daily activities' do
      it 'successfully deletes the work' do
        # Create a work with assignment but no active daily activities
        work_to_delete = Work.create!(
          title: "Work to Delete",
          company: company,
          status: "scheduled"
        )
        
        WorkAssignment.create!(
          work: work_to_delete,
          contract: contract1,
          company: company,
          role: "worker",
          is_lead: true
        )
        
        expect {
          delete "/api/v1/works/#{work_to_delete.id}", headers: jwt_auth_headers(user1)
        }.to change(Work, :count).by(-1)
        
        expect(response).to have_http_status(:no_content)
      end
    end

    context 'when work has active daily activities' do
      it 'prevents deletion and returns validation error' do
        # Create work with assignment
        work_with_activity = Work.create!(
          title: "Work with Active Activity",
          company: company,
          status: "in_progress"
        )
        
        WorkAssignment.create!(
          work: work_with_activity,
          contract: contract1,
          company: company,
          role: "worker",
          is_lead: true
        )
        
        # Create active daily activity
        DailyActivity.create!(
          work: work_with_activity,
          user: user1,
          company: company,
          contract: contract1,
          start_time: 1.hour.ago,
          end_time: nil,
          activity_type: "work_at_location"
        )
        
        expect {
          delete "/api/v1/works/#{work_with_activity.id}", headers: jwt_auth_headers(user1)
        }.not_to change(Work, :count)
        
        expect(response).to have_http_status(:unprocessable_entity)
        
        response_data = JSON.parse(response.body)
        expect(response_data["success"]).to be false
        expect(response_data["errors"].first).to include("Cannot modify work. Currently working:")
      end
    end
  end

  describe 'POST /api/v1/works/:id/force_close_activities' do
    context 'when work has active daily activities' do
      it 'closes all active activities for the work' do
        # Create work
        work_with_activities = Work.create!(
          title: "Work with Activities",
          company: company,
          status: "in_progress"
        )
        
        # Create multiple active daily activities
        activity1 = DailyActivity.create!(
          work: work_with_activities,
          user: user1,
          company: company,
          contract: contract1,
          start_time: 2.hours.ago,
          end_time: nil,
          activity_type: "work_at_location"
        )
        
        activity2 = DailyActivity.create!(
          work: work_with_activities,
          user: user2,
          company: company,
          contract: contract2,
          start_time: 1.hour.ago,
          end_time: nil,
          activity_type: "work_at_location"
        )
        
        post "/api/v1/works/#{work_with_activities.id}/force_close_activities", headers: jwt_auth_headers(user1)
        
        expect(response).to have_http_status(:success)
        
        response_data = JSON.parse(response.body)
        expect(response_data["success"]).to be true
        expect(response_data["count"]).to eq(2)
        expect(response_data["message"]).to include("2 activities closed")
        
        # Verify activities are closed
        activity1.reload
        activity2.reload
        expect(activity1.end_time).not_to be_nil
        expect(activity2.end_time).not_to be_nil
        expect(activity1.description).to include("Admin force closed:")
        expect(activity2.description).to include("Admin force closed:")
      end
    end

    context 'when work has no active daily activities' do
      it 'returns success with count 0' do
        # Create work without active activities
        work_without_activities = Work.create!(
          title: "Work without Activities",
          company: company,
          status: "scheduled"
        )
        
        post "/api/v1/works/#{work_without_activities.id}/force_close_activities", headers: jwt_auth_headers(user1)
        
        expect(response).to have_http_status(:success)
        
        response_data = JSON.parse(response.body)
        expect(response_data["success"]).to be true
        expect(response_data["count"]).to eq(0)
        expect(response_data["message"]).to include("0 activities closed")
      end
    end
  end
end