# ABOUTME: RSpec tests for TYM-28 - Fix Mainbox Today's Works filtering to exclude past ongoing works
# ABOUTME: Tests that today's works endpoint only shows works starting today or later, not past ongoing works
require 'rails_helper'

RSpec.describe 'Api::V1::Works#today filtering', type: :request do
  let(:company) { create(:company, name: "Test Company", subdomain: "test") }
  let(:user) { create(:user, email: "<EMAIL>") }
  let(:contract) do
    contract = build(:contract, company: company, user: user, first_name: "Test", last_name: "User")
    allow(contract).to receive(:send_invitation).and_return(true)
    contract.save!
    contract
  end

  before do
    ActsAsTenant.current_tenant = company
    # Associate user with company via company_user_role for JWT helpers
    employee_role = create(:role, name: 'employee')
    create(:company_user_role, company: company, user: user, role: employee_role)
    contract # Ensure contract is created
  end

  def jwt_auth_headers
    token = jwt_token_for(user)
    auth_headers(token)
  end

  describe 'GET /api/v1/works/today' do
    let(:today) { Date.current }
    let(:yesterday) { today - 1.day }
    let(:tomorrow) { today + 1.day }

    context 'when filtering today\'s works' do
      let!(:work_starting_today) do
        work = Work.create!(
          title: "Work Starting Today",
          company: company,
          status: "scheduled",
          scheduled_start_date: today,
          scheduled_end_date: today + 2.days
        )
        WorkAssignment.create!(work: work, contract: contract, company: company, role: "worker")
        work
      end

      let!(:work_starting_tomorrow) do
        work = Work.create!(
          title: "Work Starting Tomorrow",
          company: company,
          status: "scheduled",
          scheduled_start_date: tomorrow,
          scheduled_end_date: tomorrow + 1.day
        )
        WorkAssignment.create!(work: work, contract: contract, company: company, role: "worker")
        work
      end

      let!(:ongoing_work_from_yesterday) do
        work = Work.create!(
          title: "Ongoing Work From Yesterday",
          company: company,
          status: "in_progress",
          scheduled_start_date: yesterday,
          scheduled_end_date: today + 1.day
        )
        WorkAssignment.create!(work: work, contract: contract, company: company, role: "worker")
        work
      end

      let!(:ongoing_work_from_last_week) do
        work = Work.create!(
          title: "Ongoing Work From Last Week",
          company: company,
          status: "in_progress",
          scheduled_start_date: today - 7.days,
          scheduled_end_date: nil # Open-ended work
        )
        WorkAssignment.create!(work: work, contract: contract, company: company, role: "worker")
        work
      end

      it 'FIXED TYM-28: correctly excludes past ongoing works and includes future works' do
        get '/api/v1/works/today', headers: jwt_auth_headers
        
        expect(response).to have_http_status(:success)
        response_data = JSON.parse(response.body)
        work_titles = response_data.map { |work| work["title"] }
        
        # Fixed behavior - correctly excludes past works and includes today+ works
        expect(work_titles).not_to include("Ongoing Work From Yesterday") 
        expect(work_titles).not_to include("Ongoing Work From Last Week")
        expect(work_titles).to include("Work Starting Today")
        expect(work_titles).to include("Work Starting Tomorrow")
        expect(response_data.length).to eq(2)
      end

      it 'includes works starting today and tomorrow' do
        get '/api/v1/works/today', headers: jwt_auth_headers
        
        response_data = JSON.parse(response.body)
        work_titles = response_data.map { |work| work["title"] }
        
        expect(work_titles).to include("Work Starting Today")
        expect(work_titles).to include("Work Starting Tomorrow")
      end

      it 'should preserve ordering by scheduled_start_date' do
        get '/api/v1/works/today', headers: jwt_auth_headers
        
        response_data = JSON.parse(response.body)
        work_titles = response_data.map { |work| work["title"] }
        
        # Should be ordered: today's work, then tomorrow's work
        expect(work_titles.first).to eq("Work Starting Today")
        expect(work_titles.last).to eq("Work Starting Tomorrow")
      end

      it 'should include work assignments data for display' do
        get '/api/v1/works/today', headers: jwt_auth_headers
        
        response_data = JSON.parse(response.body)
        
        response_data.each do |work_data|
          expect(work_data["work_assignments"]).to be_present
          expect(work_data["work_assignments"].first["contract"]).to be_present
          expect(work_data["work_assignments"].first["contract"]["first_name"]).to eq("Test")
        end
      end
    end

    context 'edge cases' do
      let!(:work_ending_today) do
        work = Work.create!(
          title: "Work Ending Today",
          company: company,
          status: "in_progress",
          scheduled_start_date: yesterday,
          scheduled_end_date: today
        )
        WorkAssignment.create!(work: work, contract: contract, company: company, role: "worker")
        work
      end

      it 'should NOT include works that started yesterday but end today' do
        get '/api/v1/works/today', headers: jwt_auth_headers
        
        response_data = JSON.parse(response.body)
        work_titles = response_data.map { |work| work["title"] }
        
        expect(work_titles).not_to include("Work Ending Today")
      end
    end
  end
end