# ABOUTME: RSpec tests for JWT authentication with invitation_accepted_at logic from TYM-38
# ABOUTME: Tests comprehensive scenarios for users with confirmed_at OR invitation_accepted_at timestamps

require 'rails_helper'

RSpec.describe 'JWT Authentication with Invitation Acceptance', type: :request do
  let(:company) { create(:company) }
  let(:password) { 'password123' }

  shared_examples 'successful JWT login' do
    it 'returns success status' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      expect(response).to have_http_status(:ok)
    end

    it 'returns access token and user data' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      json_response = JSON.parse(response.body)
      
      expect(json_response['success']).to be true
      expect(json_response['access_token']).to be_present
      expect(json_response['user']).to include(
        'id' => user.id,
        'email' => user.email,
        'company_id' => company.id
      )
    end

    it 'sets refresh token in HttpOnly cookie' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      expect(response.cookies['refresh_token']).to be_present
    end

    it 'returns valid JWT token' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      json_response = JSON.parse(response.body)
      token = json_response['access_token']
      
      decoded = JwtService.decode(token)
      expect(decoded).to include(
        'user_id' => user.id,
        'email' => user.email,
        'company_id' => company.id
      )
      expect(decoded['jti']).to be_present
    end

    it 'logs successful authentication' do
      allow(AuthHealthCheck).to receive(:log_auth_event).with('api_request', anything)
      
      expect(AuthHealthCheck).to receive(:log_auth_event).with(
        'jwt_login',
        hash_including(success: true, duration_ms: kind_of(Integer))
      )
      
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
    end
  end

  shared_examples 'failed authentication with forbidden status' do
    it 'returns forbidden status' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      expect(response).to have_http_status(:forbidden)
    end

    it 'returns email not confirmed error' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      json_response = JSON.parse(response.body)
      
      expect(json_response['error']).to eq(I18n.t('front.email_not_confirmed'))
      expect(json_response['email_not_confirmed']).to be true
      expect(json_response['email']).to eq(user.email)
    end

    it 'does not return tokens' do
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
      json_response = JSON.parse(response.body)
      
      expect(json_response['access_token']).to be_nil
      expect(json_response['refresh_token']).to be_nil
      expect(response.cookies['refresh_token']).to be_blank
    end

    it 'logs failed authentication' do
      allow(AuthHealthCheck).to receive(:log_auth_event).with('api_request', anything)
      
      expect(AuthHealthCheck).to receive(:log_auth_event).with(
        'jwt_login',
        hash_including(success: false, duration_ms: kind_of(Integer), error: 'Email not confirmed')
      )
      
      post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
    end
  end

  describe 'POST /api/v1/auth/jwt_login with invitation_accepted_at logic' do
    describe '✅ User with only confirmed_at can login (existing behavior)' do
      let(:user) { create(:user, password: password, confirmed_at: 1.day.ago, invitation_accepted_at: nil) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it_behaves_like 'successful JWT login'
    end

    describe '🆕 User with only invitation_accepted_at can login (new behavior)' do
      let(:user) { create(:user, password: password, confirmed_at: nil, invitation_accepted_at: 1.day.ago) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it_behaves_like 'successful JWT login'
    end

    describe '❌ User with neither confirmed_at nor invitation_accepted_at cannot login' do
      let(:user) { create(:user, password: password, confirmed_at: nil, invitation_accepted_at: nil) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it_behaves_like 'failed authentication with forbidden status'
    end

    describe '✅ User with both timestamps can login' do
      let(:user) { create(:user, password: password, confirmed_at: 2.days.ago, invitation_accepted_at: 1.day.ago) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it_behaves_like 'successful JWT login'
    end

    describe '❌ User with correct email but wrong password cannot login' do
      let(:user) { create(:user, password: password, confirmed_at: 1.day.ago) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it 'returns unauthorized status' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: 'wrong_password' }
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns invalid credentials error' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: 'wrong_password' }
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq(I18n.t('front.invalid_credentials'))
      end

      it 'does not return tokens' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: 'wrong_password' }
        json_response = JSON.parse(response.body)
        
        expect(json_response['access_token']).to be_nil
        expect(response.cookies['refresh_token']).to be_blank
      end

      it 'logs failed authentication with invalid credentials' do
        allow(AuthHealthCheck).to receive(:log_auth_event).with('api_request', anything)
        
        expect(AuthHealthCheck).to receive(:log_auth_event).with(
          'jwt_login',
          hash_including(success: false, duration_ms: kind_of(Integer), error: 'Invalid credentials')
        )
        
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: 'wrong_password' }
      end
    end

    describe '❌ Non-existent user cannot login' do
      it 'returns unauthorized status' do
        post '/api/v1/auth/jwt_login', params: { email: '<EMAIL>', password: password }
        expect(response).to have_http_status(:unauthorized)
      end

      it 'returns invalid credentials error' do
        post '/api/v1/auth/jwt_login', params: { email: '<EMAIL>', password: password }
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq(I18n.t('front.invalid_credentials'))
      end

      it 'does not return tokens' do
        post '/api/v1/auth/jwt_login', params: { email: '<EMAIL>', password: password }
        json_response = JSON.parse(response.body)
        
        expect(json_response['access_token']).to be_nil
        expect(response.cookies['refresh_token']).to be_blank
      end
    end

    describe 'Edge Cases' do
      describe 'User with invitation_accepted_at but confirmed_at: nil' do
        let(:user) { create(:user, password: password, confirmed_at: nil, invitation_accepted_at: 2.hours.ago) }

        before do
          create(:company_user_role, user: user, company: company, is_primary: true)
        end

        it_behaves_like 'successful JWT login'

        it 'specifically verifies user has no confirmed_at but has invitation_accepted_at' do
          expect(user.confirmed_at).to be_nil
          expect(user.invitation_accepted_at).to be_present
          
          post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
          expect(response).to have_http_status(:ok)
        end
      end

      describe 'User with confirmed_at but invitation_accepted_at: nil' do
        let(:user) { create(:user, password: password, confirmed_at: 3.hours.ago, invitation_accepted_at: nil) }

        before do
          create(:company_user_role, user: user, company: company, is_primary: true)
        end

        it_behaves_like 'successful JWT login'

        it 'specifically verifies user has confirmed_at but no invitation_accepted_at' do
          expect(user.confirmed_at).to be_present
          expect(user.invitation_accepted_at).to be_nil
          
          post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
          expect(response).to have_http_status(:ok)
        end
      end

      describe 'User with expired-like timestamps (edge case validation)' do
        let(:user) { create(:user, password: password, confirmed_at: nil, invitation_accepted_at: 30.days.ago) }

        before do
          create(:company_user_role, user: user, company: company, is_primary: true)
        end

        it_behaves_like 'successful JWT login'

        it 'allows login with old invitation_accepted_at timestamp' do
          expect(user.invitation_accepted_at).to be < 7.days.ago
          
          post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
          expect(response).to have_http_status(:ok)
        end
      end
    end

    describe 'Response Body Structure Validation' do
      let(:user) { create(:user, password: password, invitation_accepted_at: 1.day.ago) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it 'returns correct success response structure' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        json_response = JSON.parse(response.body)
        
        expect(json_response).to include(
          'success' => true,
          'access_token' => kind_of(String),
          'expires_in' => JwtService::ACCESS_TOKEN_EXPIRATION_DURATION.to_i,
          'user' => hash_including(
            'id' => user.id,
            'email' => user.email,
            'company_id' => company.id
          ),
          'session_id' => kind_of(String)
        )
      end

      it 'returns correct forbidden response structure for unconfirmed user' do
        unconfirmed_user = create(:user, password: password, confirmed_at: nil, invitation_accepted_at: nil)
        create(:company_user_role, user: unconfirmed_user, company: company, is_primary: true)
        
        post '/api/v1/auth/jwt_login', params: { email: unconfirmed_user.email, password: password }
        json_response = JSON.parse(response.body)
        
        expect(json_response).to include(
          'error' => I18n.t('front.email_not_confirmed'),
          'email_not_confirmed' => true,
          'email' => unconfirmed_user.email
        )
        expect(json_response).not_to have_key('success')
        expect(json_response).not_to have_key('access_token')
      end
    end

    describe 'JWT Token Generation and Validation' do
      let(:user) { create(:user, password: password, invitation_accepted_at: 1.day.ago) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it 'generates JWT token with correct payload structure' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        json_response = JSON.parse(response.body)
        token = json_response['access_token']
        
        decoded_payload = JwtService.decode(token)
        
        expect(decoded_payload).to include(
          'user_id' => user.id,
          'email' => user.email,
          'company_id' => company.id,
          'jti' => kind_of(String),
          'iat' => kind_of(Integer),
          'exp' => kind_of(Integer),
          'token_type' => 'access'
        )
      end

      it 'generates JWT token that passes validation' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        json_response = JSON.parse(response.body)
        token = json_response['access_token']
        
        # Verify token can be successfully decoded
        expect { JwtService.decode(token) }.not_to raise_error
        
        # Verify token is not expired
        decoded_payload = JwtService.decode(token)
        expect(decoded_payload['exp']).to be > Time.current.to_i
      end

      it 'sets refresh token cookie with secure attributes' do
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        
        refresh_token_cookie = response.cookies['refresh_token']
        expect(refresh_token_cookie).to be_present
        
        # Note: HttpOnly and Secure attributes can't be directly tested in request specs
        # but they are set in SecureCookieHelper.set_refresh_token_cookie
      end
    end

    describe 'HTTP Status Code Validation' do
      it 'returns 200 for successful login with confirmed_at' do
        user = create(:user, password: password, confirmed_at: 1.day.ago)
        create(:company_user_role, user: user, company: company, is_primary: true)
        
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        expect(response.status).to eq(200)
      end

      it 'returns 200 for successful login with invitation_accepted_at' do
        user = create(:user, password: password, invitation_accepted_at: 1.day.ago)
        create(:company_user_role, user: user, company: company, is_primary: true)
        
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        expect(response.status).to eq(200)
      end

      it 'returns 403 for denied login (no confirmation)' do
        user = create(:user, password: password, confirmed_at: nil, invitation_accepted_at: nil)
        create(:company_user_role, user: user, company: company, is_primary: true)
        
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        expect(response.status).to eq(403)
      end

      it 'returns 401 for invalid credentials' do
        user = create(:user, password: password, confirmed_at: 1.day.ago)
        
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: 'wrong' }
        expect(response.status).to eq(401)
      end
    end

    describe 'CI Pipeline Compatibility' do
      let(:user) { create(:user, password: password, invitation_accepted_at: 1.day.ago) }

      before do
        create(:company_user_role, user: user, company: company, is_primary: true)
      end

      it 'runs successfully in test environment' do
        expect(Rails.env).to eq('test')
        
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        expect(response).to have_http_status(:ok)
      end

      it 'does not depend on external services' do
        # This test verifies that authentication doesn't require external dependencies
        # All authentication logic should work with just the database and Redis
        post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
        expect(response).to have_http_status(:ok)
      end

      it 'handles database transaction rollbacks properly' do
        # Test that the test can be run multiple times (important for CI)
        3.times do
          post '/api/v1/auth/jwt_login', params: { email: user.email, password: password }
          expect(response).to have_http_status(:ok)
        end
      end
    end
  end
end