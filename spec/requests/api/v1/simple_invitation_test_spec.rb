# ABOUTME: Simple test to verify invitation_accepted_at authentication logic
# ABOUTME: Minimal test to isolate TYM-38 authentication changes

require 'rails_helper'

RSpec.describe 'Simple Invitation Authentication Test', type: :request do
  let(:company) { create(:company) }
  
  describe 'POST /api/v1/auth/jwt_login - invitation_accepted_at test' do
    it 'allows login for user with invitation_accepted_at but no confirmed_at' do
      user = create(:user, 
        email: '<EMAIL>',
        password: 'testpass123',
        confirmed_at: nil,
        invitation_accepted_at: 1.day.ago
      )
      create(:company_user_role, user: user, company: company, is_primary: true)
      
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'testpass123'
      }
      
      expect(response).to have_http_status(:ok)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
      expect(json_response['access_token']).to be_present
    end
    
    it 'blocks login for user with neither confirmed_at nor invitation_accepted_at' do
      user = create(:user, 
        email: '<EMAIL>',
        password: 'testpass123',
        confirmed_at: nil,
        invitation_accepted_at: nil
      )
      create(:company_user_role, user: user, company: company, is_primary: true)
      
      post '/api/v1/auth/jwt_login', params: {
        email: user.email,
        password: 'testpass123'
      }
      
      expect(response).to have_http_status(:forbidden)
      json_response = JSON.parse(response.body)
      expect(json_response['error']).to eq(I18n.t('front.email_not_confirmed'))
      expect(json_response['email_not_confirmed']).to be true
    end
  end
end