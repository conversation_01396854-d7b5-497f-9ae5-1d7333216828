# spec/requests/api/v1/companies_controller_spec.rb
require 'rails_helper'

RSpec.describe Api::V1::CompaniesController, type: :request do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:company_a) { create(:company, name: 'Company A') }
  let(:company_b) { create(:company, name: 'Company B') }
  let(:company_c) { create(:company, name: 'Company C') }
  let(:owner_role) { create(:role, name: 'owner') }
  let(:employee_role) { create(:role, name: 'employee') }

  # Create company user roles for testing
  let!(:user_company_a_role) do
    create(:company_user_role, user: user, company: company_a, role: owner_role, is_primary: true, active: true)
  end
  let!(:user_company_b_role) do
    create(:company_user_role, user: user, company: company_b, role: employee_role, is_primary: false, active: true)
  end
  let!(:other_user_company_c_role) do
    create(:company_user_role, user: other_user, company: company_c, role: owner_role, is_primary: true, active: true)
  end

  before do
    # Ensure Redis is available for JWT revocation tests
    Redis.current.with { |conn| conn.flushdb }
  end

  describe 'POST /api/v1/companies/switch_company' do
    context 'with JWT authentication' do
      let(:jwt_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{jwt_token}" } }

      context 'when switching to an accessible company' do
        it 'returns success with new JWT token containing updated company_id' do
          post '/api/v1/companies/switch_company', 
               params: { company_id: company_b.id },
               headers: auth_headers

          expect(response).to have_http_status(:ok)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
          expect(json_response['token']).to be_present
          expect(json_response['company']['id']).to eq(company_b.id)
          expect(json_response['company']['name']).to eq('Company B')
          expect(json_response['message']).to include('Company B')

          # Verify the new JWT contains the updated company_id
          decoded_payload = JwtService.decode(json_response['token'])
          expect(decoded_payload[:company_id]).to eq(company_b.id)
          expect(decoded_payload[:user_id]).to eq(user.id)
        end

        it 'updates the user primary company' do
          expect {
            post '/api/v1/companies/switch_company', 
                 params: { company_id: company_b.id },
                 headers: auth_headers
          }.to change { user.reload.primary_company }.from(company_a).to(company_b)
        end

        it 'sets the tenant context for the request' do
          post '/api/v1/companies/switch_company', 
               params: { company_id: company_b.id },
               headers: auth_headers

          expect(response).to have_http_status(:ok)
          # Verify response instead of mocking internal calls
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be true
        end

        it 'logs the company switch event' do
          # Capture log output instead of mocking
          allow(Rails.logger).to receive(:info)
          
          post '/api/v1/companies/switch_company', 
               params: { company_id: company_b.id },
               headers: auth_headers

          expect(Rails.logger).to have_received(:info).with(match(/Company switch: User #{user.id} switched to company #{company_b.id}/))
        end
      end

      context 'when switching to an inaccessible company' do
        it 'returns forbidden when user has no access to the company' do
          post '/api/v1/companies/switch_company', 
               params: { company_id: company_c.id },
               headers: auth_headers

          expect(response).to have_http_status(:forbidden)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['error']).to eq('Company not found or access denied')
          expect(json_response['token']).to be_nil
        end

        it 'returns forbidden when company does not exist' do
          post '/api/v1/companies/switch_company', 
               params: { company_id: 99999 },
               headers: auth_headers

          expect(response).to have_http_status(:forbidden)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['error']).to eq('Company not found or access denied')
        end

        it 'does not update primary company when access is denied' do
          expect {
            post '/api/v1/companies/switch_company', 
                 params: { company_id: company_c.id },
                 headers: auth_headers
          }.not_to change { user.reload.primary_company }
        end
      end

      context 'with invalid parameters' do
        it 'returns bad request when company_id is missing' do
          post '/api/v1/companies/switch_company', 
               params: {},
               headers: auth_headers

          expect(response).to have_http_status(:bad_request)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['error']).to eq('Company ID is required')
        end

        it 'returns bad request when company_id is blank' do
          post '/api/v1/companies/switch_company', 
               params: { company_id: '' },
               headers: auth_headers

          expect(response).to have_http_status(:bad_request)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['error']).to eq('Company ID is required')
        end
      end

      context 'with inactive company user role' do
        before do
          user_company_b_role.update!(active: false)
        end

        it 'returns forbidden when user role is inactive' do
          post '/api/v1/companies/switch_company', 
               params: { company_id: company_b.id },
               headers: auth_headers

          expect(response).to have_http_status(:forbidden)
          
          json_response = JSON.parse(response.body)
          expect(json_response['success']).to be false
          expect(json_response['error']).to eq('Company not found or access denied')
        end
      end
    end

    context 'with session authentication fallback' do
      before do
        sign_in user
      end

      it 'works with session authentication when JWT is not provided' do
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id }

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        expect(json_response['token']).to be_present
        expect(json_response['company']['id']).to eq(company_b.id)
      end
    end

    context 'without authentication' do
      it 'returns unauthorized' do
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id }

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context 'with expired JWT token' do
      let(:expired_payload) { user.jwt_payload.merge(exp: 1.hour.ago.to_i) }
      let(:expired_token) { JWT.encode(expired_payload, Rails.application.credentials.jwt_secret, 'HS256') }
      let(:expired_headers) { { 'Authorization' => "Bearer #{expired_token}" } }

      before do
        sign_in user  # Provide session fallback
      end

      it 'falls back to session authentication' do
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: expired_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end

    context 'with revoked JWT token' do
      let(:revoked_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:revoked_headers) { { 'Authorization' => "Bearer #{revoked_token}" } }

      before do
        # Revoke the token
        decoded = JwtService.decode(revoked_token)
        revocation_strategy = JwtRevocationStrategy.new
        revocation_strategy.revoke_jwt(decoded, user)
        sign_in user  # Provide session fallback
      end

      it 'falls back to session authentication' do
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: revoked_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end

    context 'error handling' do
      it 'handles JWT generation errors gracefully' do
        jwt_token = JwtService.encode_access_token(user.jwt_payload)
        auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
        
        # Mock JWT service to raise an error during the switch_company action
        allow(JwtService).to receive(:encode_access_token).and_call_original
        allow(JwtService).to receive(:encode_access_token).with(kind_of(Hash)).and_raise(StandardError.new('JWT generation failed'))
        
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: auth_headers

        expect(response).to have_http_status(:internal_server_error)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be false
        expect(json_response['error']).to eq('Internal server error during company switch')
      end

      it 'logs errors for debugging' do
        jwt_token = JwtService.encode_access_token(user.jwt_payload)
        auth_headers = { 'Authorization' => "Bearer #{jwt_token}" }
        
        allow(JwtService).to receive(:encode_access_token).and_call_original
        allow(JwtService).to receive(:encode_access_token).with(kind_of(Hash)).and_raise(StandardError.new('JWT generation failed'))
        expect(Rails.logger).to receive(:error).with(/Company switch error: JWT generation failed/)
        
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: auth_headers
      end
    end
  end

  describe 'Integration: End-to-End Company Switch Flow' do
    context 'with JWT authentication' do
      let(:jwt_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{jwt_token}" } }

      it 'correctly updates JWT token and maintains tenant context for subsequent requests' do
        # 1. Verify initial state - user's primary company is company_a
        expect(user.primary_company).to eq(company_a)
        
        # Decode initial token to verify initial company context
        initial_payload = JwtService.decode(jwt_token)
        expect(initial_payload[:company_id]).to eq(company_a.id)
        expect(initial_payload[:user_id]).to eq(user.id)

        # 2. Switch to company_b via JWT endpoint
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: auth_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
        new_token = json_response['token']
        expect(new_token).to be_present
        expect(new_token).not_to eq(jwt_token) # New token is different

        # 3. Verify new token contains updated company context
        new_payload = JwtService.decode(new_token)
        expect(new_payload[:company_id]).to eq(company_b.id)
        expect(new_payload[:user_id]).to eq(user.id) # User remains the same
        expect(new_payload[:jti]).not_to eq(initial_payload[:jti]) # New JTI

        # 4. Verify user's primary company was updated in database
        expect(user.reload.primary_company).to eq(company_b)

        # 5. Use new token for subsequent API request to verify tenant context
        new_auth_headers = { 'Authorization' => "Bearer #{new_token}" }
        
        get '/api/v1/companies', headers: new_auth_headers
        
        expect(response).to have_http_status(:ok)
        companies_response = JSON.parse(response.body)
        
        # Verify the response shows the correct current tenant
        expect(companies_response['current_tenant']).to eq(company_b.id)
        
        # Verify user still has access to both companies
        company_ids = companies_response['company_user_roles'].map { |cur| cur['company']['id'] }
        expect(company_ids).to contain_exactly(company_a.id, company_b.id)
      end

      it 'old token still works until expiry (natural expiry strategy)' do
        # Switch company to get new token
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: auth_headers

        new_token = JSON.parse(response.body)['token']

        # Old token should still work (until natural expiry)
        get '/api/v1/companies', headers: auth_headers
        expect(response).to have_http_status(:ok)

        # New token should also work
        new_auth_headers = { 'Authorization' => "Bearer #{new_token}" }
        get '/api/v1/companies', headers: new_auth_headers
        expect(response).to have_http_status(:ok)

        # But they should have different company contexts in their payloads
        old_payload = JwtService.decode(jwt_token)
        new_payload = JwtService.decode(new_token)
        expect(old_payload[:company_id]).to eq(company_a.id)
        expect(new_payload[:company_id]).to eq(company_b.id)
      end

      it 'handles company switch chain correctly' do
        # Initial state: company_a (primary)
        expect(user.primary_company).to eq(company_a)

        # Switch A → B
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_b.id },
             headers: auth_headers

        token_b = JSON.parse(response.body)['token']
        payload_b = JwtService.decode(token_b)
        expect(payload_b[:company_id]).to eq(company_b.id)

        # Switch B → A using new token
        headers_b = { 'Authorization' => "Bearer #{token_b}" }
        post '/api/v1/companies/switch_company', 
             params: { company_id: company_a.id },
             headers: headers_b

        token_a_new = JSON.parse(response.body)['token']
        payload_a_new = JwtService.decode(token_a_new)
        expect(payload_a_new[:company_id]).to eq(company_a.id)

        # Verify user's primary company reflects final switch
        expect(user.reload.primary_company).to eq(company_a)

        # All three tokens should be different
        expect([jwt_token, token_b, token_a_new].uniq.length).to eq(3)
      end
    end
  end

  describe 'GET /api/v1/companies' do
    context 'with JWT authentication' do
      let(:jwt_token) { JwtService.encode_access_token(user.jwt_payload) }
      let(:auth_headers) { { 'Authorization' => "Bearer #{jwt_token}" } }

      it 'returns list of accessible companies' do
        get '/api/v1/companies', headers: auth_headers

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response['company_user_roles']).to be_an(Array)
        expect(json_response['company_user_roles'].length).to eq(2)
        
        company_ids = json_response['company_user_roles'].map { |cur| cur['company']['id'] }
        expect(company_ids).to contain_exactly(company_a.id, company_b.id)
        expect(company_ids).not_to include(company_c.id)
      end

      it 'includes current tenant information' do
        get '/api/v1/companies', headers: auth_headers

        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('current_tenant')
      end

      it 'excludes inactive company user roles' do
        user_company_b_role.update!(active: false)
        
        get '/api/v1/companies', headers: auth_headers

        json_response = JSON.parse(response.body)
        expect(json_response['company_user_roles'].length).to eq(1)
        
        company_ids = json_response['company_user_roles'].map { |cur| cur['company']['id'] }
        expect(company_ids).to contain_exactly(company_a.id)
      end
    end

    context 'without authentication' do
      it 'returns unauthorized' do
        get '/api/v1/companies'

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

end