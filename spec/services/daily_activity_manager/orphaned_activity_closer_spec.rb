# ABOUTME: RSpec test for DailyActivityManager::OrphanedActivityCloser service
# ABOUTME: Tests automatic closing of orphaned daily activities from previous days

require 'rails_helper'

RSpec.describe DailyActivityManager::OrphanedActivityCloser, type: :service do
  include ActiveSupport::Testing::TimeHelpers
  let(:company) { create(:company) }
  let(:contract) { create(:contract, :with_user, :skip_invitation, company: company) }
  let(:user) { contract.user }

  describe '.call' do
    context 'with orphaned activities from previous days' do
      it 'closes activities with daily_log_id nil from previous days' do
        # Activity from yesterday without daily_log association
        yesterday_activity = nil
        travel_to(1.day.ago) do
          yesterday_activity = create(:daily_activity, 
            user: user, 
            company: company, 
            contract: contract,
            daily_log_id: nil,
            end_time: nil,
            start_time: Time.current
          )
        end

        # Activity from today should NOT be closed
        today_activity = create(:daily_activity,
          user: user,
          company: company, 
          contract: contract,
          daily_log_id: nil,
          end_time: nil,
          start_time: Time.current
        )

        result = described_class.call

        expect(result).to include(yesterday_activity)
        expect(result).not_to include(today_activity)
        expect(yesterday_activity.reload.end_time).to be_present
        expect(today_activity.reload.end_time).to be_nil
      end

      it 'closes activities with daily_log_id present from previous days' do
        daily_log = nil
        yesterday_activity = nil
        
        travel_to(1.day.ago) do
          daily_log = create(:daily_log, user: user, contract: contract, company: company, end_time: nil)
          yesterday_activity = create(:daily_activity,
            user: user,
            company: company,
            contract: contract, 
            daily_log: daily_log,
            end_time: nil,
            start_time: Time.current
          )
        end

        result = described_class.call

        expect(result).to include(yesterday_activity)
        expect(yesterday_activity.reload.end_time).to be_present
      end

      it 'handles activities from multiple previous days' do
        activities = []
        
        # Activity from 3 days ago
        travel_to(3.days.ago) do
          activities << create(:daily_activity,
            user: user,
            company: company,
            contract: contract,
            daily_log_id: nil,
            end_time: nil,
            start_time: Time.current
          )
        end

        # Activity from yesterday  
        travel_to(1.day.ago) do
          activities << create(:daily_activity,
            user: user,
            company: company,
            contract: contract,
            daily_log_id: nil,
            end_time: nil,
            start_time: Time.current
          )
        end

        result = described_class.call

        expect(result).to contain_exactly(*activities)
        activities.each do |activity|
          expect(activity.reload.end_time).to be_present
        end
      end
    end

    context 'with no orphaned activities' do
      it 'returns empty array when no activities from previous days' do
        # Only today's activity
        create(:daily_activity,
          user: user,
          company: company,
          contract: contract,
          daily_log_id: nil,
          end_time: nil,
          start_time: Time.current
        )

        result = described_class.call

        expect(result).to be_empty
      end

      it 'returns empty array when previous day activities are already closed' do
        travel_to(1.day.ago) do
          create(:daily_activity,
            user: user,
            company: company,
            contract: contract,
            daily_log_id: nil,
            end_time: Time.current,
            start_time: Time.current - 1.hour
          )
        end

        result = described_class.call

        expect(result).to be_empty
      end
    end

    context 'multi-tenant safety' do
      it 'only closes activities for current tenant company' do
        other_company = create(:company)
        other_contract = create(:contract, :with_user, :skip_invitation, company: other_company)
        
        yesterday_activity_this_company = nil
        yesterday_activity_other_company = nil
        
        travel_to(1.day.ago) do
          yesterday_activity_this_company = create(:daily_activity,
            user: user,
            company: company,
            contract: contract,
            daily_log_id: nil,
            end_time: nil,
            start_time: Time.current
          )
          
          yesterday_activity_other_company = create(:daily_activity,
            user: other_contract.user,
            company: other_company,
            contract: other_contract,
            daily_log_id: nil,
            end_time: nil,
            start_time: Time.current
          )
        end

        # Set current tenant to company
        ActsAsTenant.with_tenant(company) do
          result = described_class.call
          
          expect(result).to include(yesterday_activity_this_company)
          expect(result).not_to include(yesterday_activity_other_company)
        end

        expect(yesterday_activity_this_company.reload.end_time).to be_present
        expect(yesterday_activity_other_company.reload.end_time).to be_nil
      end
    end
  end
end