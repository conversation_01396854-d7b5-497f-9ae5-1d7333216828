# ABOUTME: RSpec test for DailyActivityManager::AutoCloser service
# ABOUTME: Tests automatic closing of orphaned daily activities when daily log is closed

require 'rails_helper'

RSpec.describe DailyActivityManager::AutoCloser, type: :service do
  include ActiveSupport::Testing::TimeHelpers
  let(:company) { create(:company) }
  let(:contract) { create(:contract, :with_user, :skip_invitation, company: company) }
  let(:user) { contract.user }
  let(:work) { create(:work, company: company) }
  let(:work_assignment) { create(:work_assignment, work: work, contract: contract) }
  let(:daily_log) { create(:daily_log, user: user, contract: contract, company: company, end_time: nil) }

  # Helper method to create activities for open daily log
  def create_activity_for_open_log(attributes = {})
    create(:daily_activity, {
      daily_log: daily_log,
      user: user,
      company: company,
      contract: contract
    }.merge(attributes))
  end

  describe '.call' do
    context 'when daily log is still open (end_time is nil)' do
      it 'returns empty array and does not close activities' do
        # Create open daily log
        daily_log.update!(end_time: nil)
        
        # Create open activities
        activity1 = create(:daily_activity, daily_log: daily_log, user: user, company: company, contract: contract, end_time: nil)
        activity2 = create(:daily_activity, daily_log: daily_log, user: user, company: company, contract: contract, end_time: nil)
        
        result = described_class.call(daily_log: daily_log)
        
        expect(result).to be_empty
        expect(activity1.reload.end_time).to be_nil
        expect(activity2.reload.end_time).to be_nil
      end
    end

    context 'when daily log is closed (end_time is present)' do
      let(:close_time) { Time.current }

      context 'with open activities' do
        it 'closes all open activities with daily log end_time' do
          # Create activities before closing the log
          open_activity1 = create_activity_for_open_log(end_time: nil)
          open_activity2 = create_activity_for_open_log(end_time: nil)
          closed_activity = create_activity_for_open_log(end_time: 1.hour.ago)
          
          # Close the daily log
          daily_log.update!(end_time: close_time)
          
          # Reload to get the updated state
          updated_daily_log = daily_log.reload
          
          result = described_class.call(daily_log: updated_daily_log)
          
          expect(result).to contain_exactly(open_activity1, open_activity2)
          expect(open_activity1.reload.end_time).to be_within(1.second).of(close_time)
          expect(open_activity2.reload.end_time).to be_within(1.second).of(close_time)
          expect(closed_activity.reload.end_time).to be_within(1.second).of(1.hour.ago) # Should not change
        end

        it 'uses custom close_time when provided' do
          # Create activities before closing the log
          open_activity1 = create_activity_for_open_log(end_time: nil)
          open_activity2 = create_activity_for_open_log(end_time: nil)
          
          # Close the daily log
          daily_log.update!(end_time: close_time)
          
          # Reload to get the updated state
          updated_daily_log = daily_log.reload
          custom_time = 30.minutes.ago
          result = described_class.call(daily_log: updated_daily_log, close_time: custom_time)
          
          expect(result).to contain_exactly(open_activity1, open_activity2)
          expect(open_activity1.reload.end_time).to be_within(1.second).of(custom_time)
          expect(open_activity2.reload.end_time).to be_within(1.second).of(custom_time)
        end

        it 'handles work activities correctly' do
          work_activity = create_activity_for_open_log(
            work: work, 
            work_assignment: work_assignment,
            activity_type: 'work_at_location',
            end_time: nil
          )
          
          # Close the daily log
          daily_log.update!(end_time: close_time)
          
          updated_daily_log = daily_log.reload
          result = described_class.call(daily_log: updated_daily_log)
          
          expect(result).to include(work_activity)
          expect(work_activity.reload.end_time).to be_within(1.second).of(close_time)
        end

        it 'handles other activities correctly' do
          other_activity = create_activity_for_open_log(
            activity_type: 'regular',
            description: 'Other activity',
            end_time: nil
          )
          
          # Close the daily log
          daily_log.update!(end_time: close_time)
          
          updated_daily_log = daily_log.reload
          result = described_class.call(daily_log: updated_daily_log)
          
          expect(result).to include(other_activity)
          expect(other_activity.reload.end_time).to be_within(1.second).of(close_time)
        end
      end

      context 'with no open activities' do
        it 'returns empty array' do
          # Create only closed activities
          create_activity_for_open_log(end_time: 1.hour.ago)
          
          # Close the daily log
          daily_log.update!(end_time: close_time)
          
          updated_daily_log = daily_log.reload
          result = described_class.call(daily_log: updated_daily_log)
          
          expect(result).to be_empty
        end
      end

      context 'with no activities at all' do
        it 'returns empty array' do
          # Close the daily log
          daily_log.update!(end_time: close_time)
          
          updated_daily_log = daily_log.reload
          result = described_class.call(daily_log: updated_daily_log)
          
          expect(result).to be_empty
        end
      end
    end

    context 'error handling' do
      let(:close_time) { Time.current }

      it 'handles database errors gracefully' do
        create_activity_for_open_log(end_time: nil)
        
        # Close the daily log
        daily_log.update!(end_time: close_time)
        
        # Mock a database error
        allow(DailyActivity).to receive(:where).and_raise(ActiveRecord::StatementInvalid.new('Database error'))
        
        updated_daily_log = daily_log.reload
        expect { described_class.call(daily_log: updated_daily_log) }.to raise_error(ActiveRecord::StatementInvalid)
      end

      it 'maintains transaction integrity' do
        activity1 = create_activity_for_open_log(end_time: nil)
        activity2 = create_activity_for_open_log(end_time: nil)
        
        # Close the daily log
        daily_log.update!(end_time: close_time)
        
        # Mock update_all to fail for transaction test
        relation_double = double('relation', empty?: false, to_a: [activity1, activity2])
        allow(DailyActivity).to receive(:where).and_return(relation_double)
        allow(relation_double).to receive(:update_all).and_raise(ActiveRecord::StatementInvalid.new('Transaction failed'))
        
        # Since update_all can't really fail in the way we need for testing transactions,
        # we'll just verify the method calls are made correctly
        expect(DailyActivity).to receive(:where).with(
          daily_log_id: daily_log.id,
          end_time: nil
        )
        
        updated_daily_log = daily_log.reload
        expect { described_class.call(daily_log: updated_daily_log) }.to raise_error(ActiveRecord::StatementInvalid)
      end
    end
  end

  describe '#call' do
    let(:close_time) { Time.current }

    it 'uses daily_log end_time when close_time is nil' do
      activity = create_activity_for_open_log(end_time: nil)
      
      # Close the daily log
      daily_log.update!(end_time: close_time)
      
      instance = described_class.new(daily_log: daily_log.reload, close_time: nil)
      result = instance.call
      
      expect(activity.reload.end_time).to be_within(1.second).of(daily_log.reload.end_time)
    end

    it 'falls back to Time.current when both end_time and close_time are nil' do
      activity = create_activity_for_open_log(end_time: nil)
      
      # Keep daily_log open (end_time remains nil)
      instance = described_class.new(daily_log: daily_log.reload, close_time: nil)
      
      freeze_time do
        result = instance.call
        
        expect(result).to be_empty # Should not close activities when daily_log is open
      end
    end
  end
end