# ABOUTME: Feature test for event status display - vacation events show status, others don't
# ABOUTME: Tests the EventShow component's conditional status display based on event type

require 'rails_helper'

RSpec.describe 'Event Status Display', type: :feature, js: true do
  let(:user) { create(:user) }
  let(:company) { user.companies.first }
  let(:contract) { create(:contract, user: user, company: company) }
  
  before do
    sign_in_test_user
    navigate_to('events')
  end

  describe 'Vacation events status display' do
    let(:vacation_event_pending) { create(:event, event_type: 'vacation', status: 'pending', contract: contract, company: company, start_time: Date.current + 1.day) }
    let(:vacation_event_approved) { create(:event, event_type: 'vacation', status: 'approved', contract: contract, company: company, start_time: Date.current + 2.days) }

    it 'shows pending status badge for vacation events' do
      vacation_event_pending

      visit current_path
      
      # Should show pending status badge
      expect(page).to have_css('.status-badge', text: 'Čeká na schválení', wait: 5)
      
      # Should show bell icon for pending
      expect(page).to have_css('[data-lucide="bell-ring"]')
    end

    it 'shows approved check icon for vacation events' do
      vacation_event_approved

      visit current_path
      
      # Should show approved status icon  
      expect(page).to have_css('.status-icon [data-lucide="check-check"]', wait: 5)
    end
  end

  describe 'Non-vacation events status display' do
    let(:sick_event) { create(:event, event_type: 'illness', status: 'approved', contract: contract, company: company, start_time: Date.current + 1.day) }
    let(:family_sick_event) { create(:event, event_type: 'family_sick', status: 'approved', contract: contract, company: company, start_time: Date.current + 2.days) }
    let(:day_care_event) { create(:event, event_type: 'day_care', status: 'approved', contract: contract, company: company, start_time: Date.current + 3.days) }
    let(:other_event) { create(:event, event_type: 'other', status: 'approved', contract: contract, company: company, start_time: Date.current + 4.days) }
    let(:travel_event) { create(:event, event_type: 'travel', status: 'approved', contract: contract, company: company, start_time: Date.current + 5.days) }

    it 'does not show status badge for sick events' do
      sick_event

      visit current_path
      
      # Should NOT show status badge
      expect(page).not_to have_css('.status-badge', wait: 5)
      
      # Should NOT show approved check icon
      expect(page).not_to have_css('.status-icon [data-lucide="check-check"]')
      
      # Should show regular user icon (not bell)
      expect(page).to have_css('[data-lucide="user"]')
      expect(page).not_to have_css('[data-lucide="bell-ring"]')
    end

    it 'does not show status display for family sick events' do
      family_sick_event

      visit current_path
      
      expect(page).not_to have_css('.status-badge', wait: 5)
      expect(page).not_to have_css('.status-icon [data-lucide="check-check"]')
    end

    it 'does not show status display for day care events' do
      day_care_event

      visit current_path
      
      expect(page).not_to have_css('.status-badge', wait: 5)
      expect(page).not_to have_css('.status-icon [data-lucide="check-check"]')
    end

    it 'does not show status display for other events' do
      other_event

      visit current_path
      
      expect(page).not_to have_css('.status-badge', wait: 5)
      expect(page).not_to have_css('.status-icon [data-lucide="check-check"]')
    end

    it 'does not show status display for travel events' do
      travel_event

      visit current_path
      
      expect(page).not_to have_css('.status-badge', wait: 5)
      expect(page).not_to have_css('.status-icon [data-lucide="check-check"]')
    end
  end

  describe 'Rejected events styling' do
    let(:rejected_vacation) { create(:event, event_type: 'vacation', status: 'rejected', contract: contract, company: company, start_time: Date.current + 1.day) }
    let(:rejected_sick) { create(:event, event_type: 'illness', status: 'rejected', contract: contract, company: company, start_time: Date.current + 2.days) }

    it 'shows rejection styling for all event types' do
      rejected_vacation
      rejected_sick

      visit current_path
      
      # Both should show rejection styling (line-through text, rejected background)
      expect(page).to have_css('.event-rejected', count: 2, wait: 5)
      expect(page).to have_css('.rejected-text', count: 2)
    end
  end
end