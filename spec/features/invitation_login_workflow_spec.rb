# ABOUTME: Critical test for TYM-40 invitation login bug fix
# ABOUTME: Verifies users can login immediately after accepting company invitations

require 'rails_helper'

RSpec.describe 'Invitation Login Workflow', type: :feature, js: true do
  let!(:company) { create(:company) }
  let!(:sender) { create(:user) }
  let(:user_email) { '<EMAIL>' }
  let(:user_password) { 'password123' }
  
  # Create user without confirmed_at or invitation_accepted_at timestamps
  let!(:invited_user) do
    create(:user, 
           email: user_email, 
           password: user_password,
           confirmed_at: nil,
           invitation_accepted_at: nil)
  end
  
  let!(:invitation) do
    Invitation.create_jwt_invitation!(
      email: user_email,
      company: company,
      sender: sender,
      first_name: 'Test',
      last_name: 'User'
    )
  end
  
  before do
    # Ensure Rails server is running for API calls
    wait_for_vite_assets
  end
  
  describe 'Complete invitation acceptance and login workflow' do
    context 'when user accepts invitation and tries to login immediately' do
      it 'allows user to login after accepting invitation' do
        # Step 1: Simulate invitation acceptance
        # This would typically happen through InvitationService.accept_company_connection
        invitation_service = InvitationService.new
        result = invitation_service.accept_company_connection(
          invited_user, 
          company.id, 
          sender.id, 
          invitation.unique_token
        )
        
        expect(result[:success]).to be true
        
        # Step 2: Verify CompanyUserRole was created
        expect(invited_user.company_user_roles.where(company: company)).to exist
        
        # Step 3: CRITICAL TEST - User should be able to login immediately
        # This is the main bug: users can't login after accepting invitations
        visit new_user_session_path(locale: 'cs')
        wait_for_vite_assets
        
        fill_in 'user[email]', with: user_email
        fill_in 'user[password]', with: user_password
        click_button 'Sign in'
        
        # Should be able to login successfully (this will fail before fix)
        expect(page).to have_content('Signed in successfully')
        expect(current_path).not_to include('sign_in')
        
        # Additional verification: user should have invitation_accepted_at set
        invited_user.reload
        expect(invited_user.invitation_accepted_at).to be_present
        expect(invited_user.invitation_accepted_at).to be_within(5.seconds).of(Time.current)
      end
      
      it 'blocks login before invitation is accepted' do
        # Verify the problem exists: user cannot login before accepting invitation
        visit new_user_session_path(locale: 'cs')
        wait_for_vite_assets
        
        fill_in 'user[email]', with: user_email
        fill_in 'user[password]', with: user_password
        click_button 'Sign in'
        
        # Should be blocked with 403 Forbidden equivalent
        expect(page).to have_content('E-mail nebyl potvrzen')
        expect(current_path).to include('sign_in')
        
        # Verify user doesn't have the required timestamps
        invited_user.reload
        expect(invited_user.confirmed_at).to be_nil
        expect(invited_user.invitation_accepted_at).to be_nil
      end
    end
    
    context 'JWT API authentication after invitation acceptance' do
      it 'allows API access after invitation acceptance' do
        # Accept invitation first
        invitation_service = InvitationService.new
        result = invitation_service.accept_company_connection(
          invited_user, 
          company.id, 
          sender.id, 
          invitation.unique_token
        )
        
        expect(result[:success]).to be true
        
        # Try JWT login via API (this is what actually matters for SPA)
        post '/api/v1/auth/jwt_login', params: {
          email: user_email,
          password: user_password
        }, headers: { 'Accept' => 'application/json' }
        
        # Should return JWT tokens, not 403 Forbidden
        expect(response.status).to eq(200)
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('access_token')
        expect(json_response).to have_key('user')
        
        # Should not have email confirmation error
        expect(json_response).not_to have_key('email_not_confirmed')
      end
      
      it 'blocks JWT login before invitation acceptance' do
        # Try JWT login before accepting invitation
        post '/api/v1/auth/jwt_login', params: {
          email: user_email,
          password: user_password
        }, headers: { 'Accept' => 'application/json' }
        
        # Should return 403 Forbidden
        expect(response.status).to eq(403)
        json_response = JSON.parse(response.body)
        expect(json_response['email_not_confirmed']).to be true
      end
    end
  end
  
  describe 'Edge cases and error handling' do
    it 'handles already accepted invitations gracefully' do
      # Accept invitation first time
      invitation_service = InvitationService.new
      first_result = invitation_service.accept_company_connection(
        invited_user, 
        company.id, 
        sender.id, 
        invitation.unique_token
      )
      
      expect(first_result[:success]).to be true
      
      # Try to accept same invitation again
      second_result = invitation_service.accept_company_connection(
        invited_user, 
        company.id, 
        sender.id, 
        invitation.unique_token
      )
      
      expect(second_result[:success]).to be false
      expect(second_result[:error]).to include('already connected')
      
      # User should still be able to login (invitation_accepted_at should remain set)
      visit new_user_session_path(locale: 'cs')
      wait_for_vite_assets
      
      fill_in 'user[email]', with: user_email
      fill_in 'user[password]', with: user_password
      click_button 'Sign in'
      
      expect(page).to have_content('Signed in successfully')
    end
  end
end