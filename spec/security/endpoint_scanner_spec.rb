require 'rails_helper'

RSpec.describe 'API Endpoint Security Scanner', type: :request do
  # List ALL protected API endpoints
  PROTECTED_ENDPOINTS = {
    get: [
      '/api/v1/bookings',
      '/api/v1/meetings',
      '/api/v1/daily_logs',
      '/api/v1/events',
      '/api/v1/companies',
      '/api/v1/contracts',
      '/api/v1/works',
      '/api/v1/user',
      '/api/v1/subscription_status',
      '/api/v1/employees',
      '/api/v1/notifications',
      '/api/v1/company_settings',
      '/api/v1/user_settings',
      '/api/v1/reports/activities'
    ],
    post: [
      '/api/v1/bookings',
      '/api/v1/meetings',
      '/api/v1/daily_logs',
      '/api/v1/daily_logs/start',
      '/api/v1/daily_logs/finish',
      '/api/v1/events',
      '/api/v1/breaks',
      '/api/v1/auth/logout'
    ],
    put: [
      '/api/v1/company_settings',
      '/api/v1/user_settings'
    ],
    patch: [
      '/api/v1/notifications/mark_as_read'
    ]
  }
  
  # Skip auth endpoints
  SKIP_ENDPOINTS = [
    '/api/v1/auth/login',
    '/api/v1/auth/refresh'
  ]
  
  ATTACK_SCENARIOS = [
    { name: 'no token', token: nil },
    { name: 'empty token', token: '' },
    { name: 'malformed token', token: 'invalid.token' },
    { name: 'random string', token: 'Bearer randomstring123' }
  ]
  
  describe 'Endpoint Protection' do
    PROTECTED_ENDPOINTS.each do |method, endpoints|
      endpoints.each do |endpoint|
        next if SKIP_ENDPOINTS.include?(endpoint)
        
        describe "#{method.upcase} #{endpoint}" do
          ATTACK_SCENARIOS.each do |scenario|
            it "blocks request with #{scenario[:name]}" do
              headers = scenario[:token] ? auth_headers(scenario[:token]) : {}
              
              # Send request based on method
              case method
              when :get
                get endpoint, headers: headers
              when :post
                post endpoint, headers: headers, params: {}
              when :put
                put endpoint, headers: headers, params: {}
              when :patch
                patch endpoint, headers: headers, params: {}
              end
              
              # All should return 401 Unauthorized
              expect(response).to have_http_status(:unauthorized),
                "Expected 401 but got #{response.status} for #{method.upcase} #{endpoint} with #{scenario[:name]}"
              
              # Should not leak any data
              body = response.body
              expect(body).not_to include('company_id')
              expect(body).not_to include('user_id')
              expect(body).not_to match(/\d{4}-\d{2}-\d{2}/) # No dates
            end
          end
        end
      end
    end
  end
  
  describe 'Public Endpoints' do
    it 'allows access to auth endpoints without token' do
      # Login endpoint should be accessible
      post '/api/v1/auth/login', params: {
        email: '<EMAIL>',
        password: 'password'
      }
      
      # Should get 401 for bad credentials, not for missing token
      expect(response).not_to have_http_status(:forbidden)
    end
  end
end