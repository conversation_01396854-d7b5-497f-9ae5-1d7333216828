require 'rails_helper'

RSpec.describe 'Multi-tenancy Security with JWT', type: :request do
  let(:company_a) { create(:company, name: 'Company A') }
  let(:company_b) { create(:company, name: 'Company B') }
  let(:user_a) { create(:user) }
  let(:user_b) { create(:user) }
  
  before do
    # Set up users with their companies
    create(:company_user_role, user: user_a, company: company_a, role: create(:role, name: 'employee'))
    create(:company_user_role, user: user_b, company: company_b, role: create(:role, name: 'employee'))
    
    # Create test data for each company
    ActsAsTenant.with_tenant(company_a) do
      @booking_a = create(:booking, title: 'Company A Secret Booking')
      @event_a = create(:event, title: 'Company A Private Event', user: user_a)
      @daily_log_a = create(:daily_log, user: user_a, description: 'Company A Work')
      @contract_a = create(:contract, user: user_a, job_title: 'Company A Employee')
    end
    
    ActsAsTenant.with_tenant(company_b) do
      @booking_b = create(:booking, title: 'Company B Secret Booking')
      @event_b = create(:event, title: 'Company B Private Event', user: user_b)
      @daily_log_b = create(:daily_log, user: user_b, description: 'Company B Work')
      @contract_b = create(:contract, user: user_b, job_title: 'Company B Employee')
    end
  end
  
  describe 'Cross-company data isolation' do
    context 'when user from Company A makes requests' do
      let(:token) { jwt_token_for(user_a) }
      let(:headers) { auth_headers(token) }
      
      it 'only returns Company A bookings' do
        get '/api/v1/bookings', headers: headers
        
        expect(response).to have_http_status(:success)
        bookings = json_response
        
        # Should only see Company A data
        titles = bookings.map { |b| b['title'] }
        expect(titles).to include('Company A Secret Booking')
        expect(titles).not_to include('Company B Secret Booking')
      end
      
      it 'only returns Company A events' do
        get '/api/v1/events', headers: headers
        
        expect(response).to have_http_status(:success)
        events = json_response
        
        titles = events.map { |e| e['title'] }
        expect(titles).to include('Company A Private Event')
        expect(titles).not_to include('Company B Private Event')
      end
      
      it 'only returns Company A daily logs' do
        get '/api/v1/daily_logs', headers: headers
        
        expect(response).to have_http_status(:success)
        logs = json_response
        
        descriptions = logs.map { |l| l['description'] }
        expect(descriptions).to include('Company A Work')
        expect(descriptions).not_to include('Company B Work')
      end
      
      it 'cannot access Company B booking directly' do
        get "/api/v1/bookings/#{@booking_b.id}", headers: headers
        
        expect(response).to have_http_status(:not_found)
      end
      
      it 'cannot update Company B resources' do
        put "/api/v1/bookings/#{@booking_b.id}", 
            headers: headers,
            params: { booking: { title: 'Hacked!' } }
        
        expect(response).to have_http_status(:not_found)
        
        # Verify data wasn't changed
        @booking_b.reload
        expect(@booking_b.title).to eq('Company B Secret Booking')
      end
    end
  end
  
  describe 'JWT token company switching' do
    let(:user_multi) { create(:user) }
    
    before do
      # User belongs to both companies
      create(:company_user_role, user: user_multi, company: company_a, role: create(:role, name: 'manager'))
      create(:company_user_role, user: user_multi, company: company_b, role: create(:role, name: 'employee'))
    end
    
    it 'respects the company_id in JWT token' do
      # Token for Company A
      token_a = jwt_token_for(user_multi)
      
      get '/api/v1/bookings', headers: auth_headers(token_a)
      bookings_a = json_response
      
      # Token for Company B (would need to implement company switching)
      # This test documents expected behavior
      
      # For now, verify primary company access
      titles = bookings_a.map { |b| b['title'] }
      expect(titles).to include('Company A Secret Booking')
    end
  end
  
  describe 'Token manipulation attacks' do
    it 'prevents user from modifying company_id in token' do
      # Create valid token
      valid_token = jwt_token_for(user_a)
      
      # Decode and modify (this simulates an attack)
      decoded = JWT.decode(valid_token, Rails.application.secret_key_base, false)[0]
      decoded['company_id'] = company_b.id
      
      # Re-encode with same secret (wouldn't work with proper validation)
      modified_token = JWT.encode(decoded, Rails.application.secret_key_base)
      
      # Try to access Company B data
      get '/api/v1/bookings', headers: auth_headers(modified_token)
      
      # Should either reject the token or still restrict to Company A
      if response.status == 200
        bookings = json_response
        titles = bookings.map { |b| b['title'] }
        expect(titles).not_to include('Company B Secret Booking')
      else
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end