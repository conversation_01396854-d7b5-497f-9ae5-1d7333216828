# frozen_string_literal: true

# ABOUTME: Tests for TenantContextDebugger utility that replaced ad-hoc TYM-30 debug scripts
# ABOUTME: ensuring comprehensive context investigation and test data creation capabilities.

require 'rails_helper'
require 'tenant_context_debugger'

RSpec.describe TenantContextDebugger do
  let(:company_a) { create(:company, name: 'Company A') }
  let(:company_b) { create(:company, name: 'Company B') }
  let(:user) { create(:user, email: '<EMAIL>') }
  let(:role) { create(:role, name: 'employee') }

  before do
    create(:company_user_role, user: user, company: company_a, role: role)
    create(:company_user_role, user: user, company: company_b, role: role)
  end

  describe '.investigate_context_mismatch' do
    context 'with valid user' do
      it 'returns comprehensive context analysis' do
        analysis = described_class.investigate_context_mismatch(user.email)
        
        expect(analysis[:user]).to include(
          id: user.id,
          email: user.email,
          companies_count: 2
        )
        expect(analysis[:companies]).to have(2).items
        expect(analysis[:mismatches]).to be_an(Array)
        expect(analysis[:recommendations]).to be_an(Array)
      end

      it 'analyzes each company the user belongs to' do
        # Create some test data in each company
        ActsAsTenant.with_tenant(company_a) do
          create(:contract, user: user, company: company_a)
          create(:event, contract: Contract.last, company: company_a)
        end

        analysis = described_class.investigate_context_mismatch(user.email)
        
        company_analysis = analysis[:companies].find { |c| c[:company_id] == company_a.id }
        expect(company_analysis).to include(
          company_id: company_a.id,
          company_name: company_a.name,
          user_role: 'employee'
        )
        expect(company_analysis[:contracts]).to have(1).item
        expect(company_analysis[:events]).to have(1).item
      end
    end

    context 'with JWT token' do
      it 'analyzes JWT context and detects mismatches' do
        # Create JWT for company_b but analyze company_a context  
        jwt_token = generate_jwt_for_company(user, company_b)
        
        analysis = described_class.investigate_context_mismatch(user.email, jwt_token: jwt_token)
        
        expect(analysis[:jwt_context]).to include(
          user_id: user.id,
          company_id: company_b.id,
          company_name: company_b.name
        )
      end

      it 'detects JWT company mismatch when user does not belong to JWT company' do
        # Create a company the user doesn't belong to
        other_company = create(:company, name: 'Other Company')
        jwt_token = generate_jwt_for_company(user, other_company)
        
        analysis = described_class.investigate_context_mismatch(user.email, jwt_token: jwt_token)
        
        expect(analysis[:mismatches]).to include('JWT company mismatch')
      end
    end

    context 'with non-existent user' do
      it 'returns empty analysis with user not found' do
        analysis = described_class.investigate_context_mismatch('<EMAIL>')
        
        expect(analysis[:user]).to be_nil
      end
    end
  end

  describe '.create_test_events_for_context_validation' do
    let!(:contract) { 
      ActsAsTenant.with_tenant(company_a) do
        create(:contract, user: user, company: company_a)
      end
    }

    it 'creates test events in specified company context' do
      results = described_class.create_test_events_for_context_validation(company_a.id, user.email)
      
      expect(results[:company]).to eq(company_a)
      expect(results[:contract]).to be_a(Contract)
      expect(results[:illness_event]).to be_a(Event)
      expect(results[:vacation_event]).to be_a(Event)
      expect(results[:jwt_token]).to be_present
      
      # Verify events are in correct company context
      ActsAsTenant.with_tenant(company_a) do
        expect(Event.where(title: 'Test Illness Event')).to exist
        expect(Event.where(title: 'Test Vacation Event')).to exist
      end
    end

    it 'creates aligned JWT token for testing' do
      results = described_class.create_test_events_for_context_validation(company_a.id, user.email)
      jwt_token = results[:jwt_token]
      
      # Decode and verify token points to correct company
      payload = JWT.decode(jwt_token, Rails.application.secret_key_base, true, algorithm: 'HS256')[0]
      expect(payload['company_id']).to eq(company_a.id)
      expect(payload['user_id']).to eq(user.id)
    end

    it 'cleans up previous test events' do
      # Create some existing test events
      ActsAsTenant.with_tenant(company_a) do
        create(:event, title: 'Test Illness Event', contract: contract)
        create(:event, title: 'Test Vacation Event', contract: contract)
      end
      
      expect {
        described_class.create_test_events_for_context_validation(company_a.id, user.email)
      }.not_to change { 
        ActsAsTenant.with_tenant(company_a) { Event.count }
      }
    end

    context 'with non-existent company' do
      it 'returns nil and logs error' do
        results = described_class.create_test_events_for_context_validation(999999, user.email)
        expect(results).to be_nil
      end
    end

    context 'with non-existent user' do
      it 'returns nil and logs error' do
        results = described_class.create_test_events_for_context_validation(company_a.id, '<EMAIL>')
        expect(results).to be_nil
      end
    end
  end

  describe '.test_conflict_detection_with_context' do
    let!(:contract) {
      ActsAsTenant.with_tenant(company_a) do
        create(:contract, user: user, company: company_a)
      end
    }
    let!(:test_events) {
      ActsAsTenant.with_tenant(company_a) do
        [
          create(:event, contract: contract, company: company_a, 
                 start_time: Date.current.beginning_of_day + 9.hours,
                 end_time: Date.current.beginning_of_day + 17.hours),
          create(:event, contract: contract, company: company_a,
                 start_time: Date.current.beginning_of_day + 1.day + 9.hours,
                 end_time: Date.current.beginning_of_day + 1.day + 17.hours)
        ]
      end
    }

    it 'finds events in correct tenant context' do
      start_date = Date.current
      end_date = Date.current + 2.days
      
      events = described_class.test_conflict_detection_with_context(contract.id, start_date, end_date)
      
      expect(events).to have(2).items
      expect(events.pluck(:id)).to match_array(test_events.pluck(:id))
    end

    it 'uses proper ActsAsTenant context during conflict detection' do
      # Ensure we're testing context isolation
      other_company_contract = nil
      ActsAsTenant.with_tenant(company_b) do
        other_company_contract = create(:contract, user: user, company: company_b)
        create(:event, contract: other_company_contract, company: company_b,
               start_time: Date.current.beginning_of_day + 9.hours,
               end_time: Date.current.beginning_of_day + 17.hours)
      end
      
      start_date = Date.current
      end_date = Date.current + 1.day
      
      # Should only find events in company_a context
      events = described_class.test_conflict_detection_with_context(contract.id, start_date, end_date)
      
      expect(events).to have(1).item
      expect(events.first.contract.company).to eq(company_a)
    end

    context 'with non-existent contract' do
      it 'returns nil and logs error' do
        result = described_class.test_conflict_detection_with_context(999999, Date.current, Date.current + 1.day)
        expect(result).to be_nil
      end
    end
  end

  private

  def generate_jwt_for_company(user, company)
    if defined?(JwtService)
      JwtService.encode(user_id: user.id, company_id: company.id)
    else
      payload = {
        user_id: user.id,
        company_id: company.id,
        iat: Time.current.to_i,
        exp: 24.hours.from_now.to_i
      }
      JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
    end
  end
end