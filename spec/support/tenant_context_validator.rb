# frozen_string_literal: true

# ABOUTME: Tenant context validation helper to prevent multi-tenant testing hell by detecting
# ABOUTME: context mismatches between ActsAsTenant current_tenant and JWT token company_id.

module TenantContextValidator
  class ContextMismatchError < StandardError; end

  # Validates that ActsAsTenant current_tenant matches the company_id in JWT token
  # Prevents the TYM-30 scenario where test data was created in Company 1 but
  # JWT authentication pointed to Company 3, causing phantom debugging problems.
  #
  # @param token [String] JWT token to validate against current tenant
  # @param expected_company [Company, nil] Optional - specific company to validate against
  # @return [Boolean] true if contexts match
  # @raise [ContextMismatchError] if contexts don't match
  def ensure_tenant_jwt_context_match(token, expected_company: nil)
    return true unless token.present?

    # Decode JWT token to get company_id
    payload = decode_jwt_for_context_validation(token)
    jwt_company_id = payload&.dig('company_id')
    
    # Get current tenant context
    current_tenant = ActsAsTenant.current_tenant
    current_tenant_id = current_tenant&.id
    
    # Use expected_company if provided, otherwise use current_tenant
    target_company = expected_company || current_tenant
    target_company_id = target_company&.id
    
    # Validate context alignment
    if jwt_company_id.present? && target_company_id.present?
      if jwt_company_id != target_company_id
        raise_context_mismatch_error(
          jwt_company_id: jwt_company_id,
          tenant_company_id: target_company_id,
          jwt_company_name: Company.find_by(id: jwt_company_id)&.name,
          tenant_company_name: target_company&.name
        )
      end
    elsif jwt_company_id.present? && target_company_id.nil?
      Rails.logger.warn "[TENANT_CONTEXT] JWT contains company_id=#{jwt_company_id} but no tenant context is set"
    elsif jwt_company_id.nil? && target_company_id.present?
      Rails.logger.warn "[TENANT_CONTEXT] Tenant context is set to company_id=#{target_company_id} but JWT has no company_id"
    end
    
    Rails.logger.info "[TENANT_CONTEXT] ✅ Context validation passed - JWT company_id=#{jwt_company_id}, Tenant=#{target_company_id}"
    true
  end

  # Validates that test data creation context matches authentication context
  # Use this when creating test data to ensure it will be visible to API calls
  #
  # @param user [User] User for whom test data is being created
  # @param company [Company] Company context for test data creation
  # @return [String] JWT token that matches the test data context
  def create_test_data_with_matching_jwt_context(user, company)
    # Ensure we're in the correct tenant context for data creation
    unless ActsAsTenant.current_tenant == company
      Rails.logger.warn "[TENANT_CONTEXT] Switching tenant context from #{ActsAsTenant.current_tenant&.id} to #{company.id}"
      ActsAsTenant.current_tenant = company
    end
    
    # Generate JWT token that matches the current tenant context
    token = generate_jwt_for_testing(user, company)
    
    # Validate the context alignment
    ensure_tenant_jwt_context_match(token, expected_company: company)
    
    Rails.logger.info "[TENANT_CONTEXT] ✅ Test data context aligned - Company: #{company.id} (#{company.name})"
    token
  end

  # Helper to quickly check if current context is safe for API testing
  # Use this before making API calls in tests to catch context issues early
  #
  # @param token [String] JWT token to be used for API calls
  # @return [Hash] Context information for debugging
  def debug_current_context(token = nil)
    context_info = {
      acts_as_tenant_current: ActsAsTenant.current_tenant&.id,
      acts_as_tenant_name: ActsAsTenant.current_tenant&.name,
      jwt_company_id: nil,
      jwt_company_name: nil,
      context_aligned: false
    }
    
    if token.present?
      payload = decode_jwt_for_context_validation(token)
      jwt_company_id = payload&.dig('company_id')
      context_info[:jwt_company_id] = jwt_company_id
      context_info[:jwt_company_name] = Company.find_by(id: jwt_company_id)&.name if jwt_company_id
      context_info[:context_aligned] = (context_info[:acts_as_tenant_current] == jwt_company_id)
    end
    
    # Log context information for debugging
    Rails.logger.info "[TENANT_CONTEXT] Current context: #{context_info}"
    
    unless context_info[:context_aligned]
      Rails.logger.warn "[TENANT_CONTEXT] ⚠️  POTENTIAL MISMATCH DETECTED"
      Rails.logger.warn "[TENANT_CONTEXT] - ActsAsTenant: #{context_info[:acts_as_tenant_current]} (#{context_info[:acts_as_tenant_name]})"
      Rails.logger.warn "[TENANT_CONTEXT] - JWT Token: #{context_info[:jwt_company_id]} (#{context_info[:jwt_company_name]})"
      Rails.logger.warn "[TENANT_CONTEXT] - This may cause phantom debugging problems similar to TYM-30"
    end
    
    context_info
  end

  private

  # Safely decode JWT token for context validation without full authentication
  def decode_jwt_for_context_validation(token)
    return nil unless token.present?
    
    begin
      # Use JwtService if available, otherwise decode manually
      if defined?(JwtService)
        JwtService.decode(token)
      else
        JWT.decode(token, Rails.application.secret_key_base, true, algorithm: 'HS256')[0]
      end
    rescue JWT::DecodeError, JWT::ExpiredSignature => e
      Rails.logger.warn "[TENANT_CONTEXT] Failed to decode JWT for context validation: #{e.message}"
      nil
    end
  end

  # Generate JWT token for testing that matches the specified company context
  def generate_jwt_for_testing(user, company)
    # Use existing JWT generation if available
    if defined?(JwtService)
      JwtService.encode(user_id: user.id, company_id: company.id)
    else
      # Fallback JWT generation
      payload = {
        user_id: user.id,
        company_id: company.id,
        iat: Time.current.to_i,
        exp: 24.hours.from_now.to_i
      }
      JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
    end
  end

  # Raise detailed context mismatch error with debugging information
  def raise_context_mismatch_error(jwt_company_id:, tenant_company_id:, jwt_company_name:, tenant_company_name:)
    error_message = <<~ERROR
      🚨 TENANT CONTEXT MISMATCH DETECTED 🚨
      
      This is the same type of context mismatch that caused TYM-30 debugging hell!
      
      PROBLEM:
      - JWT Token expects Company ID: #{jwt_company_id} (#{jwt_company_name})  
      - ActsAsTenant context is: #{tenant_company_id} (#{tenant_company_name})
      
      IMPACT:
      - API calls will search in Company #{jwt_company_id} context
      - Test data exists in Company #{tenant_company_id} context  
      - API will return empty results, appearing as if features are broken
      - You'll waste hours debugging phantom problems that don't exist
      
      SOLUTION:
      1. Ensure test data is created in Company #{jwt_company_id} context:
         ActsAsTenant.with_tenant(Company.find(#{jwt_company_id})) do
           # Create your test data here
         end
         
      2. Or generate JWT token for Company #{tenant_company_id}:
         token = create_test_data_with_matching_jwt_context(user, Company.find(#{tenant_company_id}))
         
      3. Use debug_current_context(token) to verify alignment before API calls
    ERROR
    
    raise ContextMismatchError, error_message
  end
end

# Include in RSpec configuration
RSpec.configure do |config|
  config.include TenantContextValidator, type: :request
  config.include TenantContextValidator, type: :system
  config.include TenantContextValidator, type: :feature
end