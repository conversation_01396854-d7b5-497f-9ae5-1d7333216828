# frozen_string_literal: true

# ABOUTME: Tenant-aware factory helpers to prevent TYM-30 context mismatch issues
# ABOUTME: by ensuring factories respect ActsAsTenant context and provide clear error messages.

module TenantAwareFactories
  # Create a factory with explicit tenant context validation
  # Prevents TYM-30 scenario where test data is created in wrong company context
  #
  # @param factory_name [Symbol] FactoryBot factory to create
  # @param company [Company] Company context for creation
  # @param attributes [Hash] Additional factory attributes
  # @return [Object] Created factory object
  def create_in_tenant_context(factory_name, company:, **attributes)
    # Validate company parameter
    unless company.is_a?(Company)
      raise ArgumentError, "Expected Company object, got #{company.class}"
    end

    # Store original tenant context
    original_tenant = ActsAsTenant.current_tenant
    context_changed = false

    begin
      # Set tenant context if different
      if ActsAsTenant.current_tenant != company
        Rails.logger.info "[TENANT_FACTORY] Switching context from #{original_tenant&.id} to #{company.id}"
        ActsAsTenant.current_tenant = company
        context_changed = true
      end

      # Create factory with tenant context
      object = FactoryBot.create(factory_name, **attributes)
      
      # Validate created object has correct tenant association
      validate_object_tenant_context(object, company)
      
      Rails.logger.info "[TENANT_FACTORY] ✅ Created #{factory_name} in Company #{company.id} context"
      object

    rescue => e
      Rails.logger.error "[TENANT_FACTORY] ❌ Failed to create #{factory_name}: #{e.message}"
      raise e
    ensure
      # Restore original tenant context if we changed it
      if context_changed && original_tenant != company
        ActsAsTenant.current_tenant = original_tenant
        Rails.logger.info "[TENANT_FACTORY] Restored context to #{original_tenant&.id}"
      end
    end
  end

  # Create test data with matching JWT token for API testing
  # Ensures perfect alignment between test data context and authentication context
  #
  # @param user [User] User for JWT token generation
  # @param company [Company] Company context for both data and token
  # @param factories [Hash] Hash of factory_name => attributes to create
  # @return [Hash] Hash with :objects (created objects) and :jwt_token
  def create_test_scenario_with_jwt(user, company, factories = {})
    objects = {}
    
    # Create all factories in the specified tenant context
    ActsAsTenant.with_tenant(company) do
      factories.each do |factory_name, attributes|
        objects[factory_name] = FactoryBot.create(factory_name, **attributes)
      end
      
      # Ensure user has access to this company
      unless user.companies.include?(company)
        role = FactoryBot.create(:role, name: 'employee')
        FactoryBot.create(:company_user_role, user: user, company: company, role: role)
        Rails.logger.info "[TENANT_FACTORY] Added user #{user.id} to company #{company.id}"
      end
    end

    # Generate JWT token for the same company context
    jwt_token = generate_jwt_for_company(user, company)
    
    # Validate context alignment
    if defined?(TenantContextValidator)
      include TenantContextValidator
      ensure_tenant_jwt_context_match(jwt_token, expected_company: company)
    end

    Rails.logger.info "[TENANT_FACTORY] ✅ Created test scenario for Company #{company.id} with #{objects.keys.size} objects"
    
    {
      objects: objects,
      jwt_token: jwt_token,
      company: company,
      user: user
    }
  end

  # Create standard test users with proper company associations
  # Prevents the need for manual user/company setup in every test
  #
  # @return [Hash] Hash with :owner, :admin, :employee users and :company
  def create_standard_test_company_with_users
    company = FactoryBot.create(:company, name: "Test Company #{SecureRandom.hex(4)}")
    
    # Create roles
    owner_role = FactoryBot.create(:role, name: 'owner')
    admin_role = FactoryBot.create(:role, name: 'admin') 
    employee_role = FactoryBot.create(:role, name: 'employee')
    
    # Create users
    owner = FactoryBot.create(:user, email: "owner-#{SecureRandom.hex(4)}@test.com")
    admin = FactoryBot.create(:user, email: "admin-#{SecureRandom.hex(4)}@test.com")
    employee = FactoryBot.create(:user, email: "employee-#{SecureRandom.hex(4)}@test.com")
    
    # Associate users with company
    FactoryBot.create(:company_user_role, user: owner, company: company, role: owner_role)
    FactoryBot.create(:company_user_role, user: admin, company: company, role: admin_role)
    FactoryBot.create(:company_user_role, user: employee, company: company, role: employee_role)
    
    # Create contracts in the company context
    ActsAsTenant.with_tenant(company) do
      owner_contract = FactoryBot.create(:contract, user: owner, company: company)
      admin_contract = FactoryBot.create(:contract, user: admin, company: company)
      employee_contract = FactoryBot.create(:contract, user: employee, company: company)
    end

    Rails.logger.info "[TENANT_FACTORY] ✅ Created standard test company #{company.id} with 3 users"
    
    {
      company: company,
      owner: owner,
      admin: admin,
      employee: employee,
      owner_role: owner_role,
      admin_role: admin_role,
      employee_role: employee_role
    }
  end

  private

  # Validate that created object belongs to expected company context
  def validate_object_tenant_context(object, expected_company)
    return unless object.respond_to?(:company_id) || object.respond_to?(:company)
    
    object_company_id = if object.respond_to?(:company_id)
                         object.company_id
                       elsif object.respond_to?(:company)
                         object.company&.id
                       end

    if object_company_id && object_company_id != expected_company.id
      error_message = <<~ERROR
        🚨 FACTORY TENANT CONTEXT MISMATCH 🚨
        
        Factory created object in wrong company context!
        
        Expected Company: #{expected_company.id} (#{expected_company.name})
        Actual Company: #{object_company_id}
        Object: #{object.class.name} ID #{object.id}
        
        This could lead to TYM-30 debugging hell scenario.
        
        Check your factory definitions and ActsAsTenant configuration.
      ERROR
      
      raise StandardError, error_message
    end
  end

  # Generate JWT token for specific company context
  def generate_jwt_for_company(user, company)
    if defined?(JwtService)
      JwtService.encode(user_id: user.id, company_id: company.id)
    else
      payload = {
        user_id: user.id,
        company_id: company.id,
        iat: Time.current.to_i,
        exp: 24.hours.from_now.to_i
      }
      JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
    end
  end
end

# Include in RSpec configuration
RSpec.configure do |config|
  config.include TenantAwareFactories, type: :request
  config.include TenantAwareFactories, type: :system
  config.include TenantAwareFactories, type: :feature
end