# ABOUTME: <PERSON> Code optimized testing helpers for Rails+Vue.js application
# ABOUTME: Provides semantic methods for <PERSON> to interact with AttendifyApp features

module ClaudeTestHelpers
  extend ActiveSupport::Concern
  
  included do
    # Make helpers available in all feature/system specs
    include Capybara::DSL
  end
  
  module ClassMethods
    # Run specific feature test by name
    def run_claude_test(test_name)
      system("bundle exec rspec spec/features/#{test_name}_spec.rb --format documentation")
    end
    
    # Run all feature tests with Claude-friendly output
    def run_all_feature_tests
      system("bundle exec rspec spec/features/ --format documentation --out tmp/claude_test_results.txt")
    end
    
    # Network testing for LAN development setup
    def run_remote_test(test_name)
      ENV['REMOTE_TESTING'] = 'true'
      result = system("bundle exec rspec spec/features/#{test_name}_spec.rb --format documentation")
      ENV.delete('REMOTE_TESTING')
      result
    end
  end
  
  # === Authentication Helpers ===
  
  def sign_in_as(user_type)
    case user_type.to_s
    when 'admin'
      user = create(:user, role: 'admin', email: '<EMAIL>', password: 'password123')
    when 'manager'
      user = create(:user, role: 'manager', email: '<EMAIL>', password: 'password123')
    when 'employee'
      user = create(:user, role: 'employee', email: '<EMAIL>', password: 'password123')
    else
      user = create(:user, email: '<EMAIL>', password: 'password123')
    end
    
    visit new_user_session_path
    wait_for_vite_assets
    
    fill_in 'user[email]', with: user.email
    fill_in 'user[password]', with: 'password123'
    click_button 'Sign in'
    
    expect(page).to have_content('Signed in successfully')
    user
  end
  
  def sign_out_current_user
    click_link_or_button 'Sign Out' if page.has_content?('Sign Out')
  end
  
  def sign_in_test_user
    # Use the standard test credentials from CLAUDE.local.md
    visit new_user_session_path(locale: 'cs')
    wait_for_vite_assets
    
    fill_in 'user[email]', with: '<EMAIL>'
    fill_in 'user[password]', with: '123456'
    click_button 'Sign in'
    
    expect(page).to have_content('Signed in successfully')
  end
  
  # === Navigation Helpers ===
  
  def navigate_to(page_name)
    case page_name.to_s.downcase
    when 'dashboard'
      visit root_path
    when 'events'
      visit events_path
    when 'works'
      visit works_path
    when 'meetings'
      visit meetings_path
    when 'bookings'
      visit bookings_path
    when 'contracts'
      visit contracts_path
    when 'companies'
      visit companies_path
    when 'daily_logs'
      visit daily_logs_path
    when 'profile'
      visit edit_user_registration_path
    when 'settings'
      visit user_settings_path
    else
      visit "/#{page_name}"
    end
    wait_for_vite_assets
  end
  
  # === Vue.js Component Interaction ===
  
  def click_vue_component(component_name, action = nil)
    if action
      click_button_with_data_attribute('vue-component', component_name, 'action', action)
    else
      click_button_with_data_attribute('vue-component', component_name)
    end
  end
  
  def fill_vue_input(component_name, field, value)
    fill_in_with_data_attribute('vue-component', component_name, 'field', field, value)
  end
  
  def expect_vue_component_visible(component_name)
    expect(page).to have_selector("[data-vue-component='#{component_name}']")
  end
  
  def expect_vue_component_hidden(component_name)
    expect(page).not_to have_selector("[data-vue-component='#{component_name}']")
  end
  
  # === Data Attribute Helpers ===
  
  def click_button_with_data_attribute(attribute, value, secondary_attr = nil, secondary_value = nil)
    selector = "[data-#{attribute}='#{value}']"
    selector += "[data-#{secondary_attr}='#{secondary_value}']" if secondary_attr
    
    find(selector).click
  end
  
  def fill_in_with_data_attribute(attribute, value, field_attr, field_value, text)
    selector = "[data-#{attribute}='#{value}'][data-#{field_attr}='#{field_value}']"
    fill_in selector, with: text
  end
  
  def expect_element_with_data_attribute(attribute, value, content = nil)
    selector = "[data-#{attribute}='#{value}']"
    if content
      expect(page).to have_selector(selector, text: content)
    else
      expect(page).to have_selector(selector)
    end
  end
  
  # === AttendifyApp Domain-Specific Helpers ===
  
  def create_test_event(options = {})
    defaults = {
      user: create(:user),
      title: 'Test Event for Claude',
      start_time: 1.hour.from_now,
      end_time: 2.hours.from_now
    }
    
    create(:event, defaults.merge(options))
  end
  
  def create_test_work(options = {})
    defaults = {
      user: create(:user),
      title: 'Test Work for Claude',
      description: 'Test work created by Claude Code Agent'
    }
    
    create(:work, defaults.merge(options))
  end
  
  def create_test_meeting(options = {})
    defaults = {
      user: create(:user),
      title: 'Test Meeting for Claude',
      start_time: 1.hour.from_now,
      end_time: 2.hours.from_now
    }
    
    create(:meeting, defaults.merge(options))
  end
  
  def fill_event_form(event_data)
    fill_in 'event[title]', with: event_data[:title] if event_data[:title]
    fill_in 'event[description]', with: event_data[:description] if event_data[:description]
    
    if event_data[:start_time]
      fill_in 'event[start_time]', with: event_data[:start_time].strftime('%Y-%m-%d %H:%M')
    end
    
    if event_data[:end_time]
      fill_in 'event[end_time]', with: event_data[:end_time].strftime('%Y-%m-%d %H:%M')
    end
  end
  
  def submit_event_form
    click_button_with_data_attribute('action', 'submit-event')
  end
  
  def fill_work_form(work_data)
    fill_in 'work[title]', with: work_data[:title] if work_data[:title]
    fill_in 'work[description]', with: work_data[:description] if work_data[:description]
    
    if work_data[:contract_id]
      select work_data[:contract_name] || "Contract #{work_data[:contract_id]}", from: 'work[contract_id]'
    end
  end
  
  def submit_work_form
    click_button_with_data_attribute('action', 'submit-work')
  end
  
  # === Calendar Testing Helpers ===
  
  def navigate_to_calendar_month(month, year)
    # Navigate to specific month in calendar
    visit events_path(month: month, year: year)
    wait_for_vite_assets
  end
  
  def click_calendar_day(day)
    click_button_with_data_attribute('calendar-day', day.to_s)
  end
  
  def expect_calendar_event_visible(event_title)
    expect(page).to have_selector('[data-calendar-event]', text: event_title)
  end
  
  # === Error and Success Handling ===
  
  def expect_success_message(message = nil)
    if message
      expect(page).to have_content(message)
    else
      expect(page).to have_selector('.alert-success, .notice, .flash-success')
    end
  end
  
  def expect_error_message(message = nil)
    if message
      expect(page).to have_content(message)
    else
      expect(page).to have_selector('.alert-danger, .alert, .flash-error')
    end
  end
  
  # === Debug Helpers for Claude ===
  
  def debug_page_state
    puts "=== PAGE DEBUG INFO ==="
    puts "Current URL: #{current_url}"
    puts "Page Title: #{page.title}"
    puts "Flash Messages: #{page.all('.alert, .notice, .flash').map(&:text)}"
    puts "Forms Present: #{page.all('form').count}"
    puts "Buttons Present: #{page.all('button, input[type=submit]').map(&:text)}"
    puts "Vue Components: #{page.all('[data-vue-component]').map { |el| el['data-vue-component'] }}"
    puts "======================="
  end
  
  def screenshot_for_claude(name = "debug_#{Time.current.to_i}")
    page.save_screenshot("tmp/claude_screenshots/#{name}.png")
    puts "📸 Screenshot saved: tmp/claude_screenshots/#{name}.png"
  end
  
  def wait_for_vite_assets
    # Ensure Vite assets are loaded before testing
    expect(page).to have_selector('body', wait: 3)
    sleep(0.5) # Small buffer for Vue.js component mounting
  end
  
  def wait_for_api_response
    # Wait for API calls to complete (useful for SPA)
    sleep(1)
  end
  
  def check_console_errors
    # Check for JavaScript errors in console
    # Note: This requires additional setup with Cuprite console monitoring
    logs = page.driver.browser.logs.get(:browser)
    error_logs = logs.select { |log| log.level == 'SEVERE' }
    
    if error_logs.any?
      puts "⚠️  Console Errors Found:"
      error_logs.each { |log| puts "   #{log.message}" }
    end
    
    error_logs
  end
  
  # === Network Testing Helpers ===
  
  def ensure_remote_server_running
    host = ENV.fetch('RAILS_DEV_HOST', '************')
    port = ENV.fetch('RAILS_DEV_PORT', '5100')
    
    unless system("curl -s http://#{host}:#{port} > /dev/null 2>&1")
      puts "❌ Remote Rails server not accessible at http://#{host}:#{port}"
      puts "   Start server with: foreman start -f Procfile.dev"
      raise "Remote Rails server unavailable"
    end
    puts "✅ Remote Rails server accessible at http://#{host}:#{port}"
  end
  
  def debug_network_state
    host = ENV.fetch('RAILS_DEV_HOST', '************')
    port = ENV.fetch('RAILS_DEV_PORT', '5100')
    
    puts "=== NETWORK DEBUG INFO ==="
    puts "Remote Testing: #{ENV['REMOTE_TESTING'] || 'false'}"
    puts "Target Server: http://#{host}:#{port}"
    puts "Current URL: #{current_url}"
    puts "Page Title: #{page.title}"
    puts "Capybara App Host: #{Capybara.app_host}"
    puts "Capybara Server Running: #{Capybara.run_server}"
    puts "=========================="
  end
end

# Include in all feature and system specs
RSpec.configure do |config|
  config.include ClaudeTestHelpers, type: :feature
  config.include ClaudeTestHelpers, type: :system
end