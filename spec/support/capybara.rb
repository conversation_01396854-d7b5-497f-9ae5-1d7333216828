# ABOUTME: Enhanced Capybara configuration optimized for Claude Code Agent testing
# ABOUTME: Supports LAN development setup (************:5100) with Cuprite for fast headless testing

# frozen_string_literal: true

require 'capybara/rspec'
require 'capybara/rails'
require 'capybara/cuprite'

# Network configuration - matches foreman development setup
# Rails runs on 5100 in development, Vite assets integrated via Rails
RAILS_DEV_HOST = ENV.fetch('RAILS_DEV_HOST', '************')
RAILS_DEV_PORT = ENV.fetch('RAILS_DEV_PORT', '5100')

# Configure Capybara drivers
Capybara.javascript_driver = :cuprite
Capybara.default_driver = :rack_test # Fast for non-JS tests

# Configure Cuprite options for LAN testing
Capybara.register_driver :cuprite do |app|
  Capybara::Cuprite::Driver.new(
    app,
    window_size: [1200, 800],
    browser_options: {
      'no-sandbox' => nil,
      'disable-web-security' => nil, # For LAN testing
      'allow-running-insecure-content' => nil
    },
    process_timeout: 60, # Increased for network latency and slow browser startup
    inspector: true,
    headless: !ENV['HEADLESS'].in?([nil, '', '0', 'false'])
  )
end

# Configure for remote Rails server when REMOTE_TESTING=true
if ENV['REMOTE_TESTING'] == 'true'
  Capybara.app_host = "http://#{RAILS_DEV_HOST}:#{RAILS_DEV_PORT}"
  Capybara.run_server = false # Don't start local server
  puts "🌐 Capybara configured for remote testing: #{Capybara.app_host}"
else
  # Local testing setup
  Capybara.server = :puma, { Silent: true }
  Capybara.asset_host = 'http://localhost:3000'
end

# Configure timeouts for network testing
Capybara.default_max_wait_time = ENV['REMOTE_TESTING'] == 'true' ? 10 : 5
Capybara.default_normalize_ws = true

# Helper for testing in multiple browser sessions
def in_browser(name)
  old_session = Capybara.session_name
  Capybara.session_name = name
  yield
  Capybara.session_name = old_session
end

# Configure for system specs
RSpec.configure do |config|
  config.include Capybara::DSL, type: :feature
  config.include Capybara::DSL, type: :system
  
  # Clean up after each system spec
  config.after(:each, type: :system) do
    Capybara.reset_sessions!
  end
  
  # Ensure screenshot directory exists
  config.before(:suite) do
    FileUtils.mkdir_p('tmp/claude_screenshots')
  end
  
  # Wait for Vite assets in JS tests
  config.before(:each, js: true) do
    # Ensure development server is running
    unless system("curl -s http://#{RAILS_DEV_HOST}:#{RAILS_DEV_PORT} > /dev/null")
      puts "⚠️  Rails server not running on #{RAILS_DEV_HOST}:#{RAILS_DEV_PORT}. Run: foreman start -f Procfile.dev"
    end
  end
end