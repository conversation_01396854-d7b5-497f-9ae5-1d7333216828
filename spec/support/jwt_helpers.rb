# spec/support/jwt_helpers.rb
module Jwt<PERSON>el<PERSON>
  def jwt_token_for(user, exp: 1.hour.from_now)
    payload = {
      user_id: user.id,
      email: user.email,
      company_id: user.companies.first&.id || user.company_user_roles.first&.company_id
    }
    
    JwtService.encode(payload, exp)
  end
  
  def expired_jwt_token_for(user)
    jwt_token_for(user, exp: 1.hour.ago)
  end
  
  def invalid_jwt_token
    'invalid.jwt.token'
  end
  
  def auth_headers(token)
    token ? { 'Authorization' => "Bearer #{token}" } : {}
  end
  
  def decode_token(response)
    token = response.headers['Authorization']&.split(' ')&.last
    return nil unless token
    
    JwtService.decode(token)
  rescue JWT::DecodeError
    nil
  end
  
  def json_response
    JSON.parse(response.body)
  end
end

RSpec.configure do |config|
  config.include JwtHelpers, type: :request
end