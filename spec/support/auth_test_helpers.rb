# frozen_string_literal: true

# TODO: Consider exploring more streamlined factory patterns or dedicated test data builders 
# for complex multi-tenant scenarios to improve test readability and maintainability.
# See: docs/jwt_implementation_notes_chunk_19_final.md - "Potential Future Enhancements #1"
module AuthTestHelpers
  # Helper methods for session-based authentication testing
  
  def sign_in_user(user, password = 'password123')
    post user_session_path(locale: 'en'), params: {
      user: {
        email: user.email,
        password: password
      }
    }
    follow_redirect! if response.redirect?
  end

  def sign_out_user
    delete destroy_user_session_path(locale: 'en')
    follow_redirect! if response.redirect?
  end

  def ensure_signed_in(user)
    unless user_signed_in?
      sign_in_user(user)
    end
  end

  def user_signed_in?
    session[:user_id].present? || session['warden.user.user.key'].present?
  end

  def current_session_user_id
    if session['warden.user.user.key'].present?
      session['warden.user.user.key'][0][0]
    else
      session[:user_id]
    end
  end

  def current_tenant_id
    session[:tenant_id]
  end

  def switch_tenant(company)
    # Simulate tenant switching in session
    session[:tenant_id] = company.id
  end

  # Helper to test protected routes
  def assert_requires_authentication(path, method = :get)
    send(method, path)
    expect(response).to redirect_to(new_user_session_path(locale: 'en'))
  end

  # Helper to test session persistence
  def assert_session_persists_across_requests(user)
    sign_in_user(user)
    initial_session_id = session.id
    
    # Make another request
    get root_path
    
    expect(session.id).to eq(initial_session_id)
    expect(user_signed_in?).to be true
  end

  # Helper to test session expiry
  def simulate_session_timeout
    # Clear session data to simulate timeout
    session.clear
  end

  # Helper to verify multi-tenancy in session
  def assert_tenant_isolation(user, company)
    sign_in_user(user)
    switch_tenant(company)
    
    expect(current_tenant_id).to eq(company.id)
    expect(ActsAsTenant.current_tenant).to eq(company)
  end
end

RSpec.configure do |config|
  config.include AuthTestHelpers, type: :request
  config.include AuthTestHelpers, type: :system
end