# spec/models/user_spec.rb
require 'rails_helper'

RSpec.describe User, type: :model do
  let(:user) { create(:user) }
  let(:company) { create(:company) }
  let(:company2) { create(:company) }
  let(:company3) { create(:company) }
  let(:owner_role) { create(:role, name: 'owner') }
  let(:employee_role) { create(:role, name: 'employee') }
  
  describe 'JWT-specific methods' do
    describe '#jwt_payload' do
      context 'when user has a primary company' do
        before do
          create(:company_user_role, user: user, company: company, is_primary: true)
        end
        
        it 'returns a hash with user_id, email, company_id, and jti' do
          payload = user.jwt_payload
          
          expect(payload).to be_a(Hash)
          expect(payload[:user_id]).to eq(user.id)
          expect(payload[:email]).to eq(user.email)
          expect(payload[:company_id]).to eq(company.id)
          expect(payload[:jti]).to be_present
          expect(payload[:jti]).to match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
        end
        
        it 'generates a unique jti each time' do
          jti1 = user.jwt_payload[:jti]
          jti2 = user.jwt_payload[:jti]
          
          expect(jti1).not_to eq(jti2)
        end
      end
      
      context 'when user has no primary company' do
        it 'returns a hash with nil company_id' do
          payload = user.jwt_payload
          
          expect(payload).to be_a(Hash)
          expect(payload[:user_id]).to eq(user.id)
          expect(payload[:email]).to eq(user.email)
          expect(payload[:company_id]).to be_nil
          expect(payload[:jti]).to be_present
        end
      end
    end
    
    describe '#on_jwt_dispatch' do
      it 'logs JWT dispatch information' do
        token = 'sample.jwt.token'
        payload = { 'jti' => 'test-jti-123', 'user_id' => user.id }
        
        expect(Rails.logger).to receive(:info).with("JWT dispatched for user #{user.id} with jti: test-jti-123")
        
        user.on_jwt_dispatch(token, payload)
      end
    end
  end

  describe '#leave_company' do
    # Use unique instances for each test context to avoid interference
    let(:test_user) { create(:user) }
    let(:test_company) { create(:company) }
    let(:test_company2) { create(:company) }
    let(:test_company3) { create(:company) }
    
    before do
      # Create contracts for testing
      create(:contract, user: test_user, company: test_company)
      create(:contract, user: test_user, company: test_company2)
      create(:contract, user: test_user, company: test_company3)
    end

    context 'when leaving a primary company with other companies available' do
      before do
        # Set up user with multiple companies - company is primary, company2 is not
        create(:company_user_role, user: test_user, company: test_company, role: owner_role, is_primary: true, active: true)
        create(:company_user_role, user: test_user, company: test_company2, role: employee_role, is_primary: false, active: true)
      end

      it 'successfully leaves the company and reassigns primary to another company' do
        expect(test_user.primary_company).to eq(test_company)
        
        result = test_user.leave_company(test_company)
        
        expect(result).to be true
        
        # Verify role was deactivated and primary flag removed
        role = test_user.company_user_roles.unscoped.find_by(company: test_company)
        expect(role.active).to be false
        expect(role.is_primary).to be false # Primary flag removed to avoid uniqueness constraint
        
        # Verify another company became primary
        test_user.reload
        expect(test_user.primary_company).to eq(test_company2)
        
        # Verify the new primary role
        new_primary_role = test_user.company_user_roles.find_by(company: test_company2)
        expect(new_primary_role.is_primary).to be true
        expect(new_primary_role.active).to be true
      end

      it 'terminates all contracts with the company' do
        contracts_count_before = Contract.where(user: test_user, company: test_company).count
        expect(contracts_count_before).to be > 0
        
        test_user.leave_company(test_company)
        
        # All contracts should be terminated (status changed to terminated)
        active_contracts = Contract.where(user: test_user, company: test_company, status: :active)
        expect(active_contracts.count).to eq(0)
        
        terminated_contracts = Contract.where(user: test_user, company: test_company, status: :terminated)
        expect(terminated_contracts.count).to eq(contracts_count_before)
      end
    end

    context 'when leaving a non-primary company' do
      before do
        # Set up user with multiple companies - test_company2 is primary, test_company is not
        create(:company_user_role, user: test_user, company: test_company, role: employee_role, is_primary: false, active: true)
        create(:company_user_role, user: test_user, company: test_company2, role: owner_role, is_primary: true, active: true)
      end

      it 'successfully leaves the company without affecting primary company' do
        expect(test_user.primary_company).to eq(test_company2)
        
        result = test_user.leave_company(test_company)
        
        expect(result).to be true
        
        # Verify role was deactivated
        role = test_user.company_user_roles.unscoped.find_by(company: test_company)
        expect(role.active).to be false
        
        # Verify primary company unchanged
        test_user.reload
        expect(test_user.primary_company).to eq(test_company2)
        
        # Verify primary role unchanged
        primary_role = test_user.company_user_roles.find_by(company: test_company2)
        expect(primary_role.is_primary).to be true
        expect(primary_role.active).to be true
      end
    end

    context 'when leaving the only company' do
      before do
        create(:company_user_role, user: test_user, company: test_company, role: owner_role, is_primary: true, active: true)
      end

      it 'successfully leaves the company and user has no primary company' do
        expect(test_user.primary_company).to eq(test_company)
        
        result = test_user.leave_company(test_company)
        
        expect(result).to be true
        
        # Verify role was deactivated
        role = test_user.company_user_roles.unscoped.find_by(company: test_company)
        expect(role.active).to be false
        
        # Verify no primary company
        test_user.reload
        expect(test_user.primary_company).to be_nil
      end
    end

    context 'when leaving primary company with multiple other companies' do
      before do
        # Set up user with three companies - test_company is primary
        create(:company_user_role, user: test_user, company: test_company, role: owner_role, is_primary: true, active: true)
        create(:company_user_role, user: test_user, company: test_company2, role: employee_role, is_primary: false, active: true)
        create(:company_user_role, user: test_user, company: test_company3, role: employee_role, is_primary: false, active: true)
      end

      it 'reassigns primary to first available company' do
        expect(test_user.primary_company).to eq(test_company)
        
        result = test_user.leave_company(test_company)
        
        expect(result).to be true
        
        # Verify primary was reassigned to one of the remaining companies
        test_user.reload
        expect(test_user.primary_company).to be_in([test_company2, test_company3])
        
        # Verify only one company is marked as primary
        primary_roles = test_user.company_user_roles.where(is_primary: true, active: true)
        expect(primary_roles.count).to eq(1)
      end
    end

    context 'when an error occurs during contract termination' do
      before do
        create(:company_user_role, user: test_user, company: test_company, role: owner_role, is_primary: true, active: true)
        
        # Mock contract to raise an error
        allow_any_instance_of(Contract).to receive(:terminate!).and_raise(StandardError.new("Contract termination failed"))
      end

      it 'rolls back the transaction and returns false' do
        expect(Rails.logger).to receive(:error).with(/Error leaving company/)
        
        result = test_user.leave_company(test_company)
        
        expect(result).to be false
        
        # Verify nothing was changed due to rollback
        role = test_user.company_user_roles.find_by(company: test_company)
        expect(role.active).to be true
        expect(role.is_primary).to be true
      end
    end

    context 'when leaving a company that user does not belong to' do
      let(:other_company) { create(:company) }
      
      before do
        create(:company_user_role, user: test_user, company: test_company, role: owner_role, is_primary: true, active: true)
      end

      it 'returns true without making any changes' do
        result = test_user.leave_company(other_company)
        
        expect(result).to be true
        
        # Verify user's existing role is unchanged
        role = test_user.company_user_roles.find_by(company: test_company)
        expect(role.active).to be true
        expect(role.is_primary).to be true
      end
    end

    context 'when leaving company from different tenant context (TYM-56 bug fix)' do
      let(:company_a) { create(:company, name: 'Company A') }
      let(:company_b) { create(:company, name: 'Company B') }
      let(:multi_tenant_user) { create(:user) }
      
      before do
        # Set up user in both companies - A as primary, B as secondary
        create(:company_user_role, user: multi_tenant_user, company: company_a, role: owner_role, is_primary: true, active: true)
        create(:company_user_role, user: multi_tenant_user, company: company_b, role: employee_role, is_primary: false, active: true)
        
        # Create contracts in both companies
        ActsAsTenant.with_tenant(company_a) do
          create(:contract, user: multi_tenant_user, company: company_a, status: :active)
        end
        ActsAsTenant.with_tenant(company_b) do
          create(:contract, user: multi_tenant_user, company: company_b, status: :active)
        end
      end
      
      it 'terminates contracts in target company despite different tenant context' do
        # Count contracts before leaving
        contracts_in_b_before = Contract.without_tenant.where(user: multi_tenant_user, company: company_b).count
        expect(contracts_in_b_before).to be > 0
        
        # User is in Company A context but tries to leave Company B
        ActsAsTenant.with_tenant(company_a) do
          result = multi_tenant_user.leave_company(company_b)
          expect(result).to be true
        end
        
        # Verify role was deactivated in Company B
        role_b = multi_tenant_user.company_user_roles.unscoped.find_by(company: company_b)
        expect(role_b.active).to be false
        
        # Verify contracts in Company B were terminated (using without_tenant to bypass scoping)
        active_contracts_in_b = Contract.without_tenant.where(user: multi_tenant_user, company: company_b, status: :active)
        expect(active_contracts_in_b.count).to eq(0)
        
        terminated_contracts_in_b = Contract.without_tenant.where(user: multi_tenant_user, company: company_b, status: :terminated)
        expect(terminated_contracts_in_b.count).to eq(contracts_in_b_before)
        
        # Verify Company A remains unchanged
        role_a = multi_tenant_user.company_user_roles.find_by(company: company_a)
        expect(role_a.active).to be true
        expect(role_a.is_primary).to be true
      end
    end
  end
end