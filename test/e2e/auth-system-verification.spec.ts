// ABOUTME: Verification test for the new multi-role API-based authentication system
// ABOUTME: Tests that all three user roles can authenticate and access appropriate features

import { test, expect } from './fixtures/auth';
import { testUsers } from './helpers/test-data';

test.describe('Authentication System Verification', () => {
  test('owner can authenticate and access mainbox', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Should be on mainbox page (authentication successful)
    expect(page.url()).toContain('/mainbox');
    
    // Should see user menu indicating logged in state
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    // Owner should see company management elements
    // Note: These selectors may need adjustment based on actual UI
    await expect(page.locator('body')).toContainText('Mainbox');
  });

  test('admin can authenticate and access mainbox', async ({ authenticatedAdmin }) => {
    const page = authenticatedAdmin.page;
    
    // Should be on mainbox page (authentication successful)
    expect(page.url()).toContain('/mainbox');
    
    // Should see user menu indicating logged in state
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    // Admin should see work management elements
    await expect(page.locator('body')).toContainText('Mainbox');
  });

  test('employee can authenticate and access mainbox', async ({ authenticatedEmployee }) => {
    const page = authenticatedEmployee.page;
    
    // Should be on mainbox page (authentication successful) 
    expect(page.url()).toContain('/mainbox');
    
    // Should see user menu indicating logged in state
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    
    // Employee should see today's work section
    await expect(page.locator('[data-vue-component="today-work-section"]')).toBeVisible();
  });

  test('authentication is fast (under 3 seconds per role)', async ({ browser }) => {
    const startTime = Date.now();
    
    // Test owner authentication speed
    const ownerContext = await browser.newContext();
    const ownerPage = await ownerContext.newPage();
    
    // Get CSRF token
    await ownerPage.goto('/users/sign_in');
    const csrfToken = await ownerPage.locator('meta[name="csrf-token"]').getAttribute('content');
    
    // API login
    await ownerPage.request.post('/users/sign_in', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': csrfToken,
      },
      data: {
        'user[email]': testUsers.owner.email,
        'user[password]': testUsers.owner.password,
        'user[remember_me]': '0',
        'commit': 'Log in',
        'authenticity_token': csrfToken
      }
    });
    
    await ownerPage.goto('/mainbox');
    await ownerContext.close();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should be much faster than UI-based login (which took 10+ seconds)
    expect(duration).toBeLessThan(3000);
  });

  test('can switch between different authenticated contexts', async ({ browser }) => {
    // Create owner context
    const ownerContext = await browser.newContext();
    const ownerPage = await ownerContext.newPage();
    
    await ownerPage.goto('/users/sign_in');
    const ownerCsrfToken = await ownerPage.locator('meta[name="csrf-token"]').getAttribute('content');
    
    await ownerPage.request.post('/users/sign_in', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': ownerCsrfToken,
      },
      data: {
        'user[email]': testUsers.owner.email,
        'user[password]': testUsers.owner.password,
        'user[remember_me]': '0',
        'commit': 'Log in',
        'authenticity_token': ownerCsrfToken
      }
    });
    
    await ownerPage.goto('/mainbox');
    
    // Create employee context simultaneously
    const employeeContext = await browser.newContext();
    const employeePage = await employeeContext.newPage();
    
    await employeePage.goto('/users/sign_in');
    const employeeCsrfToken = await employeePage.locator('meta[name="csrf-token"]').getAttribute('content');
    
    await employeePage.request.post('/users/sign_in', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': employeeCsrfToken,
      },
      data: {
        'user[email]': testUsers.employee.email,
        'user[password]': testUsers.employee.password,
        'user[remember_me]': '0',
        'commit': 'Log in',
        'authenticity_token': employeeCsrfToken
      }
    });
    
    await employeePage.goto('/mainbox');
    
    // Both should be authenticated to their respective accounts
    expect(ownerPage.url()).toContain('/mainbox');
    expect(employeePage.url()).toContain('/mainbox');
    
    // Contexts should be isolated (different users)
    await expect(ownerPage.locator('[data-testid="user-menu"]')).toBeVisible();
    await expect(employeePage.locator('[data-testid="user-menu"]')).toBeVisible();
    
    await ownerContext.close();
    await employeeContext.close();
  });

  test('authentication failure is handled correctly', async ({ page }) => {
    // Test with invalid credentials
    await page.goto('/users/sign_in');
    const csrfToken = await page.locator('meta[name="csrf-token"]').getAttribute('content');
    
    const response = await page.request.post('/users/sign_in', {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': csrfToken,
      },
      data: {
        'user[email]': '<EMAIL>',
        'user[password]': 'wrongpassword',
        'user[remember_me]': '0',
        'commit': 'Log in',
        'authenticity_token': csrfToken
      }
    });
    
    // Should redirect back to login or return error status
    // The exact behavior depends on Rails/Devise configuration
    await page.goto('/mainbox');
    
    // Should be redirected to login page (authentication failed)
    expect(page.url()).toContain('/users/sign_in');
  });
});