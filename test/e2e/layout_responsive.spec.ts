// ABOUTME: Test for responsive layout behavior - topbar hidden on desktop, company info in sidebar
// ABOUTME: Tests the implementation of tym-51 - desktop topbar removal and company info relocation

import { test, expect } from '@playwright/test'
import { authenticatedOwner } from './fixtures/auth'

test.describe('Responsive Layout - TYM-51', () => {
  test('should hide topbar on desktop and show company info in sidebar', async ({ page }) => {
    await authenticatedOwner(page)
    
    // Set desktop viewport
    await page.setViewportSize({ width: 1024, height: 768 })
    
    // Navigate to dashboard
    await page.goto('/cs/dashboard')
    await page.waitForSelector('[data-testid="authenticated-layout"]')
    
    // Topbar should be hidden on desktop
    const topbar = page.locator('.topbar')
    await expect(topbar).toBeHidden()
    
    // Company info should be visible in sidebar
    const sidebar = page.locator('[data-vue-component="sidebar"]')
    await expect(sidebar).toBeVisible()
    
    // Company section should be visible in sidebar on desktop
    const companySection = page.locator('.company-section')
    await expect(companySection).toBeVisible()
    
    // Company name should be present in sidebar
    const companyName = page.locator('.company-name')
    await expect(companyName).toBeVisible()
  })

  test('should show topbar on mobile and hide company section in sidebar', async ({ page }) => {
    await authenticatedOwner(page)
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to dashboard
    await page.goto('/cs/dashboard')
    await page.waitForSelector('[data-testid="authenticated-layout"]')
    
    // Topbar should be visible on mobile
    const topbar = page.locator('.topbar')
    await expect(topbar).toBeVisible()
    
    // Company info should be visible in topbar on mobile
    const topbarCompanySelector = page.locator('.topbar .company-selector')
    await expect(topbarCompanySelector).toBeVisible()
    
    // Sidebar should be hidden by default on mobile
    const sidebar = page.locator('[data-vue-component="sidebar"]')
    await expect(sidebar).not.toHaveClass(/open/)
    
    // Company section in sidebar should be hidden on mobile
    const companySection = page.locator('.company-section')
    await expect(companySection).toBeHidden()
  })

  test('should maintain mobile menu toggle functionality', async ({ page }) => {
    await authenticatedOwner(page)
    
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Navigate to dashboard
    await page.goto('/cs/dashboard')
    await page.waitForSelector('[data-testid="authenticated-layout"]')
    
    // Mobile menu toggle should be visible in topbar
    const mobileMenuToggle = page.locator('#mobile-menu-toggle')
    await expect(mobileMenuToggle).toBeVisible()
    
    // Click mobile menu toggle
    await mobileMenuToggle.click()
    
    // Sidebar should become visible and have 'open' class
    const sidebar = page.locator('[data-vue-component="sidebar"]')
    await expect(sidebar).toHaveClass(/open/)
    
    // Close button should be visible in mobile sidebar
    const closeMobileMenu = page.locator('[data-testid="close-mobile-menu"]')
    await expect(closeMobileMenu).toBeVisible()
    
    // Click close button
    await closeMobileMenu.click()
    
    // Sidebar should be hidden again
    await expect(sidebar).not.toHaveClass(/open/)
  })

  test('should show plan badge when not on free plan', async ({ page }) => {
    await authenticatedOwner(page)
    
    // Set desktop viewport
    await page.setViewportSize({ width: 1024, height: 768 })
    
    // Navigate to dashboard
    await page.goto('/cs/dashboard')
    await page.waitForSelector('[data-testid="authenticated-layout"]')
    
    // If plan badge exists in sidebar, it should be visible
    const planBadge = page.locator('.company-section .plan-badge')
    const planBadgeCount = await planBadge.count()
    
    if (planBadgeCount > 0) {
      await expect(planBadge).toBeVisible()
      // Should contain crown icon
      const crownIcon = planBadge.locator('svg')
      await expect(crownIcon).toBeVisible()
    }
  })

  test('should have proper content area height on desktop', async ({ page }) => {
    await authenticatedOwner(page)
    
    // Set desktop viewport
    await page.setViewportSize({ width: 1024, height: 768 })
    
    // Navigate to dashboard
    await page.goto('/cs/dashboard')
    await page.waitForSelector('[data-testid="authenticated-layout"]')
    
    // Content area should have full viewport height on desktop
    const contentArea = page.locator('.content-area')
    await expect(contentArea).toBeVisible()
    
    // Check that content area has proper height styling
    const contentAreaHeight = await contentArea.evaluate(el => {
      const styles = window.getComputedStyle(el)
      return styles.height
    })
    
    // Should be 100vh on desktop (no topbar)
    expect(contentAreaHeight).toBe('768px') // 100vh = viewport height
  })
})