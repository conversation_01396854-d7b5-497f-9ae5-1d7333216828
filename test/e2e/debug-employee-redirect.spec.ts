// ABOUTME: Debug test to see where employee gets redirected after login
// ABOUTME: Helps identify the correct redirect URL for employee users

import { test, expect } from '@playwright/test';
import { LoginPage } from './pages/LoginPage';
import { testUsers } from './helpers/test-data';

test.describe('Debug Employee Redirect', () => {
  test('see where employee gets redirected', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    
    // Use real development database credentials
    await loginPage.login(testUsers.employee.email, testUsers.employee.password);
    
    // Wait a moment for redirect
    await page.waitForTimeout(3000);
    
    // Check current URL
    const currentUrl = page.url();
    console.log('Employee redirected to:', currentUrl);
    
    // Check if there are any error messages
    const errorMessage = await page.locator('.alert-danger').textContent().catch(() => null);
    if (errorMessage) {
      console.log('Error message:', errorMessage);
    }
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'employee-redirect-debug.png' });
    
    console.log('Debug screenshot saved as employee-redirect-debug.png');
  });
});