// ABOUTME: Simple authentication test using UI method with real credentials
// ABOUTME: Tests that real development database users can authenticate via UI

import { test, expect } from '@playwright/test';
import { LoginPage } from './pages/LoginPage';
import { testUsers } from './helpers/test-data';

test.describe('Simple Authentication Test', () => {
  test('owner can authenticate via UI', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    
    // Use real development database credentials
    await loginPage.login(testUsers.owner.email, testUsers.owner.password);
    
    // Should redirect to mainbox
    await loginPage.expectToBeLoggedIn();
    
    // Verify we're actually logged in by checking for user email in the page
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    
    console.log('Owner authentication successful!');
  });

  test('employee can authenticate via UI', async ({ page }) => {
    const loginPage = new LoginPage(page);
    await loginPage.goto();
    
    // Use real development database credentials  
    await loginPage.login(testUsers.employee.email, testUsers.employee.password);
    
    // Should redirect to mainbox
    await loginPage.expectToBeLoggedIn();
    
    // Verify we're actually logged in by checking for user email in the page
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    
    console.log('Employee authentication successful!');
  });
});