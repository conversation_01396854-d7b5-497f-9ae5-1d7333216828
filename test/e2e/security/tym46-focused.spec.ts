// ABOUTME: Focused security test for TYM-46 - Company access validation
// ABOUTME: Quick test to verify the security vulnerability is NOT present

import { test, expect } from '../fixtures/auth';

test.describe('TYM-46: Focused Security Verification', () => {
  test('API endpoints properly validate company access', async ({ authenticatedEmployee }) => {
    const page = authenticatedEmployee.page;
    
    // Test 1: Companies API only returns authorized companies
    const companiesResponse = await page.request.get('/api/v1/companies', {
      headers: { 'Accept': 'application/json' }
    });
    
    expect(companiesResponse.status()).toBe(200);
    const companiesData = await companiesResponse.json();
    
    expect(companiesData).toHaveProperty('company_user_roles');
    expect(Array.isArray(companiesData.company_user_roles)).toBe(true);
    
    console.log(`User has access to ${companiesData.company_user_roles.length} companies`);
    
    // Test 2: Company switching validates access  
    const unauthorizedCompanyId = 999;
    const switchResponse = await page.request.post('/api/v1/companies/switch_company', {
      data: { company_id: unauthorizedCompanyId },
      headers: { 
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    expect(switchResponse.status()).toBe(403);
    const error = await switchResponse.json();
    expect(error.success).toBe(false);
    
    // Test 3: Company connections shows only legitimate pending invitations
    const connectionsResponse = await page.request.get('/api/v1/company_connections/fetch', {
      headers: { 'Accept': 'application/json' }
    });
    
    expect(connectionsResponse.status()).toBe(200);
    const connections = await connectionsResponse.json();
    
    console.log(`User has ${connections.length} pending invitations`);
    
    // All connections should be legitimate pending contracts
    if (Array.isArray(connections)) {
      for (const connection of connections) {
        expect(connection).toHaveProperty('company');
        expect(connection.company).toHaveProperty('id');
      }
    }
  });
  
  test('dashboard navigation works without unauthorized data exposure', async ({ authenticatedEmployee }) => {
    const page = authenticatedEmployee.page;
    
    // Navigate to main dashboard
    await page.goto('/cs/mainbox');
    
    // Should load without errors
    await expect(page.locator('[data-vue-component="mainbox"]')).toBeVisible({ timeout: 10000 });
    
    // Check URL didn't redirect to error page
    expect(page.url()).toContain('/mainbox');
    
    console.log('Dashboard loaded successfully - no unauthorized access detected');
  });
});