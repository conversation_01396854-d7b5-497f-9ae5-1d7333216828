// ABOUTME: TYM-47 E2E tests for desktop calendar button positioning and holidays link
// ABOUTME: Tests real calendar application to ensure buttons are visible on all days including day 31

import { test, expect } from './fixtures/auth';

test.describe('TYM-47 - Desktop Calendar Button Positioning', () => {
  test('calendar component loads and day buttons are visible for all days including day 31', async ({ authenticatedAdmin }) => {
    const page = authenticatedAdmin.page;

    // Navigate to real calendar application
    await page.goto('/calendar');

    // Verify real Vue.js calendar component loads
    await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible({ timeout: 10000 });

    // Wait for calendar grid to be populated
    await expect(page.locator('.calendar-grid .calendar-day-cell')).toHaveCount(42); // 6 weeks * 7 days

    // Find day 31 on the calendar (if it exists in current month)
    const day31Cell = page.locator('[data-testid="calendar-day-31"]');
    const day31Exists = await day31Cell.count() > 0;

    if (day31Exists) {
      // Test day 31 specifically - this is where the bug occurs
      console.log('Testing day 31 button positioning...');
      
      // Click on day 31 to show create buttons
      await day31Cell.click();
      
      // Verify day-create-buttons appear
      const createButtons = page.locator('.calendar-day-cell').filter({ has: page.locator('[data-testid="calendar-day-31"]') }).locator('.day-create-buttons');
      await expect(createButtons).toBeVisible({ timeout: 5000 });
      
      // Check that buttons are fully visible (not cut off by cell boundaries)
      const buttonsBox = await createButtons.boundingBox();
      const cellBox = await day31Cell.boundingBox();
      
      if (buttonsBox && cellBox) {
        // Buttons should be within or slightly extending from cell boundaries
        // Allow small overflow for visual design but ensure readability
        expect(buttonsBox.x + buttonsBox.width).toBeLessThan(cellBox.x + cellBox.width + 20); // Allow 20px overflow
        expect(buttonsBox.y + buttonsBox.height).toBeLessThan(cellBox.y + cellBox.height + 20); // Allow 20px overflow
      }
      
      // Verify both event and work buttons are present and clickable
      await expect(createButtons.locator('button').filter({ hasText: 'Událost' })).toBeVisible();
      await expect(createButtons.locator('button').filter({ hasText: 'Zakázka' })).toBeVisible();
    } else {
      // Test with another day if 31 doesn't exist in current month
      console.log('Day 31 not in current month, testing with day 30...');
      const day30Cell = page.locator('[data-testid="calendar-day-30"]');
      await day30Cell.click();
      
      const createButtons = page.locator('.calendar-day-cell').filter({ has: page.locator('[data-testid="calendar-day-30"]') }).locator('.day-create-buttons');
      await expect(createButtons).toBeVisible({ timeout: 5000 });
    }
  });

  test('edge day buttons (first and last columns) are properly positioned', async ({ authenticatedAdmin }) => {
    const page = authenticatedAdmin.page;

    await page.goto('/calendar');
    await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible({ timeout: 10000 });

    // Test first column day (Monday) - these should use left-aligned positioning
    const firstColumnDays = page.locator('.calendar-day-cell:nth-child(7n+1)').filter({ has: page.locator('.day-number') });
    const firstDayCount = await firstColumnDays.count();
    
    if (firstDayCount > 0) {
      await firstColumnDays.first().click();
      const firstColumnButtons = firstColumnDays.first().locator('.day-create-buttons');
      await expect(firstColumnButtons).toBeVisible({ timeout: 5000 });
      
      // Verify buttons don't overflow left edge
      const buttonsBox = await firstColumnButtons.boundingBox();
      const cellBox = await firstColumnDays.first().boundingBox();
      
      if (buttonsBox && cellBox) {
        expect(buttonsBox.x).toBeGreaterThanOrEqual(cellBox.x - 5); // Allow small margin
      }
    }

    // Test last column day (Sunday) - these should use right-aligned positioning  
    const lastColumnDays = page.locator('.calendar-day-cell:nth-child(7n)').filter({ has: page.locator('.day-number') });
    const lastDayCount = await lastColumnDays.count();
    
    if (lastDayCount > 0) {
      await lastColumnDays.first().click();
      const lastColumnButtons = lastColumnDays.first().locator('.day-create-buttons');
      await expect(lastColumnButtons).toBeVisible({ timeout: 5000 });
      
      // Verify buttons don't overflow right edge
      const buttonsBox = await lastColumnButtons.boundingBox();
      const cellBox = await lastColumnDays.first().boundingBox();
      
      if (buttonsBox && cellBox) {
        expect(buttonsBox.x + buttonsBox.width).toBeLessThanOrEqual(cellBox.x + cellBox.width + 5); // Allow small margin
      }
    }
  });

  test('holidays link is present and functional in desktop calendar header', async ({ authenticatedAdmin }) => {
    const page = authenticatedAdmin.page;

    await page.goto('/calendar');
    
    // Verify calendar loads
    await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible({ timeout: 10000 });
    
    // Check for desktop calendar header (should be visible on desktop, not mobile)
    await expect(page.locator('.desktop-calendar-header')).toBeVisible();
    
    // Verify holidays link exists
    const holidaysLink = page.locator('.desktop-calendar-header .calendar-controls a[href*="/holidays"]');
    await expect(holidaysLink).toBeVisible();
    await expect(holidaysLink).toHaveText('Svátky');
    
    // Test that the link is actually clickable and has proper styling
    await expect(holidaysLink).toHaveClass(/text-link-action/);
    await expect(holidaysLink).toHaveClass(/blue/);
    
    // Test navigation (but don't actually navigate to avoid test state issues)
    const href = await holidaysLink.getAttribute('href');
    expect(href).toMatch(/\/holidays/);
  });

  test('calendar buttons work with real form interactions', async ({ authenticatedAdmin }) => {
    const page = authenticatedAdmin.page;

    await page.goto('/calendar');
    await expect(page.locator('[data-vue-component="new-monthly-calendar"]')).toBeVisible({ timeout: 10000 });

    // Find any available day in current month
    const availableDay = page.locator('[data-testid^="calendar-day-"]').first();
    await availableDay.click();

    // Click create event button
    const createButtons = availableDay.locator('../.day-create-buttons');
    await expect(createButtons).toBeVisible({ timeout: 5000 });
    
    const eventButton = createButtons.locator('button').filter({ hasText: 'Událost' });
    await eventButton.click();

    // Verify event form modal opens (real Vue.js component)
    await expect(page.locator('.modal-overlay')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-vue-component="event-form"]')).toBeVisible();

    // Close modal
    await page.locator('.modal-overlay .close-btn').click();
    await expect(page.locator('.modal-overlay')).not.toBeVisible();

    // Test work button
    await availableDay.click();
    const workButton = createButtons.locator('button').filter({ hasText: 'Zakázka' });
    await workButton.click();

    // Verify work form modal opens
    await expect(page.locator('.modal-overlay')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-vue-component="work-form"]')).toBeVisible();
  });
});