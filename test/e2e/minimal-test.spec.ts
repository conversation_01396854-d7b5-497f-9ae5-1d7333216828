// ABOUTME: Minimal test to verify basic Playwright functionality and server connection
// ABOUTME: Tests that we can load the login page and see form elements

import { test, expect } from '@playwright/test';

test.describe('Minimal Connection Test', () => {
  test('can load login page', async ({ page }) => {
    await page.goto('/cs/users/sign_in');
    
    // Should see the login form
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    console.log('Login page loaded successfully');
    console.log('Page title:', await page.title());
  });

  test('can navigate to mainbox (should redirect to login)', async ({ page }) => {
    await page.goto('/cs/mainbox');
    
    // Should redirect to login page
    await page.waitForURL('**/cs/users/sign_in');
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    console.log('Mainbox correctly redirects to login when not authenticated');
  });
});