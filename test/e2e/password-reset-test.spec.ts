// ABOUTME: Password reset functionality test for employee user
// ABOUTME: Tests password reset flow and token generation

import { test, expect } from '@playwright/test';

test.describe('Password Reset Test', () => {
  test('employee can request password reset', async ({ page }) => {
    // Go to login page
    await page.goto('/cs/users/sign_in');
    
    // Click forgot password link
    await page.locator('text="Zapomněli jste heslo?"').click();
    
    // Should be on password reset page
    await expect(page).toHaveURL('**/cs/users/password/new');
    
    // Enter employee email
    await page.locator('input[type="email"]').fill('<EMAIL>');
    
    // Submit reset request
    await page.locator('button[type="submit"]').click();
    
    // Should show success message
    await expect(page.locator('text*="odeslali jsme vám e-mail"')).toBeVisible();
    
    console.log('Password reset request submitted successfully!');
  });
  
  test('can see reset token in database', async ({ page }) => {
    // This test will help us get the reset token from database
    console.log('Check rails console for: User.find_by(email: "<EMAIL>").reset_password_token');
  });
});