// ABOUTME: Playwright tests for TYM-47 - Desktop Calendar Button Visibility Issues and Missing Holidays Link
// ABOUTME: Tests both button positioning problems and the missing holidays link on desktop calendar

import { test, expect } from './fixtures/auth';

test.describe('Desktop Calendar - TYM-47', () => {
  test.beforeEach(async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Navigate to events calendar (desktop view)
    await page.goto('/cs/events');
    
    // Wait for calendar to load
    await page.waitForSelector('.new-calendar-layout', { timeout: 10000 });
    
    // Ensure we're on desktop view (resize if needed)
    await page.setViewportSize({ width: 1200, height: 800 });
  });

  test('should show holidays link on desktop calendar header', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // The holidays link should be visible in the calendar header/navigation area
    const holidaysLink = page.locator('a[href*="/holidays"]');
    
    // This test should fail initially as the holidays link doesn't exist on desktop
    await expect(holidaysLink).toBeVisible();
    
    // Verify the link text is properly localized
    await expect(holidaysLink).toContainText('Svátky');
  });

  test('should display create buttons without cutoff in small calendar cells', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Click on a calendar day to show create buttons
    const firstDayCell = page.locator('.calendar-day-cell').first();
    await firstDayCell.click();
    
    // Wait for create buttons to appear
    await page.waitForSelector('.day-create-buttons', { timeout: 5000 });
    
    const createButtons = page.locator('.day-create-buttons');
    
    // Verify buttons are visible and not cut off
    await expect(createButtons).toBeVisible();
    
    // Check that both buttons are visible within the viewport
    const eventButton = createButtons.locator('button', { hasText: 'Událost' });
    const workButton = createButtons.locator('button', { hasText: 'Zakázka' });
    
    await expect(eventButton).toBeVisible();
    await expect(workButton).toBeVisible();
    
    // Verify buttons are not clipped by checking their bounding box
    const buttonsBox = await createButtons.boundingBox();
    const cellBox = await firstDayCell.boundingBox();
    
    // Buttons should be positioned properly within or around the cell
    expect(buttonsBox).toBeTruthy();
    expect(cellBox).toBeTruthy();
    
    if (buttonsBox && cellBox) {
      // Buttons should not be completely outside the visible cell area
      // They can extend beyond but should have reasonable visibility
      const buttonsVisible = (
        buttonsBox.x >= cellBox.x - 60 && // Allow some overflow to the left
        buttonsBox.y >= cellBox.y - 60 && // Allow some overflow above
        buttonsBox.x + buttonsBox.width <= cellBox.x + cellBox.width + 60 && // Allow some overflow to the right
        buttonsBox.y + buttonsBox.height <= cellBox.y + cellBox.height + 60  // Allow some overflow below
      );
      
      expect(buttonsVisible).toBeTruthy();
    }
  });

  test('should handle create buttons on edge calendar cells without overflow', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Test with an edge cell (first cell of calendar grid)
    const edgeCell = page.locator('.calendar-day-cell').first();
    await edgeCell.click();
    
    await page.waitForSelector('.day-create-buttons', { timeout: 5000 });
    
    const createButtons = page.locator('.day-create-buttons');
    await expect(createButtons).toBeVisible();
    
    // Get viewport dimensions
    const viewportSize = page.viewportSize();
    const buttonsBox = await createButtons.boundingBox();
    
    if (buttonsBox && viewportSize) {
      // Buttons should be within viewport bounds
      expect(buttonsBox.x).toBeGreaterThanOrEqual(0);
      expect(buttonsBox.y).toBeGreaterThanOrEqual(0);
      expect(buttonsBox.x + buttonsBox.width).toBeLessThanOrEqual(viewportSize.width);
      expect(buttonsBox.y + buttonsBox.height).toBeLessThanOrEqual(viewportSize.height);
    }
  });

  test('should show holidays link with proper styling and navigation', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Find the holidays link
    const holidaysLink = page.locator('a[href*="/holidays"]');
    await expect(holidaysLink).toBeVisible();
    
    // Verify it has proper styling classes (consistent with mobile version)
    await expect(holidaysLink).toHaveClass(/text-link-action/);
    await expect(holidaysLink).toHaveClass(/blue/);
    
    // Test navigation
    await holidaysLink.click();
    
    // Should navigate to holidays page
    await expect(page).toHaveURL(/.*\/holidays/);
    
    // Should show holidays management interface
    await expect(page.locator('h1, h2, h3')).toContainText(/svátky|holidays/i);
  });

  test('should maintain button functionality after visibility fixes', async ({ authenticatedOwner }) => {
    const page = authenticatedOwner.page;
    
    // Click on a calendar day
    const dayCell = page.locator('.calendar-day-cell[data-testid*="calendar-day"]').first();
    await dayCell.click();
    
    await page.waitForSelector('.day-create-buttons', { timeout: 5000 });
    
    // Test event button functionality
    const eventButton = page.locator('.day-create-buttons button', { hasText: 'Událost' });
    await expect(eventButton).toBeVisible();
    await eventButton.click();
    
    // Should open event form modal
    await expect(page.locator('.modal-overlay')).toBeVisible();
    await expect(page.locator('.modal-header h3')).toContainText('Nová událost');
    
    // Close modal
    await page.locator('.close-btn').click();
    await expect(page.locator('.modal-overlay')).not.toBeVisible();
    
    // Test work button functionality
    await dayCell.click(); // Re-show buttons
    await page.waitForSelector('.day-create-buttons', { timeout: 5000 });
    
    const workButton = page.locator('.day-create-buttons button', { hasText: 'Zakázka' });
    await expect(workButton).toBeVisible();
    await workButton.click();
    
    // Should open work form modal
    await expect(page.locator('.modal-overlay')).toBeVisible();
    await expect(page.locator('.modal-header h3')).toContainText('Nová zakázka');
  });
});