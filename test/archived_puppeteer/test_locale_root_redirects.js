import puppeteer from 'puppeteer';

async function testLocaleRootRedirects() {
  console.log('=== Testing Locale Root Redirects ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const locales = ['cs', 'sk', 'en'];
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = [];
  
  try {
    const page = await browser.newPage();
    
    // Suppress console logs for cleaner output
    page.on('console', () => {});
    page.on('error', () => {});
    page.on('pageerror', () => {});
    
    console.log('Step 1: Logging in with test user...');
    
    // Navigate to login page and login
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Successfully logged in\n');
    
    console.log('Step 2: Testing root redirects for each locale...\n');
    
    // Test each locale root redirect
    for (const locale of locales) {
      totalTests++;
      try {
        console.log(`Testing /${locale} redirect...`);
        
        const response = await page.goto(`http://0.0.0.0:5100/${locale}`, {
          waitUntil: 'networkidle2',
          timeout: 10000
        });
        
        const finalUrl = page.url();
        const expectedRedirect = `/${locale}/dashboard`;
        const passed = finalUrl.includes(expectedRedirect);
        
        if (passed) {
          console.log(`  ✓ /${locale} → ${finalUrl}`);
          passedTests++;
        } else {
          console.log(`  ✗ /${locale} → ${finalUrl} (expected to include ${expectedRedirect})`);
          failedTests.push({
            path: `/${locale}`,
            expected: expectedRedirect,
            actual: finalUrl
          });
        }
      } catch (err) {
        console.log(`  ✗ /${locale} - Error: ${err.message}`);
        failedTests.push({
          path: `/${locale}`,
          error: err.message
        });
      }
    }
    
    console.log('\n=== Test Summary ===');
    console.log(`Total tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests.length}`);
    console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests.length > 0) {
      console.log('\nFailed tests:');
      failedTests.forEach(test => {
        console.log(`  - ${test.path}: ${test.error || `Expected ${test.expected}, got ${test.actual}`}`);
      });
      
      console.log('\nNote: Root redirects need to be configured in the Rails routes or Vue Router.');
      console.log('Current behavior might be handled by the authenticated_root_with_locale route.');
    } else {
      console.log('\n🎉 All locale root redirects working perfectly!');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testLocaleRootRedirects().catch(console.error);