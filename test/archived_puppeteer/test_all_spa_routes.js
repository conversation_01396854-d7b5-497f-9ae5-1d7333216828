import puppeteer from 'puppeteer';

async function testAllSPARoutes() {
  console.log('=== Testing All SPA Routes ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const routes = [
    // Core routes
    { path: '/cs/dashboard', name: 'dashboard' },
    { path: '/cs/mainbox', name: 'mainbox' },
    
    // Bookings
    { path: '/cs/bookings', name: 'bookings' },
    { path: '/cs/bookings/123', name: 'bookingShow' },
    { path: '/cs/booking_links', name: 'bookingLinks' },
    { path: '/cs/booking_links/456', name: 'bookingLinkShow' },
    
    // Meetings
    { path: '/cs/meetings', name: 'meetings' },
    { path: '/cs/meetings/789', name: 'meetingShow' },
    
    // Daily Logs
    { path: '/cs/daily_logs', name: 'dailyLogs' },
    { path: '/cs/daily_logs/report', name: 'dailyLogsReport' },
    
    // Works
    { path: '/cs/works', name: 'works' },
    { path: '/cs/works/101', name: 'workShow' },
    
    // Events
    { path: '/cs/events', name: 'events' },
    
    // Contracts
    { path: '/cs/contracts', name: 'contracts' },
    { path: '/cs/contracts/202', name: 'contractShow' },
    
    // Companies
    { path: '/cs/companies', name: 'companies' },
    { path: '/cs/companies/303/edit', name: 'companyEdit' },
    { path: '/cs/company_connections', name: 'companyConnections' },
    { path: '/cs/company_settings/edit', name: 'companySettings' },
    
    // Reports
    { path: '/cs/reports/activities', name: 'activitiesReport' },
    
    // User
    { path: '/cs/user_profiles/404/edit', name: 'userProfileEdit' },
    { path: '/cs/user_settings/edit', name: 'userSettings' },
    
    // Holidays
    { path: '/cs/holidays', name: 'holidays' },
    
    // Assignments
    { path: '/cs/assignments', name: 'assignments' }
  ];
  
  let passedRoutes = 0;
  let failedRoutes = [];
  
  try {
    const page = await browser.newPage();
    
    // Suppress console logs for cleaner output
    page.on('console', () => {});
    page.on('error', () => {});
    page.on('pageerror', () => {});
    
    for (const route of routes) {
      try {
        const response = await page.goto(`http://0.0.0.0:5100${route.path}`, {
          waitUntil: 'networkidle2',
          timeout: 10000
        });
        
        const status = response.status();
        const routerInfo = await page.evaluate(() => {
          if (window.router) {
            return {
              available: true,
              currentPath: window.router.currentRoute.value.path,
              routeName: window.router.currentRoute.value.name
            };
          }
          return { available: false };
        });
        
        const passed = status === 200 && 
                      routerInfo.available && 
                      routerInfo.currentPath === route.path;
        
        if (passed) {
          console.log(`✓ ${route.path.padEnd(30)} - ${route.name}`);
          passedRoutes++;
        } else {
          console.log(`✗ ${route.path.padEnd(30)} - Status: ${status}, Router: ${routerInfo.available}`);
          failedRoutes.push(route.path);
        }
      } catch (err) {
        console.log(`✗ ${route.path.padEnd(30)} - Error: ${err.message}`);
        failedRoutes.push(route.path);
      }
    }
    
    console.log('\n=== Test Summary ===');
    console.log(`Total routes tested: ${routes.length}`);
    console.log(`Passed: ${passedRoutes}`);
    console.log(`Failed: ${failedRoutes.length}`);
    
    if (failedRoutes.length > 0) {
      console.log('\nFailed routes:');
      failedRoutes.forEach(route => console.log(`  - ${route}`));
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testAllSPARoutes().catch(console.error);