import puppeteer from 'puppeteer';

async function testLocaleSwitch() {
  console.log('=== Simple Locale Switching Test ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Log redirects
    page.on('response', response => {
      if (response.status() >= 300 && response.status() < 400) {
        console.log(`REDIRECT: ${response.status()} ${response.url()} → ${response.headers()['location']}`);
      }
    });
    
    console.log('Step 1: Login...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Successfully logged in\n');
    
    // Test simple routes that should work
    const simpleRoutes = ['dashboard', 'events', 'bookings', 'works'];
    
    for (const route of simpleRoutes) {
      console.log(`Testing /${route} with both locales:`);
      
      // Test CS
      await page.goto(`http://0.0.0.0:5100/cs/${route}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const csUrl = page.url();
      console.log(`  CS: ${csUrl}`);
      
      // Test SK
      await page.goto(`http://0.0.0.0:5100/sk/${route}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const skUrl = page.url();
      console.log(`  SK: ${skUrl}`);
      
      if (skUrl.includes('/sk/') && !skUrl.includes('/cs/')) {
        console.log(`  ✅ ${route} works in SK`);
      } else {
        console.log(`  ❌ ${route} failed in SK - redirected to ${skUrl}`);
      }
      
      console.log('');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testLocaleSwitch().catch(console.error);