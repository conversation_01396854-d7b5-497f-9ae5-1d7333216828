import puppeteer from 'puppeteer';

async function testBookingsRoute() {
  console.log('Testing new bookings route...\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Test the new bookings route
    const response = await page.goto('http://0.0.0.0:5100/cs/bookings', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`Route status: ${response.status()}`);
    
    // Check if Vue app mounted
    const appElement = await page.$('#app');
    console.log(`Vue app mounted: ${appElement !== null}`);
    
    // Check Vue Router
    const routerInfo = await page.evaluate(() => {
      if (window.router) {
        return {
          available: true,
          currentPath: window.router.currentRoute.value.path,
          routeName: window.router.currentRoute.value.name
        };
      }
      return { available: false };
    });
    
    console.log('Vue Router info:', routerInfo);
    
    // Take screenshot
    await page.screenshot({ path: 'bookings-route-test.png' });
    console.log('Screenshot saved: bookings-route-test.png');
    
    // Final result
    if (response.status() === 200 && routerInfo.available && routerInfo.currentPath === '/cs/bookings') {
      console.log('\n✅ Bookings route test PASSED');
    } else {
      console.log('\n❌ Bookings route test FAILED');
    }
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testBookingsRoute().catch(console.error);