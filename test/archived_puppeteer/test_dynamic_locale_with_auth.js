import puppeteer from 'puppeteer';

async function testDynamicLocaleWithAuth() {
  console.log('=== Testing Dynamic Locale Routes with Authentication ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const locales = ['cs', 'sk', 'en'];
  const routes = ['dashboard', 'events', 'bookings'];
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = [];
  
  try {
    const page = await browser.newPage();
    
    // Suppress console logs for cleaner output
    page.on('console', () => {});
    page.on('error', () => {});
    page.on('pageerror', () => {});
    
    console.log('Step 1: Logging in with test user...');
    
    // Navigate to login page
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    // Fill login form with the provided credentials
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    // Submit form and wait for redirect
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    const loginResult = page.url();
    if (loginResult.includes('sign_in')) {
      console.log('❌ Login failed - using test user credentials');
      console.log('To test properly, create a test user with:');
      console.log('User.create!(email: "<EMAIL>", password: "password123", confirmed_at: Time.current)');
      console.log('Company.create!(name: "Test Company")');
      console.log('Role.create!(name: "owner")');
      console.log('CompanyUserRole.create!(user: user, company: company, role: role, is_primary: true)');
      return;
    }
    
    console.log('✅ Successfully logged in');
    console.log(`Redirected to: ${loginResult}`);
    
    console.log(`\nStep 2: Testing ${locales.length} locales × ${routes.length} routes = ${locales.length * routes.length} combinations\n`);
    
    for (const locale of locales) {
      console.log(`Testing locale: ${locale.toUpperCase()}`);
      
      for (const route of routes) {
        totalTests++;
        const testPath = `/${locale}/${route}`;
        
        try {
          const response = await page.goto(`http://0.0.0.0:5100${testPath}`, {
            waitUntil: 'networkidle2',
            timeout: 10000
          });
          
          const status = response.status();
          
          // Wait for Vue to load
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Check if Vue Router properly parsed locale
          const routerInfo = await page.evaluate(() => {
            if (window.router && window.router.currentRoute) {
              return {
                available: true,
                currentPath: window.router.currentRoute.value.path,
                params: window.router.currentRoute.value.params,
                matched: window.router.currentRoute.value.matched.length > 0
              };
            }
            return { available: false };
          });
          
          const passed = status === 200 && 
                        routerInfo.available && 
                        routerInfo.params?.locale === locale &&
                        routerInfo.matched;
          
          if (passed) {
            console.log(`  ✓ ${testPath} - Router locale: ${routerInfo.params.locale}`);
            passedTests++;
          } else {
            console.log(`  ✗ ${testPath} - Status: ${status}, Router: ${routerInfo.available}, Locale: ${routerInfo.params?.locale}, Matched: ${routerInfo.matched}`);
            failedTests.push({
              path: testPath,
              status: status,
              expected: locale,
              actual: routerInfo.params?.locale,
              routerAvailable: routerInfo.available,
              matched: routerInfo.matched
            });
          }
        } catch (err) {
          console.log(`  ✗ ${testPath} - Error: ${err.message}`);
          failedTests.push({
            path: testPath,
            error: err.message
          });
        }
      }
      console.log('');
    }
    
    console.log('=== Test Summary ===');
    console.log(`Total tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests.length}`);
    console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests.length > 0) {
      console.log('\nFailed tests details:');
      failedTests.forEach(test => {
        if (test.error) {
          console.log(`  - ${test.path}: ${test.error}`);
        } else {
          console.log(`  - ${test.path}: Status ${test.status}, Expected locale '${test.expected}', Got '${test.actual}', Router available: ${test.routerAvailable}, Route matched: ${test.matched}`);
        }
      });
    } else {
      console.log('\n🎉 All dynamic locale routes working perfectly with authentication!');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testDynamicLocaleWithAuth().catch(console.error);