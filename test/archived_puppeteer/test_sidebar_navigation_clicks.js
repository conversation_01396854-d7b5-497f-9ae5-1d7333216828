import puppeteer from 'puppeteer';

async function testSidebarNavigationClicks() {
  console.log('=== Testing Sidebar Navigation Clicks ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Login and navigate to SK dashboard...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    // Navigate to SK locale
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    console.log(`✅ Starting at: ${page.url()}`);
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\nStep 2: Test navigation clicks...');
    
    // Test clicking different navigation links
    const linksToTest = [
      { selector: '[href*="/sk/events"]', name: 'Events (Kalendár)', expectedUrl: '/sk/events' },
      { selector: '[href*="/sk/works"]', name: 'Works (Zákazky)', expectedUrl: '/sk/works' },
      { selector: '[href*="/sk/bookings"]', name: 'Bookings', expectedUrl: '/sk/bookings' },
      { selector: '[href*="/sk/meetings"]', name: 'Meetings', expectedUrl: '/sk/meetings' }
    ];
    
    for (const linkTest of linksToTest) {
      console.log(`\nTesting ${linkTest.name}:`);
      
      // Go back to dashboard for each test
      await page.goto('http://0.0.0.0:5100/sk/dashboard', {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find and click the link
      const linkExists = await page.$(linkTest.selector);
      if (linkExists) {
        await page.click(linkTest.selector);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const currentUrl = page.url();
        const correct = currentUrl.includes(linkTest.expectedUrl);
        
        console.log(`  ${correct ? '✅' : '❌'} Clicked: ${linkTest.name}`);
        console.log(`  Current URL: ${currentUrl}`);
        console.log(`  Expected: ${linkTest.expectedUrl}`);
        
        if (!correct) {
          console.log(`  Issue: Navigation did not work as expected`);
        }
      } else {
        console.log(`  ❌ Link not found: ${linkTest.selector}`);
      }
    }
    
    console.log('\nStep 3: Test locale persistence...');
    
    // Test that when we switch to a different starting locale, navigation preserves it
    await page.goto('http://0.0.0.0:5100/en/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Click on events from EN locale
    const enEventsLink = await page.$('[href*="/en/events"]');
    if (enEventsLink) {
      await page.click('[href*="/en/events"]');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const finalUrl = page.url();
      const correct = finalUrl.includes('/en/events');
      
      console.log(`  ${correct ? '✅' : '❌'} EN locale preservation test`);
      console.log(`  Final URL: ${finalUrl}`);
      console.log(`  Expected: /en/events`);
    } else {
      console.log(`  ❌ EN events link not found`);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testSidebarNavigationClicks().catch(console.error);