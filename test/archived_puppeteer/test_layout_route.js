import puppeteer from 'puppeteer';

async function testLayoutRoute() {
  console.log('Testing dashboard route with layout...\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Test dashboard with layout
    const response = await page.goto('http://0.0.0.0:5100/cs/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`Route status: ${response.status()}`);
    
    // Check if layout elements exist
    const hasTopbar = await page.$('.topbar') !== null;
    const hasSidebar = await page.$('.sidebar') !== null;
    const hasMainContent = await page.$('.main-content') !== null;
    
    console.log(`Layout elements:`);
    console.log(`  Topbar: ${hasTopbar}`);
    console.log(`  Sidebar: ${hasSidebar}`);
    console.log(`  Main content: ${hasMainContent}`);
    
    // Check Vue Router
    const routerInfo = await page.evaluate(() => {
      if (window.router) {
        return {
          available: true,
          currentPath: window.router.currentRoute.value.path,
          routeName: window.router.currentRoute.value.name
        };
      }
      return { available: false };
    });
    
    console.log('\nVue Router info:', routerInfo);
    
    // Take screenshot
    await page.screenshot({ path: 'dashboard-with-layout.png' });
    console.log('\nScreenshot saved: dashboard-with-layout.png');
    
    // Test another route without layout to compare
    const eventsResponse = await page.goto('http://0.0.0.0:5100/cs/events', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    const hasLayoutOnEvents = await page.$('.topbar') !== null;
    console.log(`\nEvents route (without layout): ${eventsResponse.status()}`);
    console.log(`  Has layout: ${hasLayoutOnEvents}`);
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testLayoutRoute().catch(console.error);