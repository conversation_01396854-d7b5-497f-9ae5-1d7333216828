const puppeteer = require('puppeteer');

async function captureConsoleErrors() {
  const browser = await puppeteer.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Capture ALL console messages
  const logs = {
    errors: [],
    warnings: [],
    logs: [],
    network: []
  };
  
  page.on('console', msg => {
    const type = msg.type();
    const text = msg.text();
    const location = msg.location();
    
    if (type === 'error') {
      logs.errors.push({ text, location });
      console.log(`❌ ERROR: ${text}`);
      if (location.url) {
        console.log(`   at ${location.url}:${location.lineNumber}`);
      }
    } else if (type === 'warning') {
      logs.warnings.push({ text, location });
      console.log(`⚠️  WARNING: ${text}`);
    } else {
      logs.logs.push({ text });
    }
  });
  
  // Capture network failures
  page.on('requestfailed', request => {
    const failure = request.failure();
    logs.network.push({
      url: request.url(),
      reason: failure ? failure.errorText : 'Unknown'
    });
    console.log(`🌐 NETWORK ERROR: ${request.url()} - ${failure ? failure.errorText : 'Unknown'}`);
  });
  
  // Capture page errors
  page.on('pageerror', error => {
    logs.errors.push({ text: error.toString() });
    console.log(`📄 PAGE ERROR: ${error}`);
  });
  
  // Capture responses with errors
  page.on('response', response => {
    if (response.status() >= 400) {
      console.log(`🔴 HTTP ${response.status()}: ${response.url()}`);
    }
  });
  
  try {
    // 1. Navigate to login
    console.log('\n=== NAVIGATING TO LOGIN ===');
    await page.goto('http://0.0.0.0:5100/en', { waitUntil: 'networkidle2' });
    
    // 2. Login
    console.log('\n=== LOGGING IN ===');
    await page.type('#email', '<EMAIL>');
    await page.type('#password', '123456');
    await page.click('button[type="submit"]');
    await page.waitForNavigation({ waitUntil: 'networkidle2' });
    
    // 3. Check JWT token
    const token = await page.evaluate(() => localStorage.getItem('jwt_token'));
    console.log(`\n✅ JWT Token present: ${!!token}`);
    
    // 4. Test meeting form
    console.log('\n=== TESTING MEETING FORM ===');
    await page.goto('http://0.0.0.0:5100/en/meetings', { waitUntil: 'networkidle2' });
    await page.waitForTimeout(2000);
    
    // Try to open meeting form if there's a button
    const meetingButton = await page.$('button[data-action="new-meeting"], .btn-primary');
    if (meetingButton) {
      await meetingButton.click();
      await page.waitForTimeout(2000);
    }
    
    // 5. Test daily log
    console.log('\n=== TESTING DAILY LOG ===');
    await page.goto('http://0.0.0.0:5100/en/daily_logs', { waitUntil: 'networkidle2' });
    await page.waitForTimeout(2000);
    
    // 6. Test language change
    console.log('\n=== TESTING LANGUAGE CHANGE ===');
    const langSelector = await page.$('[data-action="change-language"], .language-selector');
    if (langSelector) {
      await langSelector.click();
      await page.waitForTimeout(1000);
    }
    
    // 7. Test logout
    console.log('\n=== TESTING LOGOUT ===');
    // Look for user dropdown
    const userDropdown = await page.$('.dropdown-toggle, [data-bs-toggle="dropdown"]');
    if (userDropdown) {
      await userDropdown.click();
      await page.waitForTimeout(500);
      
      const logoutLink = await page.$('a[href*="logout"], [data-action="logout"]');
      if (logoutLink) {
        await logoutLink.click();
        await page.waitForTimeout(2000);
      }
    }
    
    // Print summary
    console.log('\n=== SUMMARY ===');
    console.log(`Total Errors: ${logs.errors.length}`);
    console.log(`Total Warnings: ${logs.warnings.length}`);
    console.log(`Network Errors: ${logs.network.length}`);
    
    if (logs.errors.length > 0) {
      console.log('\n=== DETAILED ERRORS ===');
      logs.errors.forEach((err, i) => {
        console.log(`\nError ${i + 1}: ${err.text}`);
        if (err.location && err.location.url) {
          console.log(`Location: ${err.location.url}:${err.location.lineNumber}`);
        }
      });
    }
    
  } catch (error) {
    console.error('Test script error:', error);
  } finally {
    await browser.close();
  }
}

captureConsoleErrors().catch(console.error);