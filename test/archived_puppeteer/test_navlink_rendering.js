import puppeteer from 'puppeteer';

async function testNavLinkRendering() {
  console.log('=== Testing NavLink Component Rendering ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Login and navigate to SK dashboard...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    // Navigate to SK locale
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    console.log(`✅ Starting at: ${page.url()}`);
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\nStep 2: Analyze all sidebar elements...');
    
    const sidebarAnalysis = await page.evaluate(() => {
      const sidebar = document.querySelector('.sidebar');
      if (!sidebar) return { error: 'Sidebar not found' };
      
      // Get all elements in the sidebar
      const allElements = sidebar.querySelectorAll('*');
      const navItems = sidebar.querySelectorAll('.nav-item');
      
      const results = {
        totalElements: allElements.length,
        totalNavItems: navItems.length,
        routerLinks: [],
        aLinks: [],
        localizedLinks: [],
        navLinkComponents: []
      };
      
      // Check for router-link elements
      allElements.forEach(el => {
        if (el.tagName === 'ROUTER-LINK') {
          results.routerLinks.push({
            to: el.getAttribute('to'),
            text: el.textContent.trim(),
            classes: Array.from(el.classList)
          });
        }
      });
      
      // Check for a elements
      sidebar.querySelectorAll('a').forEach(el => {
        results.aLinks.push({
          href: el.href,
          text: el.textContent.trim(),
          hasLocalizedAttr: el.hasAttribute('data-localized-link-anchor'),
          classes: Array.from(el.classList)
        });
      });
      
      // Look for any elements with navlink-related attributes
      allElements.forEach(el => {
        if (el.hasAttribute('v-slot') || 
            el.textContent.includes('NavLink') ||
            el.hasAttribute('nav-link')) {
          results.navLinkComponents.push({
            tag: el.tagName,
            attributes: Array.from(el.attributes).map(attr => `${attr.name}="${attr.value}"`),
            text: el.textContent.trim()
          });
        }
      });
      
      return results;
    });
    
    console.log('Sidebar analysis results:');
    console.log(`Total elements: ${sidebarAnalysis.totalElements}`);
    console.log(`Total nav items: ${sidebarAnalysis.totalNavItems}`);
    console.log(`Router links found: ${sidebarAnalysis.routerLinks.length}`);
    console.log(`A links found: ${sidebarAnalysis.aLinks.length}`);
    
    if (sidebarAnalysis.routerLinks.length > 0) {
      console.log('\nRouter links:');
      sidebarAnalysis.routerLinks.forEach((link, i) => {
        console.log(`  ${i + 1}. ${link.text} → ${link.to}`);
      });
    }
    
    if (sidebarAnalysis.aLinks.length > 0) {
      console.log('\nA links:');
      sidebarAnalysis.aLinks.forEach((link, i) => {
        const type = link.hasLocalizedAttr ? '[LocalizedLink]' : '[NavLink]';
        console.log(`  ${i + 1}. ${type} ${link.text} → ${link.href}`);
      });
    }
    
    // Test clicking one of the a links to see behavior
    console.log('\nStep 3: Test clicking an a link...');
    
    const clickResult = await page.evaluate(() => {
      const eventsLink = Array.from(document.querySelectorAll('a')).find(a => 
        a.href && a.href.includes('/sk/events')
      );
      
      if (eventsLink) {
        // Store the original location
        const originalLocation = window.location.href;
        
        // Simulate click
        eventsLink.click();
        
        return {
          clicked: true,
          href: eventsLink.href,
          originalLocation: originalLocation,
          newLocation: window.location.href
        };
      }
      
      return { clicked: false };
    });
    
    if (clickResult.clicked) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      const finalUrl = page.url();
      console.log(`  ✅ Clicked link: ${clickResult.href}`);
      console.log(`  Final URL: ${finalUrl}`);
      
      if (finalUrl.includes('/sk/events')) {
        console.log('  ✅ Navigation successful via a tag');
      } else {
        console.log('  ❌ Navigation did not work');
      }
    } else {
      console.log('  ❌ Could not find events link to click');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testNavLinkRendering().catch(console.error);