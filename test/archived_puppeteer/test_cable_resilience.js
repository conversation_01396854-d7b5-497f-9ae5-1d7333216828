// Test script for cable service resilience
// Run this in the browser console after logging in

async function testCableResilience() {
  console.log('=== Cable Service Resilience Test ===')
  
  // Test 1: Basic connection
  console.log('\n1. Testing basic connection...')
  try {
    await cable.connect()
    console.log('✅ Basic connection successful')
  } catch (error) {
    console.error('❌ Basic connection failed:', error)
  }
  
  // Test 2: Multiple concurrent connection attempts
  console.log('\n2. Testing concurrent connection attempts...')
  const promises = []
  for (let i = 0; i < 5; i++) {
    promises.push(cable.connect())
  }
  try {
    const results = await Promise.all(promises)
    const uniqueConsumers = new Set(results)
    console.log(`✅ All promises resolved to same consumer: ${uniqueConsumers.size === 1}`)
  } catch (error) {
    console.error('❌ Concurrent connection test failed:', error)
  }
  
  // Test 3: Subscribe to a test channel
  console.log('\n3. Testing channel subscription...')
  const subscription = await cable.subscribe('NotificationChannel', {}, {
    connected: () => console.log('✅ Channel connected'),
    disconnected: () => console.log('⚠️ Channel disconnected'),
    received: (data) => console.log('📨 Received data:', data),
    connectionFailed: (error) => console.error('❌ Connection failed:', error)
  })
  
  if (subscription) {
    console.log('✅ Subscription created successfully')
  } else {
    console.log('❌ Subscription failed')
  }
  
  // Test 4: Disconnect and reconnect
  console.log('\n4. Testing disconnect/reconnect...')
  cable.disconnect()
  console.log('✅ Disconnected')
  
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  try {
    await cable.reconnect()
    console.log('✅ Reconnected successfully')
    
    // Check if subscriptions were restored
    const isConnected = cable.isConnected()
    console.log(`✅ Connection status: ${isConnected ? 'Connected' : 'Disconnected'}`)
    
    if (cable.subscriptions.size > 0) {
      console.log(`✅ Subscriptions restored: ${cable.subscriptions.size} active`)
    }
  } catch (error) {
    console.error('❌ Reconnection failed:', error)
  }
  
  // Test 5: Simulate connection with invalid token
  console.log('\n5. Testing error recovery...')
  const originalToken = jwtStorage.getToken()
  jwtStorage.setToken('invalid-token')
  
  try {
    await cable.reconnect()
    console.log('❌ Should have failed with invalid token')
  } catch (error) {
    console.log('✅ Correctly rejected invalid token:', error.message)
  }
  
  // Restore valid token
  jwtStorage.setToken(originalToken)
  
  // Test 6: Final reconnection
  console.log('\n6. Testing recovery after error...')
  try {
    await cable.reconnect()
    console.log('✅ Successfully recovered after error')
  } catch (error) {
    console.error('❌ Recovery failed:', error)
  }
  
  console.log('\n=== Test Complete ===')
  console.log('Check the results above for any failures')
}

// Run the test
testCableResilience()