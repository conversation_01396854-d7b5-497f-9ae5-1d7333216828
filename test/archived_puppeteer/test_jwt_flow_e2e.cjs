#!/usr/bin/env node

/**
 * End-to-End JWT Authentication Flow Test
 * 
 * This script tests the complete JWT authentication flow to ensure:
 * 1. JWT-first authentication (no session fallback)
 * 2. Proper Vue login component usage
 * 3. JWT token management and refresh
 * 4. Company switching with JWT
 * 5. Clean console output (no errors from Vite/dependencies)
 */

const puppeteer = require('puppeteer');
const fs = require('fs');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:5100',
  credentials: {
    email: '<EMAIL>',
    password: '123456'
  },
  headless: false, // Set to true for CI/automated runs
  slowMo: 100, // Slow down actions for debugging
  timeout: 30000
};

class JWTFlowTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      tests: [],
      startTime: new Date(),
      errors: [],
      warnings: []
    };
  }

  async setup() {
    console.log('🚀 Starting JWT Flow E2E Test...');
    
    this.browser = await puppeteer.launch({
      headless: TEST_CONFIG.headless,
      slowMo: TEST_CONFIG.slowMo,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security'
      ]
    });

    this.page = await this.browser.newPage();
    
    // Set larger viewport
    await this.page.setViewport({ width: 1280, height: 720 });
    
    // Enable console logging
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      
      if (type === 'error') {
        this.results.errors.push(text);
        console.log(`❌ Console Error: ${text}`);
      } else if (type === 'warn') {
        this.results.warnings.push(text);
        console.log(`⚠️ Console Warning: ${text}`);
      } else if (text.includes('JWT') || text.includes('auth')) {
        console.log(`📝 Auth Log: ${text}`);
      }
    });

    // Monitor network requests
    this.page.on('response', response => {
      const url = response.url();
      const status = response.status();
      
      if (url.includes('/api/v1/auth/') || url.includes('/api/v1/user') || url.includes('/api/v1/companies/switch')) {
        console.log(`🌐 API: ${status} ${url}`);
      }
      
      if (status >= 400) {
        console.log(`❌ Failed Request: ${status} ${url}`);
      }
    });
  }

  async addResult(testName, success, details = '') {
    this.results.tests.push({
      name: testName,
      success,
      details,
      timestamp: new Date()
    });
    
    const emoji = success ? '✅' : '❌';
    console.log(`${emoji} ${testName}: ${details}`);
  }

  async testViteConfiguration() {
    try {
      await this.page.goto(TEST_CONFIG.baseUrl, { waitUntil: 'networkidle0', timeout: TEST_CONFIG.timeout });
      
      // Check if critical resources loaded successfully
      const failedResources = await this.page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        return resources.filter(r => r.transferSize === 0 && r.name.includes('node_modules')).length;
      });
      
      await this.addResult(
        'Vite Configuration', 
        failedResources === 0, 
        failedResources === 0 ? 'All dependencies loaded' : `${failedResources} dependencies failed to load`
      );
      
      return failedResources === 0;
    } catch (error) {
      await this.addResult('Vite Configuration', false, error.message);
      return false;
    }
  }

  async testVueLoginComponent() {
    try {
      // Navigate to Czech locale login (should load Vue SPA)
      await this.page.goto(`${TEST_CONFIG.baseUrl}/cs/users/sign_in`, { waitUntil: 'networkidle0' });
      
      // Wait for Vue app to initialize
      await this.page.waitForSelector('#app', { timeout: 10000 });
      
      // Check if it's the Vue login component (not Rails form)
      const isVueLogin = await this.page.evaluate(() => {
        const app = document.querySelector('#app');
        // Look for Vue-specific attributes or components
        return app && (
          app.querySelector('[data-v-]') || // Vue scoped CSS attributes
          app.querySelector('.vue-login') || // Vue login component class
          app.innerHTML.includes('v-') // Vue directives
        );
      });
      
      await this.addResult(
        'Vue Login Component', 
        isVueLogin, 
        isVueLogin ? 'Vue login component loaded' : 'Rails login form detected'
      );
      
      return isVueLogin;
    } catch (error) {
      await this.addResult('Vue Login Component', false, error.message);
      return false;
    }
  }

  async testJWTFirstLogin() {
    try {
      // Clear any existing auth state
      await this.page.evaluate(() => {
        localStorage.clear();
        sessionStorage.clear();
      });
      
      // Navigate to login page
      await this.page.goto(`${TEST_CONFIG.baseUrl}/cs/users/sign_in`, { waitUntil: 'networkidle0' });
      
      // Look for login form fields
      const emailField = await this.page.$('input[type="email"], input[name*="email"]');
      const passwordField = await this.page.$('input[type="password"], input[name*="password"]');
      const submitButton = await this.page.$('button[type="submit"], input[type="submit"], .btn');
      
      if (!emailField || !passwordField || !submitButton) {
        throw new Error('Login form fields not found');
      }
      
      // Fill login form
      await this.page.type('input[type="email"], input[name*="email"]', TEST_CONFIG.credentials.email);
      await this.page.type('input[type="password"], input[name*="password"]', TEST_CONFIG.credentials.password);
      
      // Monitor for JWT API calls
      let jwtLoginCalled = false;
      this.page.on('response', response => {
        if (response.url().includes('/api/v1/auth/jwt_login')) {
          jwtLoginCalled = true;
        }
      });
      
      // Submit form
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 15000 }),
        this.page.click('button[type="submit"], input[type="submit"], .btn')
      ]);
      
      // Check if JWT token is stored
      const hasJWTToken = await this.page.evaluate(() => {
        return localStorage.getItem('access_token') !== null;
      });
      
      // Check if we're redirected to dashboard (successful login)
      const currentUrl = this.page.url();
      const isOnDashboard = currentUrl.includes('/dashboard') || currentUrl.includes('/mainbox');
      
      const success = jwtLoginCalled && hasJWTToken && isOnDashboard;
      await this.addResult(
        'JWT-First Login', 
        success, 
        `JWT API: ${jwtLoginCalled}, Token stored: ${hasJWTToken}, Dashboard: ${isOnDashboard}`
      );
      
      return success;
    } catch (error) {
      await this.addResult('JWT-First Login', false, error.message);
      return false;
    }
  }

  async testJWTCompanySwitch() {
    try {
      // First get current company and available companies
      const currentCompany = await this.page.evaluate(async () => {
        const response = await fetch('/api/v1/companies', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Accept': 'application/json'
          }
        });
        return response.json();
      });
      
      if (!currentCompany.company_user_roles || currentCompany.company_user_roles.length < 2) {
        await this.addResult('JWT Company Switch', true, 'Only one company available - skipping test');
        return true;
      }
      
      // Find a different company to switch to
      const targetCompany = currentCompany.company_user_roles.find(
        role => role.company.id !== currentCompany.current_tenant
      );
      
      if (!targetCompany) {
        await this.addResult('JWT Company Switch', true, 'No alternative company found - skipping test');
        return true;
      }
      
      // Perform company switch via API
      const switchResult = await this.page.evaluate(async (companyId) => {
        const response = await fetch('/api/v1/companies/switch_company', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ company_id: companyId })
        });
        return response.json();
      }, targetCompany.company.id);
      
      // Check if new token was received and stored
      const hasNewToken = switchResult.success && switchResult.access_token;
      
      // Verify the token was updated in localStorage
      const updatedToken = await this.page.evaluate(() => localStorage.getItem('access_token'));
      const tokenUpdated = updatedToken === switchResult.access_token;
      
      const success = hasNewToken && tokenUpdated;
      await this.addResult(
        'JWT Company Switch', 
        success, 
        `Switch success: ${switchResult.success}, New token: ${hasNewToken}, Token updated: ${tokenUpdated}`
      );
      
      return success;
    } catch (error) {
      await this.addResult('JWT Company Switch', false, error.message);
      return false;
    }
  }

  async testJWTTokenRefresh() {
    try {
      // Get current token
      const originalToken = await this.page.evaluate(() => localStorage.getItem('access_token'));
      
      if (!originalToken) {
        throw new Error('No JWT token found to test refresh');
      }
      
      // Call refresh endpoint
      const refreshResult = await this.page.evaluate(async () => {
        const response = await fetch('/api/v1/auth/refresh_token', {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include' // Include HttpOnly cookies
        });
        return response.json();
      });
      
      // Check if refresh was successful
      const refreshSuccess = refreshResult.success && refreshResult.access_token;
      
      // Verify new token is different from original
      const newTokenDifferent = refreshResult.access_token !== originalToken;
      
      const success = refreshSuccess && newTokenDifferent;
      await this.addResult(
        'JWT Token Refresh', 
        success, 
        `Refresh success: ${refreshSuccess}, Token changed: ${newTokenDifferent}`
      );
      
      return success;
    } catch (error) {
      await this.addResult('JWT Token Refresh', false, error.message);
      return false;
    }
  }

  async testConsoleErrors() {
    // Count unique errors (excluding HMR polling which is expected)
    const criticalErrors = this.results.errors.filter(error => 
      !error.includes('vite-dev') && 
      !error.includes('WebSocket') && 
      !error.includes('Failed to fetch') &&
      error.trim().length > 0
    );
    
    const success = criticalErrors.length === 0;
    await this.addResult(
      'Console Errors', 
      success, 
      success ? 'No critical console errors' : `${criticalErrors.length} critical errors found`
    );
    
    return success;
  }

  async generateReport() {
    const endTime = new Date();
    const duration = endTime - this.results.startTime;
    
    const report = {
      summary: {
        totalTests: this.results.tests.length,
        passed: this.results.tests.filter(t => t.success).length,
        failed: this.results.tests.filter(t => !t.success).length,
        duration: `${Math.round(duration / 1000)}s`,
        timestamp: this.results.startTime.toISOString()
      },
      tests: this.results.tests,
      errors: this.results.errors.slice(0, 10), // Limit errors in report
      warnings: this.results.warnings.slice(0, 10) // Limit warnings in report
    };
    
    // Write detailed report to file
    const reportPath = `/home/<USER>/Projects/attendifyapp/jwt_e2e_test_report_${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`⏱️ Duration: ${report.summary.duration}`);
    console.log(`📄 Report saved: ${reportPath}`);
    
    return report;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runAllTests() {
    try {
      await this.setup();
      
      // Run tests in sequence
      await this.testViteConfiguration();
      await this.testVueLoginComponent();
      await this.testJWTFirstLogin();
      await this.testJWTCompanySwitch();
      await this.testJWTTokenRefresh();
      await this.testConsoleErrors();
      
      const report = await this.generateReport();
      
      // Return overall success
      return report.summary.failed === 0;
      
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const tester = new JWTFlowTester();
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = JWTFlowTester;