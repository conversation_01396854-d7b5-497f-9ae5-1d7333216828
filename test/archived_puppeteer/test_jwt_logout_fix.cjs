#!/usr/bin/env node

const puppeteer = require('puppeteer');

async function testLogoutFix() {
  const browser = await puppeteer.launch({
    headless: false,
    devtools: true
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('Browser console error:', msg.text());
      }
    });
    
    console.log('1. Navigating to login page...');
    await page.goto('http://localhost:5100/cs/login');
    await page.waitForSelector('input[type="email"]', { timeout: 5000 });
    
    console.log('2. Logging in...');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', '123456');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    console.log('3. Login successful, current URL:', page.url());
    
    // Check localStorage and cookies
    const authData = await page.evaluate(() => {
      return {
        token: localStorage.getItem('jwt_token'),
        cookies: document.cookie
      };
    });
    console.log('4. Auth data after login:', authData);
    
    // Wait a moment to ensure everything is loaded
    await page.waitForTimeout(2000);
    
    console.log('5. Clicking logout...');
    // Find and click logout button
    await page.waitForSelector('[data-test-id="logout-button"], button:has-text("Odhlásit"), a:has-text("Odhlásit"), [href*="logout"]', { timeout: 5000 });
    await page.click('[data-test-id="logout-button"], button:has-text("Odhlásit"), a:has-text("Odhlásit"), [href*="logout"]');
    
    // Wait for redirect after logout
    await page.waitForNavigation({ waitUntil: 'networkidle0' });
    console.log('6. After logout, current URL:', page.url());
    
    // Check if localStorage and cookies are cleared
    const authDataAfterLogout = await page.evaluate(() => {
      return {
        token: localStorage.getItem('jwt_token'),
        cookies: document.cookie
      };
    });
    console.log('7. Auth data after logout:', authDataAfterLogout);
    
    // Navigate to root path to test auto-login issue
    console.log('8. Navigating to root path...');
    await page.goto('http://localhost:5100/');
    await page.waitForTimeout(2000);
    
    const finalUrl = page.url();
    console.log('9. Final URL after navigating to root:', finalUrl);
    
    // Check if we're redirected to login (expected) or auto-logged in (bug)
    if (finalUrl.includes('/login')) {
      console.log('✅ SUCCESS: Properly redirected to login page - logout fix is working!');
    } else if (finalUrl.includes('/dashboard') || finalUrl.includes('/mainbox')) {
      console.log('❌ FAILURE: Auto-logged in after logout - session cleanup bug still exists!');
      
      // Get more debug info
      const debugInfo = await page.evaluate(() => {
        return {
          localStorage: { ...localStorage },
          sessionStorage: { ...sessionStorage },
          cookies: document.cookie
        };
      });
      console.log('Debug info:', debugInfo);
    } else {
      console.log('⚠️  UNEXPECTED: Ended up at unexpected URL:', finalUrl);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    console.log('\nPress Ctrl+C to close browser and exit...');
    // Keep browser open for manual inspection
    await new Promise(() => {});
  }
}

testLogoutFix().catch(console.error);