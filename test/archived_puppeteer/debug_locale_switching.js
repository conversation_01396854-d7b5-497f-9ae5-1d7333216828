import puppeteer from 'puppeteer';

async function debugLocaleSwitching() {
  console.log('=== Debugging Locale Switching Issues ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Log all requests to see what's happening
    page.on('request', request => {
      if (request.url().includes('cs') || request.url().includes('sk')) {
        console.log(`REQUEST: ${request.method()} ${request.url()}`);
      }
    });
    
    // Log console messages
    page.on('console', msg => {
      if (msg.text().includes('locale')) {
        console.log(`BROWSER: ${msg.text()}`);
      }
    });
    
    console.log('Step 1: Logging in...');
    
    // Navigate to login page and login
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Successfully logged in\n');
    
    // Test sequence: CS → SK → CS to see locale persistence
    const testSequence = [
      { locale: 'cs', route: 'dashboard', step: 'CS Dashboard' },
      { locale: 'sk', route: 'dashboard', step: 'SK Dashboard' },
      { locale: 'sk', route: 'contracts', step: 'SK Contracts (problematic)' },
      { locale: 'cs', route: 'contracts', step: 'CS Contracts' },
      { locale: 'sk', route: 'companies/1/edit', step: 'SK Company Edit (problematic)' }
    ];
    
    for (const test of testSequence) {
      console.log(`\nStep: ${test.step}`);
      console.log(`Navigating to /${test.locale}/${test.route}...`);
      
      await page.goto(`http://0.0.0.0:5100/${test.locale}/${test.route}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      // Wait for Vue to process
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check current state
      const state = await page.evaluate(() => {
        return {
          url: window.location.href,
          pathname: window.location.pathname,
          routerAvailable: !!window.router,
          currentRoute: window.router ? {
            path: window.router.currentRoute.value.path,
            fullPath: window.router.currentRoute.value.fullPath,
            params: window.router.currentRoute.value.params,
            name: window.router.currentRoute.value.name
          } : null,
          locale: window.router?.currentRoute?.value?.params?.locale,
          i18nLocale: window.i18n ? window.i18n.global.locale : 'not available'
        };
      });
      
      console.log(`  URL: ${state.url}`);
      console.log(`  Expected locale: ${test.locale}`);
      console.log(`  Router locale: ${state.locale}`);
      console.log(`  i18n locale: ${state.i18nLocale}`);
      console.log(`  Route name: ${state.currentRoute?.name}`);
      
      if (state.locale !== test.locale) {
        console.log(`  ❌ LOCALE MISMATCH: Expected ${test.locale}, got ${state.locale}`);
        
        // Check cookies and local storage
        const browserState = await page.evaluate(() => {
          return {
            cookies: document.cookie,
            localStorage: Object.keys(localStorage).reduce((acc, key) => {
              acc[key] = localStorage.getItem(key);
              return acc;
            }, {}),
            sessionStorage: Object.keys(sessionStorage).reduce((acc, key) => {
              acc[key] = sessionStorage.getItem(key);
              return acc;
            }, {})
          };
        });
        
        console.log(`  Browser state:`, browserState);
      } else {
        console.log(`  ✅ Locale correct`);
      }
    }
    
  } catch (error) {
    console.error('Debug failed:', error);
  } finally {
    await browser.close();
  }
}

debugLocaleSwitching().catch(console.error);