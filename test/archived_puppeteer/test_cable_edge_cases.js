// Edge case testing for cable service
// This tests scenarios that might not trigger standard handlers

async function testCableEdgeCases() {
  console.log('=== Cable Service Edge Case Testing ===')
  
  // Save original createConsumer for restoration
  const originalCreateConsumer = window.ActionCable.createConsumer
  
  // Test 1: createConsumer throws synchronous error
  console.log('\n1. Testing synchronous createConsumer error...')
  window.ActionCable.createConsumer = () => {
    throw new Error('Simulated createConsumer failure')
  }
  
  try {
    await cable.reconnect()
    console.log('❌ Should have failed')
  } catch (error) {
    console.log('✅ Correctly caught synchronous error:', error.message)
  }
  
  // Verify we can recover
  window.ActionCable.createConsumer = originalCreateConsumer
  try {
    await cable.reconnect()
    console.log('✅ Recovered from synchronous error')
  } catch (error) {
    console.error('❌ Failed to recover:', error)
  }
  
  // Test 2: Connection that never resolves (no events fired)
  console.log('\n2. Testing connection that never resolves...')
  window.ActionCable.createConsumer = (url) => {
    // Create a mock consumer that never fires any events
    return {
      connection: {
        events: {},
        isOpen: () => false,
        disconnect: () => {}
      },
      subscriptions: {
        create: () => {},
        remove: () => {}
      }
    }
  }
  
  const timeoutPromise = new Promise((resolve) => {
    setTimeout(() => resolve('timeout'), 35000) // Wait longer than cable timeout
  })
  
  const connectPromise = cable.reconnect().catch(error => error)
  
  console.log('Waiting for timeout (this will take 30 seconds)...')
  const result = await Promise.race([connectPromise, timeoutPromise])
  
  if (result instanceof Error && result.message === 'Connection timeout') {
    console.log('✅ Connection correctly timed out')
  } else {
    console.log('❌ Unexpected result:', result)
  }
  
  // Test 3: Verify we can connect again after timeout
  console.log('\n3. Testing recovery after timeout...')
  window.ActionCable.createConsumer = originalCreateConsumer
  
  try {
    await cable.reconnect()
    console.log('✅ Successfully recovered after timeout')
  } catch (error) {
    console.error('❌ Failed to recover after timeout:', error)
  }
  
  // Test 4: Rapid successive connection attempts
  console.log('\n4. Testing rapid successive attempts...')
  const rapidPromises = []
  for (let i = 0; i < 10; i++) {
    rapidPromises.push(
      cable.connect()
        .then(() => `success-${i}`)
        .catch(error => `error-${i}: ${error.message}`)
    )
  }
  
  const rapidResults = await Promise.all(rapidPromises)
  const uniqueResults = new Set(rapidResults)
  console.log(`✅ All ${rapidPromises.length} attempts resolved to ${uniqueResults.size} unique result(s)`)
  console.log('Results:', Array.from(uniqueResults))
  
  // Test 5: Connection promise cleanup verification
  console.log('\n5. Testing connection promise cleanup...')
  
  // Force a failure
  window.ActionCable.createConsumer = () => {
    throw new Error('Forced failure for cleanup test')
  }
  
  try {
    await cable.reconnect()
  } catch (error) {
    // Expected to fail
  }
  
  // Check if connectionPromise was cleared
  if (cable.connectionPromise === null) {
    console.log('✅ connectionPromise properly cleared after failure')
  } else {
    console.error('❌ connectionPromise not cleared!', cable.connectionPromise)
  }
  
  // Restore and verify we can connect
  window.ActionCable.createConsumer = originalCreateConsumer
  try {
    await cable.connect()
    console.log('✅ Can connect after promise cleanup')
  } catch (error) {
    console.error('❌ Cannot connect after cleanup:', error)
  }
  
  console.log('\n=== Edge Case Testing Complete ===')
  console.log('The robust .finally() implementation should pass all tests')
}

// Run the edge case tests
// Note: Test 2 will take 30 seconds due to timeout testing
testCableEdgeCases()