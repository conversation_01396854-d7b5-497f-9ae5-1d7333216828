import puppeteer from 'puppeteer';

async function test404Handling() {
  console.log('=== Testing 404 Handling ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // First login
    console.log('Step 1: Login...');
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Logged in successfully\n');
    
    // Test various non-existent routes
    const testRoutes = [
      '/cs/non-existent',
      '/cs/fake-route',
      '/cs/bookings/invalid-id/edit',
      '/sk/doesnt-exist',
      '/en/404-test'
    ];
    
    console.log('Step 2: Testing non-existent routes...\n');
    
    for (const route of testRoutes) {
      console.log(`Testing: ${route}`);
      
      await page.goto(`http://0.0.0.0:5100${route}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      // Wait for Vue app to render
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if 404 content is displayed
      const pageContent = await page.evaluate(() => {
        return {
          title: document.title,
          has404: document.body.innerText.includes('404'),
          hasNotFound: document.body.innerText.includes('Page Not Found') || 
                       document.body.innerText.includes('page not found'),
          bodyText: document.body.innerText.substring(0, 200)
        };
      });
      
      if (pageContent.has404 || pageContent.hasNotFound) {
        console.log(`✅ 404 page displayed correctly`);
      } else {
        console.log(`❌ 404 page not displayed`);
        console.log(`   Page content: ${pageContent.bodyText}...`);
      }
      
      // Check if we're still in SPA (not redirected to login)
      const currentUrl = page.url();
      if (currentUrl.includes('/users/sign_in')) {
        console.log(`⚠️  Redirected to login - auth lost`);
      }
      
      console.log('');
    }
    
    console.log('Step 3: Test valid routes still work...\n');
    
    const validRoutes = ['/cs/dashboard', '/cs/events', '/cs/bookings'];
    
    for (const route of validRoutes) {
      await page.goto(`http://0.0.0.0:5100${route}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const has404 = await page.evaluate(() => {
        return document.body.innerText.includes('404');
      });
      
      if (!has404) {
        console.log(`✅ ${route} - loads correctly (no 404)`);
      } else {
        console.log(`❌ ${route} - shows 404 (should be valid)`);
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

test404Handling().catch(console.error);