import puppeteer from 'puppeteer';

async function testAuthFlow() {
  console.log('=== Testing Authentication Flow with Full Router ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Attempt to access protected route without authentication...');
    await page.goto('http://0.0.0.0:5100/cs/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    // Check if we were redirected to login
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);
    
    if (currentUrl.includes('/users/sign_in')) {
      console.log('✅ Successfully redirected to login page');
      
      // Check if redirect query param is preserved
      const url = new URL(currentUrl);
      const redirectParam = url.searchParams.get('redirect');
      console.log(`Redirect param: ${redirectParam}`);
      
      if (redirectParam === '/cs/dashboard') {
        console.log('✅ Redirect parameter preserved correctly');
      } else {
        console.log('❌ Redirect parameter not preserved correctly');
      }
    } else {
      console.log('❌ Not redirected to login page');
    }
    
    console.log('\nStep 2: Login with valid credentials...');
    
    // Fill in login form
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    // Submit form
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log(`After login URL: ${page.url()}`);
    
    // Check if we're on the dashboard
    if (page.url().includes('/dashboard')) {
      console.log('✅ Successfully logged in and redirected to dashboard');
    } else {
      console.log('❌ Login redirect failed');
    }
    
    console.log('\nStep 3: Test Vue Router navigation guard...');
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if Vue Router is available
    const hasVueRouter = await page.evaluate(() => {
      return typeof window.$router !== 'undefined';
    });
    
    console.log(`Vue Router available: ${hasVueRouter ? '✅' : '❌'}`);
    
    // Check if user is authenticated in store
    const isAuthenticated = await page.evaluate(() => {
      if (window.$store) {
        return window.$store.getters['userStore/isAuthenticated'];
      }
      return false;
    });
    
    console.log(`User authenticated in store: ${isAuthenticated ? '✅' : '❌'}`);
    
    console.log('\nStep 4: Test navigation to different protected routes...');
    
    const protectedRoutes = [
      '/cs/events',
      '/cs/bookings',
      '/cs/meetings',
      '/cs/works'
    ];
    
    for (const route of protectedRoutes) {
      await page.goto(`http://0.0.0.0:5100${route}`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const routeUrl = page.url();
      if (routeUrl.includes(route)) {
        console.log(`✅ ${route} - Access granted`);
      } else if (routeUrl.includes('/users/sign_in')) {
        console.log(`❌ ${route} - Redirected to login (auth failed)`);
      } else {
        console.log(`⚠️ ${route} - Unexpected redirect to ${routeUrl}`);
      }
    }
    
    console.log('\nStep 5: Test 404 handling...');
    
    await page.goto('http://0.0.0.0:5100/cs/non-existent-route', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    // Check if 404 component is rendered
    const has404Content = await page.evaluate(() => {
      const body = document.body.innerText;
      return body.includes('404') || body.includes('Page Not Found');
    });
    
    console.log(`404 page displayed: ${has404Content ? '✅' : '❌'}`);
    
    console.log('\nStep 6: Test locale switching...');
    
    const locales = ['cs', 'sk', 'en'];
    for (const locale of locales) {
      await page.goto(`http://0.0.0.0:5100/${locale}/dashboard`, {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const localeUrl = page.url();
      if (localeUrl.includes(`/${locale}/`)) {
        console.log(`✅ ${locale} locale - Navigation successful`);
      } else {
        console.log(`❌ ${locale} locale - Navigation failed`);
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testAuthFlow().catch(console.error);