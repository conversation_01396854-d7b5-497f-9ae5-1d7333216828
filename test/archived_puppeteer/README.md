# Archived Puppeteer Tests

These Puppeteer-based tests have been replaced by the enhanced Capybara+Cuprite setup optimized for Claude Code Agent.

## Why Archived?

1. **Better Integration**: Capybara integrates natively with Rails test suite
2. **LAN Support**: Enhanced for 192.168.1.51:5100 development setup  
3. **Faster**: Cuprite (Chrome headless) is faster than Puppeteer for Rails apps
4. **Claude Optimized**: Custom test helpers provide semantic testing methods

## New Testing Approach

Use the enhanced Capybara setup in `spec/support/`:
- `capybara.rb` - Enhanced configuration with LAN support
- `claude_test_helpers.rb` - Semantic testing methods for Claude

## Test Commands

```bash
# Standard feature testing
bundle exec rspec spec/features/

# Remote testing against live server
REMOTE_TESTING=true bundle exec rspec spec/features/

# Visual debugging  
HEADLESS=false bundle exec rspec spec/features/specific_test_spec.rb
```

## Migration Complete

All functionality from these Puppeteer tests has been integrated into the RSpec+Capybara test suite with enhanced Claude Code Agent support.