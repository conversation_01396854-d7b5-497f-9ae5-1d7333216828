import puppeteer from 'puppeteer';

async function testDynamicLocaleRoutes() {
  console.log('=== Testing Dynamic Locale Routes ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const locales = ['cs', 'sk', 'en'];
  const routes = [
    'dashboard',
    'events', 
    'bookings',
    'companies',
    'works'
  ];
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = [];
  
  try {
    const page = await browser.newPage();
    
    // Suppress console logs for cleaner output
    page.on('console', () => {});
    page.on('error', () => {});
    page.on('pageerror', () => {});
    
    console.log(`Testing ${locales.length} locales × ${routes.length} routes = ${locales.length * routes.length} combinations\n`);
    
    for (const locale of locales) {
      console.log(`Testing locale: ${locale.toUpperCase()}`);
      
      for (const route of routes) {
        totalTests++;
        const testPath = `/${locale}/${route}`;
        
        try {
          const response = await page.goto(`http://0.0.0.0:5100${testPath}`, {
            waitUntil: 'networkidle2',
            timeout: 10000
          });
          
          const status = response.status();
          
          // Check if Vue Router properly parsed locale
          const routerInfo = await page.evaluate(() => {
            if (window.router && window.router.currentRoute) {
              return {
                available: true,
                currentPath: window.router.currentRoute.value.path,
                params: window.router.currentRoute.value.params
              };
            }
            return { available: false };
          });
          
          const passed = status === 200 && 
                        routerInfo.available && 
                        routerInfo.params?.locale === locale;
          
          if (passed) {
            console.log(`  ✓ ${testPath}`);
            passedTests++;
          } else {
            console.log(`  ✗ ${testPath} - Status: ${status}, Locale: ${routerInfo.params?.locale}`);
            failedTests.push({
              path: testPath,
              status: status,
              expected: locale,
              actual: routerInfo.params?.locale
            });
          }
        } catch (err) {
          console.log(`  ✗ ${testPath} - Error: ${err.message}`);
          failedTests.push({
            path: testPath,
            error: err.message
          });
        }
      }
      console.log('');
    }
    
    // Test root redirects
    console.log('Testing root redirects...');
    for (const locale of locales) {
      totalTests++;
      try {
        const response = await page.goto(`http://0.0.0.0:5100/${locale}`, {
          waitUntil: 'networkidle2',
          timeout: 10000
        });
        
        const finalUrl = page.url();
        const expectedRedirect = `/${locale}/dashboard`;
        const passed = finalUrl.includes(expectedRedirect);
        
        if (passed) {
          console.log(`  ✓ /${locale} → ${expectedRedirect}`);
          passedTests++;
        } else {
          console.log(`  ✗ /${locale} → ${finalUrl} (expected ${expectedRedirect})`);
          failedTests.push({
            path: `/${locale}`,
            expected: expectedRedirect,
            actual: finalUrl
          });
        }
      } catch (err) {
        console.log(`  ✗ /${locale} - Error: ${err.message}`);
        failedTests.push({
          path: `/${locale}`,
          error: err.message
        });
      }
    }
    
    console.log('\n=== Test Summary ===');
    console.log(`Total tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests.length}`);
    console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests.length > 0) {
      console.log('\nFailed tests:');
      failedTests.forEach(test => {
        console.log(`  - ${test.path}: ${test.error || `Expected ${test.expected}, got ${test.actual}`}`);
      });
    } else {
      console.log('\n🎉 All dynamic locale routes working perfectly!');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testDynamicLocaleRoutes().catch(console.error);