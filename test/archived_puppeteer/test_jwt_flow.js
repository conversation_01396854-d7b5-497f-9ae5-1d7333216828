const puppeteer = require('puppeteer');

async function testJWTFlow() {
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  const page = await browser.newPage();
  
  // Collect console messages
  const consoleLogs = [];
  page.on('console', msg => {
    consoleLogs.push({
      type: msg.type(),
      text: msg.text(),
      location: msg.location()
    });
  });
  
  // Collect network errors
  const networkErrors = [];
  page.on('requestfailed', request => {
    networkErrors.push({
      url: request.url(),
      failure: request.failure()
    });
  });
  
  // Collect page errors
  const pageErrors = [];
  page.on('pageerror', error => {
    pageErrors.push(error.toString());
  });
  
  try {
    // Navigate to login page
    console.log('Navigating to login page...');
    await page.goto('http://localhost:3000/en/login', { waitUntil: 'networkidle2' });
    
    // Wait for login form
    await page.waitForSelector('input[type="email"]', { timeout: 5000 });
    
    // Fill login form
    console.log('Filling login form...');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', '123456');
    
    // Submit form
    console.log('Submitting login...');
    await page.click('button[type="submit"]');
    
    // Wait for navigation or API response
    await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 }).catch(() => {
      console.log('Navigation timeout - checking if SPA handled the login');
    });
    
    // Wait a bit for all API calls to complete
    await page.waitForTimeout(3000);
    
    // Check if we're logged in by looking for user menu or checking localStorage
    const jwtToken = await page.evaluate(() => {
      return localStorage.getItem('jwt_token');
    });
    
    console.log('JWT Token present:', !!jwtToken);
    
    // Print console logs
    console.log('\n=== Console Logs ===');
    consoleLogs.forEach(log => {
      if (log.type === 'error' || log.type === 'warning') {
        console.log(`${log.type.toUpperCase()}: ${log.text}`);
        if (log.location.url) {
          console.log(`  at ${log.location.url}:${log.location.lineNumber}`);
        }
      }
    });
    
    // Print network errors
    if (networkErrors.length > 0) {
      console.log('\n=== Network Errors ===');
      networkErrors.forEach(error => {
        console.log(`Failed: ${error.url}`);
        console.log(`  Reason: ${error.failure.errorText}`);
      });
    }
    
    // Print page errors
    if (pageErrors.length > 0) {
      console.log('\n=== Page Errors ===');
      pageErrors.forEach(error => {
        console.log(error);
      });
    }
    
    // Take screenshot
    await page.screenshot({ path: 'jwt_test_result.png' });
    console.log('Screenshot saved as jwt_test_result.png');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testJWTFlow().catch(console.error);