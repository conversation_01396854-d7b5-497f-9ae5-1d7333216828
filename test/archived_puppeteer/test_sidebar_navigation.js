import puppeteer from 'puppeteer';

async function testSidebarNavigation() {
  console.log('=== Testing Sidebar Navigation Links ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Login and navigate to SK dashboard...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    // Navigate to SK locale
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    console.log(`✅ Current URL: ${page.url()}`);
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\nStep 2: Check sidebar navigation links...');
    
    // Get all sidebar navigation links
    const sidebarLinks = await page.evaluate(() => {
      const navItems = document.querySelectorAll('.nav-item');
      const links = [];
      
      navItems.forEach((item, index) => {
        const linkElement = item.tagName === 'A' ? item : item.querySelector('a');
        if (linkElement) {
          links.push({
            index: index,
            text: item.textContent.trim(),
            href: linkElement.href,
            tagName: linkElement.tagName,
            isRouterLink: linkElement.classList.contains('router-link') || linkElement.tagName === 'ROUTER-LINK',
            classes: Array.from(linkElement.classList)
          });
        }
      });
      
      return links;
    });
    
    console.log('Found navigation links:');
    sidebarLinks.forEach(link => {
      const localeMatch = link.href.match(/\/(cs|sk|en)\//);
      const locale = localeMatch ? localeMatch[1] : 'NONE';
      const correct = locale === 'sk';
      
      console.log(`  ${correct ? '✅' : '❌'} ${link.text}: ${link.href} (locale: ${locale})`);
    });
    
    console.log('\nStep 3: Test clicking a sidebar link...');
    
    // Try clicking the Events link
    const eventsLinkClicked = await page.evaluate(() => {
      const eventsLink = Array.from(document.querySelectorAll('.nav-item')).find(item => 
        item.textContent.includes('Kalendář') || item.textContent.includes('agenda')
      );
      
      if (eventsLink) {
        const linkElement = eventsLink.tagName === 'A' ? eventsLink : eventsLink.querySelector('a');
        if (linkElement) {
          linkElement.click();
          return { clicked: true, href: linkElement.href };
        }
      }
      return { clicked: false };
    });
    
    if (eventsLinkClicked.clicked) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      const newUrl = page.url();
      const expectedUrl = 'http://0.0.0.0:5100/sk/events';
      const correct = newUrl === expectedUrl;
      
      console.log(`  Click result: ${newUrl}`);
      console.log(`  Expected: ${expectedUrl}`);
      console.log(`  ${correct ? '✅' : '❌'} Navigation ${correct ? 'correct' : 'incorrect'}`);
    } else {
      console.log('  ❌ Could not find Events link to click');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testSidebarNavigation().catch(console.error);