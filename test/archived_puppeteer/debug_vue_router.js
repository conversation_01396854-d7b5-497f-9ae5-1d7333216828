import puppeteer from 'puppeteer';

async function debugVueRouter() {
  console.log('=== Debugging Vue Router State ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,  // Show browser for debugging
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    devtools: true
  });
  
  const page = await browser.newPage();
  
  // Add console logging
  page.on('console', msg => {
    console.log('BROWSER:', msg.text());
  });
  
  try {
    console.log('Navigating to /cs/dashboard...');
    const response = await page.goto('http://0.0.0.0:5100/cs/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 15000
    });
    
    console.log(`Response status: ${response.status()}`);
    
    // Wait a bit for Vue to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check what's available in the global scope
    const globalCheck = await page.evaluate(() => {
      return {
        hasVue: typeof window.Vue !== 'undefined',
        hasRouter: typeof window.router !== 'undefined',
        hasApp: typeof window.app !== 'undefined',
        appElement: !!document.getElementById('app'),
        routerConstructor: window.router ? window.router.constructor.name : 'not available'
      };
    });
    
    console.log('Global state:', globalCheck);
    
    if (globalCheck.hasRouter) {
      // Check router state
      const routerState = await page.evaluate(() => {
        try {
          const router = window.router;
          return {
            currentRoute: router.currentRoute.value,
            routes: router.getRoutes().map(r => ({ path: r.path, name: r.name })),
            resolved: router.resolve('/cs/dashboard')
          };
        } catch (error) {
          return { error: error.message };
        }
      });
      
      console.log('Router state:', JSON.stringify(routerState, null, 2));
    } else {
      console.log('Router not available - checking Vue app state');
      
      // Check if Vue app is mounted
      const vueState = await page.evaluate(() => {
        const appEl = document.getElementById('app');
        return {
          hasAppElement: !!appEl,
          appContent: appEl ? appEl.innerHTML.substring(0, 200) : 'not found',
          scripts: Array.from(document.scripts).map(s => s.src).filter(s => s.includes('application'))
        };
      });
      
      console.log('Vue state:', vueState);
    }
    
    // Check the actual URL and page content
    const pageInfo = await page.evaluate(() => {
      return {
        currentUrl: window.location.href,
        pathname: window.location.pathname,
        title: document.title,
        hasLayoutComponents: {
          sidebar: !!document.querySelector('[class*="sidebar"]'),
          topbar: !!document.querySelector('[class*="topbar"]')
        }
      };
    });
    
    console.log('Page info:', pageInfo);
    
    // Wait for user input before closing
    console.log('\nBrowser left open for manual inspection. Press any key to close...');
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve());
    });
    
  } catch (error) {
    console.error('Debug failed:', error);
  } finally {
    await browser.close();
  }
}

debugVueRouter().catch(console.error);