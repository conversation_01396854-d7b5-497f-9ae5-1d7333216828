import puppeteer from 'puppeteer';

async function testAllDynamicLocaleRoutes() {
  console.log('=== Testing All Dynamic Locale Routes ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const locales = ['cs', 'sk', 'en'];
  const routes = [
    'dashboard',
    'mainbox',
    'bookings',
    'bookings/1', // Test parameterized routes
    'booking_links',
    'booking_links/1',
    'meetings',
    'meetings/1', 
    'daily_logs',
    'daily_logs/report',
    'works',
    'works/1',
    'events',
    'contracts',
    'contracts/1',
    'companies',
    'companies/1/edit',
    'company_connections',
    'company_settings/edit',
    'reports/activities',
    'user_profiles/1/edit',
    'user_settings/edit',
    'holidays',
    'assignments'
  ];
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = [];
  
  try {
    const page = await browser.newPage();
    
    // Suppress console logs for cleaner output
    page.on('console', () => {});
    page.on('error', () => {});
    page.on('pageerror', () => {});
    
    console.log('Step 1: Logging in with test user...');
    
    // Navigate to login page and login
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    console.log('✅ Successfully logged in\n');
    
    console.log(`Step 2: Testing ${locales.length} locales × ${routes.length} routes = ${locales.length * routes.length} combinations\n`);
    
    for (const locale of locales) {
      console.log(`Testing locale: ${locale.toUpperCase()}`);
      
      for (const route of routes) {
        totalTests++;
        const testPath = `/${locale}/${route}`;
        
        try {
          const response = await page.goto(`http://0.0.0.0:5100${testPath}`, {
            waitUntil: 'networkidle2',
            timeout: 10000
          });
          
          const status = response.status();
          
          // Wait for Vue to load
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Check if Vue Router properly parsed locale
          const routerInfo = await page.evaluate(() => {
            if (window.router && window.router.currentRoute) {
              return {
                available: true,
                currentPath: window.router.currentRoute.value.path,
                params: window.router.currentRoute.value.params,
                matched: window.router.currentRoute.value.matched.length > 0
              };
            }
            return { available: false };
          });
          
          const passed = status === 200 && 
                        routerInfo.available && 
                        routerInfo.params?.locale === locale &&
                        routerInfo.matched;
          
          if (passed) {
            console.log(`  ✓ ${testPath}`);
            passedTests++;
          } else {
            console.log(`  ✗ ${testPath} - Status: ${status}, Locale: ${routerInfo.params?.locale}, Matched: ${routerInfo.matched}`);
            failedTests.push({
              path: testPath,
              status: status,
              expected: locale,
              actual: routerInfo.params?.locale,
              matched: routerInfo.matched
            });
          }
        } catch (err) {
          console.log(`  ✗ ${testPath} - Error: ${err.message}`);
          failedTests.push({
            path: testPath,
            error: err.message
          });
        }
      }
      console.log('');
    }
    
    console.log('=== Test Summary ===');
    console.log(`Total tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests.length}`);
    console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (failedTests.length > 0) {
      console.log('\nFailed tests details:');
      failedTests.forEach(test => {
        if (test.error) {
          console.log(`  - ${test.path}: ${test.error}`);
        } else {
          console.log(`  - ${test.path}: Status ${test.status}, Expected locale '${test.expected}', Got '${test.actual}', Route matched: ${test.matched}`);
        }
      });
    } else {
      console.log('\n🎉 All dynamic locale routes working perfectly!');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testAllDynamicLocaleRoutes().catch(console.error);