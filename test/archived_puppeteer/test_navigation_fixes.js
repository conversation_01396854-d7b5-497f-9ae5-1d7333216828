import puppeteer from 'puppeteer';

async function testNavigationFixes() {
  console.log('=== Testing Navigation Fixes ===\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('Step 1: Login and navigate to SK dashboard...');
    
    await page.goto('http://0.0.0.0:5100/cs/users/sign_in', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await page.type('#user_email', '<EMAIL>');
    await page.type('#user_password', '123456');
    
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle2' }),
      page.click('input[type="submit"]')
    ]);
    
    // Navigate to SK locale dashboard
    await page.goto('http://0.0.0.0:5100/sk/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    console.log(`✅ Starting at: ${page.url()}`);
    
    // Wait for Vue app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\nStep 2: Test Titulka (dashboard) link - should NOT cause full page reload...');
    
    // Check if Titulka link is now a router-link
    const titulkaLinkInfo = await page.evaluate(() => {
      // Look for the Titulka link
      const titulkaLink = Array.from(document.querySelectorAll('*')).find(el => 
        el.textContent && el.textContent.trim() === 'Titulka'
      );
      
      if (titulkaLink) {
        const linkElement = titulkaLink.closest('a, router-link');
        return {
          found: true,
          tagName: linkElement ? linkElement.tagName : 'none',
          href: linkElement ? linkElement.href : null,
          to: linkElement ? linkElement.getAttribute('to') : null,
          isRouterLink: linkElement ? linkElement.tagName === 'ROUTER-LINK' : false
        };
      }
      
      return { found: false };
    });
    
    console.log('Titulka link analysis:');
    console.log(`  Found: ${titulkaLinkInfo.found}`);
    if (titulkaLinkInfo.found) {
      console.log(`  Tag: ${titulkaLinkInfo.tagName}`);
      console.log(`  Is router-link: ${titulkaLinkInfo.isRouterLink}`);
      console.log(`  Href: ${titulkaLinkInfo.href}`);
      console.log(`  To: ${titulkaLinkInfo.to}`);
      
      if (titulkaLinkInfo.isRouterLink) {
        console.log('  ✅ Titulka now renders as router-link (should prevent page reload)');
      } else if (titulkaLinkInfo.href && titulkaLinkInfo.href.includes('/sk/dashboard')) {
        console.log('  ✅ Titulka has correct SK locale URL');
      } else {
        console.log('  ❌ Titulka link issue detected');
      }
    }
    
    console.log('\nStep 3: Test Topbar company link...');
    
    const topbarLinkInfo = await page.evaluate(() => {
      // Look for company name link in topbar
      const companyLinks = Array.from(document.querySelectorAll('a, router-link')).filter(el => 
        el.href && el.href.includes('/companies') || 
        el.getAttribute('to') && el.getAttribute('to').includes('companies')
      );
      
      if (companyLinks.length > 0) {
        const link = companyLinks[0];
        return {
          found: true,
          tagName: link.tagName,
          href: link.href,
          to: link.getAttribute('to'),
          isRouterLink: link.tagName === 'ROUTER-LINK',
          text: link.textContent.trim()
        };
      }
      
      return { found: false };
    });
    
    console.log('Topbar company link analysis:');
    console.log(`  Found: ${topbarLinkInfo.found}`);
    if (topbarLinkInfo.found) {
      console.log(`  Tag: ${topbarLinkInfo.tagName}`);
      console.log(`  Is router-link: ${topbarLinkInfo.isRouterLink}`);
      console.log(`  Href: ${topbarLinkInfo.href}`);
      console.log(`  To: ${topbarLinkInfo.to}`);
      console.log(`  Text: ${topbarLinkInfo.text}`);
      
      if (topbarLinkInfo.isRouterLink || (topbarLinkInfo.href && topbarLinkInfo.href.includes('/sk/companies'))) {
        console.log('  ✅ Topbar company link has correct locale support');
      } else {
        console.log('  ❌ Topbar company link issue detected');
      }
    }
    
    console.log('\nStep 4: Test locale consistency...');
    
    // Navigate to EN locale and check links
    await page.goto('http://0.0.0.0:5100/en/dashboard', {
      waitUntil: 'networkidle2',
      timeout: 10000
    });
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const enLinksInfo = await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a, router-link'));
      const enLinks = links.filter(link => {
        const href = link.href || '';
        const to = link.getAttribute('to') || '';
        return href.includes('/en/') || to.includes('/en/');
      });
      
      return {
        totalLinks: links.length,
        enLocalizedLinks: enLinks.length,
        sampleLinks: enLinks.slice(0, 3).map(link => ({
          href: link.href,
          to: link.getAttribute('to'),
          text: link.textContent.trim().substring(0, 20)
        }))
      };
    });
    
    console.log('EN locale link analysis:');
    console.log(`  Total links: ${enLinksInfo.totalLinks}`);
    console.log(`  EN localized links: ${enLinksInfo.enLocalizedLinks}`);
    console.log('  Sample EN links:');
    enLinksInfo.sampleLinks.forEach((link, i) => {
      console.log(`    ${i + 1}. ${link.text}: ${link.href || link.to}`);
    });
    
    if (enLinksInfo.enLocalizedLinks > 0) {
      console.log('  ✅ EN locale links are being generated correctly');
    } else {
      console.log('  ❌ No EN locale links found');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testNavigationFixes().catch(console.error);