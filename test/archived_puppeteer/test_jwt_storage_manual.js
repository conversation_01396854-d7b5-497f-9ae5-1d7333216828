/**
 * Manual test script for JwtStorageService
 * Open browser dev tools console and run this script to verify JWT storage functionality
 * 
 * Usage:
 * 1. Start the dev server: foreman start -f Procfile.dev
 * 2. Open browser to http://localhost:5100
 * 3. Open dev tools console
 * 4. Copy and paste this script
 */

// Import the service (you'll need to adapt this based on your module loading)
// import JwtStorageService from './app/frontend/services/jwtStorage.js';

console.log('=== JWT Storage Service Manual Test ===');

// Test token
const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJjb21wYW55X2lkIjoxLCJleHAiOjE2MzkxNDU2MDB9.test-signature';

// Test 1: Initial state
console.log('Test 1: Initial state');
console.log('hasToken():', JwtStorageService.hasToken());
console.log('getToken():', JwtStorageService.getToken());

// Test 2: Set token
console.log('\nTest 2: Set token');
JwtStorageService.setToken(testToken);
console.log('hasToken():', JwtStorageService.hasToken());
console.log('getToken():', JwtStorageService.getToken());

// Test 3: Update token
console.log('\nTest 3: Update token');
const newToken = 'new-token-value';
JwtStorageService.setToken(newToken);
console.log('getToken():', JwtStorageService.getToken());

// Test 4: Storage key
console.log('\nTest 4: Storage key');
console.log('getStorageKey():', JwtStorageService.getStorageKey());

// Test 5: Manual localStorage inspection
console.log('\nTest 5: Manual localStorage inspection');
console.log('localStorage value:', localStorage.getItem(JwtStorageService.getStorageKey()));

// Test 6: Remove token
console.log('\nTest 6: Remove token');
const removeResult = JwtStorageService.removeToken();
console.log('removeToken() result:', removeResult);
console.log('hasToken():', JwtStorageService.hasToken());
console.log('getToken():', JwtStorageService.getToken());

// Test 7: Error handling (try invalid inputs)
console.log('\nTest 7: Error handling');
try {
  JwtStorageService.setToken(null);
} catch (error) {
  console.log('Expected error for null token:', error.message);
}

try {
  JwtStorageService.setToken(123);
} catch (error) {
  console.log('Expected error for number token:', error.message);
}

console.log('\n=== Manual Test Complete ===');
console.log('All basic operations should work correctly.');
console.log('Check localStorage in Application tab to verify token storage.');