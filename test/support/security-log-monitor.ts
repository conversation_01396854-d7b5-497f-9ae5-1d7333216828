// ABOUTME: Security-focused log monitoring for Playwright tests
// ABOUTME: Automatically detects and reports security-relevant log entries

import { Page } from '@playwright/test';

export interface SecurityLogEntry {
  message: string;
  timestamp: Date;
  severity: 'info' | 'warning' | 'critical';
  category: 'auth' | 'access' | 'data' | 'system';
}

export class SecurityLogMonitor {
  private logs: SecurityLogEntry[] = [];
  private criticalPatterns = [
    // Authentication & Authorization
    { pattern: /Company switch:.*User (\d+) switched to company (\d+)/, category: 'access' as const, severity: 'info' as const },
    { pattern: /JWT tenant context set:.*company_id=(\d+).*user=(\d+).*role=(\w+)/, category: 'auth' as const, severity: 'info' as const },
    { pattern: /Unauthorized access attempt/, category: 'access' as const, severity: 'critical' as const },
    { pattern: /Permission denied/, category: 'access' as const, severity: 'warning' as const },
    
    // Data Access
    { pattern: /Loading employees for company/, category: 'data' as const, severity: 'info' as const },
    { pattern: /Fetching sensitive data/, category: 'data' as const, severity: 'warning' as const },
    { pattern: /Data leak detected/, category: 'data' as const, severity: 'critical' as const },
    
    // Security Events
    { pattern: /Security violation/, category: 'system' as const, severity: 'critical' as const },
    { pattern: /Invalid token/, category: 'auth' as const, severity: 'warning' as const },
    { pattern: /Session hijack/, category: 'auth' as const, severity: 'critical' as const }
  ];

  constructor(private page: Page) {
    this.attachListeners();
  }

  private attachListeners() {
    // Monitor console logs
    this.page.on('console', msg => {
      const text = msg.text();
      this.analyzeLog(text);
    });

    // Monitor network requests for API security headers
    this.page.on('response', response => {
      if (response.url().includes('/api/')) {
        const status = response.status();
        if (status === 403 || status === 401) {
          this.logs.push({
            message: `API Security: ${status} on ${response.url()}`,
            timestamp: new Date(),
            severity: 'warning',
            category: 'access'
          });
        }
      }
    });
  }

  private analyzeLog(message: string) {
    for (const { pattern, category, severity } of this.criticalPatterns) {
      const match = message.match(pattern);
      if (match) {
        this.logs.push({
          message,
          timestamp: new Date(),
          severity,
          category
        });
        break;
      }
    }
  }

  // Get all security-relevant logs
  getSecurityLogs(): SecurityLogEntry[] {
    return [...this.logs];
  }

  // Check if specific security event occurred
  hasSecurityEvent(pattern: RegExp): boolean {
    return this.logs.some(log => pattern.test(log.message));
  }

  // Get logs by category
  getLogsByCategory(category: SecurityLogEntry['category']): SecurityLogEntry[] {
    return this.logs.filter(log => log.category === category);
  }

  // Assert no critical security issues
  assertNoSecurityViolations() {
    const criticalLogs = this.logs.filter(log => log.severity === 'critical');
    if (criticalLogs.length > 0) {
      throw new Error(`Critical security violations detected:\n${
        criticalLogs.map(log => `- ${log.message}`).join('\n')
      }`);
    }
  }

  // Verify expected security behavior
  assertCompanySwitchAuthorized(userId: number, companyId: number) {
    const switchLog = this.logs.find(log => 
      log.message.includes(`User ${userId} switched to company ${companyId}`)
    );
    
    if (!switchLog) {
      throw new Error(`No company switch log found for user ${userId} to company ${companyId}`);
    }

    // Check if JWT context was set (indicates successful authorization)
    const jwtLog = this.logs.find(log =>
      log.message.includes(`JWT tenant context set`) &&
      log.message.includes(`company_id=${companyId}`) &&
      log.message.includes(`user=${userId}`)
    );

    if (!jwtLog) {
      throw new Error(`Company switch succeeded but JWT context not set - SECURITY VULNERABILITY`);
    }
  }

  // Generate security report
  generateSecurityReport(): string {
    const report = [
      '=== SECURITY LOG REPORT ===',
      `Total logs captured: ${this.logs.length}`,
      '',
      'By Category:',
      `- Auth: ${this.getLogsByCategory('auth').length}`,
      `- Access: ${this.getLogsByCategory('access').length}`,
      `- Data: ${this.getLogsByCategory('data').length}`,
      `- System: ${this.getLogsByCategory('system').length}`,
      '',
      'Critical Issues:',
      ...this.logs
        .filter(log => log.severity === 'critical')
        .map(log => `- ${log.message}`),
      '',
      'Access Logs:',
      ...this.getLogsByCategory('access')
        .map(log => `- [${log.severity}] ${log.message}`)
    ];

    return report.join('\n');
  }
}

// Helper function to use in tests
export function attachSecurityMonitor(page: Page): SecurityLogMonitor {
  return new SecurityLogMonitor(page);
}