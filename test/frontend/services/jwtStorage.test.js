import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import JwtStorageService from '../../../app/frontend/services/jwtStorage.js';

// Mock localStorage for testing environment
const localStorageMock = {
  store: {},
  getItem: vi.fn((key) => localStorageMock.store[key] || null),
  setItem: vi.fn((key, value) => { localStorageMock.store[key] = value; }),
  removeItem: vi.fn((key) => { delete localStorageMock.store[key]; }),
  clear: vi.fn(() => { localStorageMock.store = {}; })
};

// Mock the global localStorage
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

describe('JwtStorageService', () => {
  const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJjb21wYW55X2lkIjoxLCJleHAiOjE2MzkxNDU2MDB9.test-signature';
  const storageKey = JwtStorageService.getStorageKey();

  beforeEach(() => {
    // Clear localStorage before each test
    localStorageMock.store = {};
    vi.clearAllMocks();
    
    // Reset localStorage mock implementations to defaults
    localStorageMock.getItem.mockImplementation((key) => localStorageMock.store[key] || null);
    localStorageMock.setItem.mockImplementation((key, value) => { localStorageMock.store[key] = value; });
    localStorageMock.removeItem.mockImplementation((key) => { delete localStorageMock.store[key]; });
    localStorageMock.clear.mockImplementation(() => { localStorageMock.store = {}; });
  });

  afterEach(() => {
    // Clean up localStorage after each test
    localStorageMock.store = {};
    vi.restoreAllMocks();
  });

  describe('setToken', () => {
    it('should store a valid JWT token', () => {
      JwtStorageService.setToken(testToken);
      expect(localStorageMock.store[storageKey]).toBe(testToken);
    });

    it('should throw error for non-string token', () => {
      expect(() => JwtStorageService.setToken(null)).toThrow('Token must be a string');
      expect(() => JwtStorageService.setToken(undefined)).toThrow('Token must be a string');
      expect(() => JwtStorageService.setToken(123)).toThrow('Token must be a string');
      expect(() => JwtStorageService.setToken({})).toThrow('Token must be a string');
    });

    it('should overwrite existing token', () => {
      const firstToken = 'first-token';
      const secondToken = 'second-token';
      
      JwtStorageService.setToken(firstToken);
      expect(localStorageMock.store[storageKey]).toBe(firstToken);
      
      JwtStorageService.setToken(secondToken);
      expect(localStorageMock.store[storageKey]).toBe(secondToken);
    });

    it('should handle localStorage quota exceeded error', () => {
      // Mock localStorage.setItem to throw quota exceeded error while keeping isLocalStorageAvailable true
      localStorageMock.setItem.mockImplementation((key, value) => {
        if (key === '__test_localStorage__') {
          // Allow the availability test to pass
          localStorageMock.store[key] = value;
          return;
        }
        const error = new Error('QuotaExceededError');
        error.name = 'QuotaExceededError';
        throw error;
      });

      expect(() => JwtStorageService.setToken(testToken)).toThrow('Failed to store JWT token in localStorage');
    });
  });

  describe('getToken', () => {
    it('should return stored token', () => {
      localStorageMock.store[storageKey] = testToken;
      expect(JwtStorageService.getToken()).toBe(testToken);
    });

    it('should return null when no token stored', () => {
      expect(JwtStorageService.getToken()).toBeNull();
    });

    it('should handle localStorage access error', () => {
      // Mock localStorage.getItem to throw error
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage access error');
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      expect(JwtStorageService.getToken()).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Failed to retrieve JWT token:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('removeToken', () => {
    it('should remove existing token', () => {
      localStorageMock.store[storageKey] = testToken;
      expect(localStorageMock.store[storageKey]).toBe(testToken);
      
      const result = JwtStorageService.removeToken();
      expect(result).toBe(true);
      expect(localStorageMock.store[storageKey]).toBeUndefined();
    });

    it('should return true even when no token exists', () => {
      const result = JwtStorageService.removeToken();
      expect(result).toBe(true);
    });

    it('should handle localStorage remove error', () => {
      // Mock localStorage.removeItem to throw error while keeping availability check working
      localStorageMock.removeItem.mockImplementation((key) => {
        if (key === '__test_localStorage__') {
          // Allow the availability test to pass
          delete localStorageMock.store[key];
          return;
        }
        throw new Error('localStorage remove error');
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const result = JwtStorageService.removeToken();
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Failed to remove JWT token:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('hasToken', () => {
    it('should return true when token exists', () => {
      localStorageMock.store[storageKey] = testToken;
      expect(JwtStorageService.hasToken()).toBe(true);
    });

    it('should return false when no token exists', () => {
      expect(JwtStorageService.hasToken()).toBe(false);
    });

    it('should return false when token is empty string', () => {
      localStorageMock.store[storageKey] = '';
      expect(JwtStorageService.hasToken()).toBe(false);
    });

    it('should return false when localStorage access fails', () => {
      // Mock getToken to return null (simulating localStorage error)
      const mockGetToken = vi.spyOn(JwtStorageService, 'getToken').mockReturnValue(null);
      
      expect(JwtStorageService.hasToken()).toBe(false);
      
      mockGetToken.mockRestore();
    });
  });

  describe('localStorage availability', () => {
    it('should handle missing localStorage gracefully', () => {
      // Mock isLocalStorageAvailable to return false
      const mockIsAvailable = vi.spyOn(JwtStorageService, 'isLocalStorageAvailable').mockReturnValue(false);
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      expect(() => JwtStorageService.setToken(testToken)).toThrow('localStorage is not available');
      expect(JwtStorageService.getToken()).toBeNull();
      expect(JwtStorageService.removeToken()).toBe(false);
      expect(JwtStorageService.hasToken()).toBe(false);

      expect(consoleSpy).toHaveBeenCalledWith('localStorage is not available, returning null for JWT token');
      
      consoleSpy.mockRestore();
    });
  });

  describe('utility methods', () => {
    describe('getStorageKey', () => {
      it('should return the correct storage key', () => {
        expect(JwtStorageService.getStorageKey()).toBe('TYMBOX_JWT_TOKEN');
      });
    });

    describe('clearAll', () => {
      it('should clear all localStorage data', () => {
        localStorageMock.store['test-key'] = 'test-value';
        localStorageMock.store[storageKey] = testToken;
        
        const result = JwtStorageService.clearAll();
        expect(result).toBe(true);
        expect(localStorageMock.store['test-key']).toBeUndefined();
        expect(localStorageMock.store[storageKey]).toBeUndefined();
      });

      it('should handle localStorage clear error', () => {
        localStorageMock.clear.mockImplementation(() => {
          throw new Error('localStorage clear error');
        });
        
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        const result = JwtStorageService.clearAll();
        expect(result).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Failed to clear localStorage:', expect.any(Error));
        
        consoleSpy.mockRestore();
      });

      it('should return false when localStorage not available', () => {
        const mockIsAvailable = vi.spyOn(JwtStorageService, 'isLocalStorageAvailable').mockReturnValue(false);
        
        const result = JwtStorageService.clearAll();
        expect(result).toBe(false);
      });
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete token lifecycle', () => {
      // Initially no token
      expect(JwtStorageService.hasToken()).toBe(false);
      expect(JwtStorageService.getToken()).toBeNull();

      // Store token
      JwtStorageService.setToken(testToken);
      expect(JwtStorageService.hasToken()).toBe(true);
      expect(JwtStorageService.getToken()).toBe(testToken);

      // Update token
      const newToken = 'new-jwt-token';
      JwtStorageService.setToken(newToken);
      expect(JwtStorageService.getToken()).toBe(newToken);

      // Remove token
      const removeResult = JwtStorageService.removeToken();
      expect(removeResult).toBe(true);
      expect(JwtStorageService.hasToken()).toBe(false);
      expect(JwtStorageService.getToken()).toBeNull();
    });

    it('should handle multiple rapid operations', () => {
      const tokens = ['token1', 'token2', 'token3'];
      
      // Rapid set/get operations
      tokens.forEach(token => {
        JwtStorageService.setToken(token);
        expect(JwtStorageService.getToken()).toBe(token);
        expect(JwtStorageService.hasToken()).toBe(true);
      });

      // Final removal
      JwtStorageService.removeToken();
      expect(JwtStorageService.hasToken()).toBe(false);
    });
  });

  describe('security considerations', () => {
    it('should not expose token in error messages', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Mock localStorage to throw error while keeping availability check working
      localStorageMock.setItem.mockImplementation((key, value) => {
        if (key === '__test_localStorage__') {
          // Allow the availability test to pass
          localStorageMock.store[key] = value;
          return;
        }
        throw new Error('Test error');
      });

      try {
        JwtStorageService.setToken('sensitive-token-data');
      } catch (error) {
        // Error message should not contain the token
        expect(error.message).not.toContain('sensitive-token-data');
        expect(error.message).toBe('Failed to store JWT token in localStorage');
      }

      consoleSpy.mockRestore();
    });

    it('should handle XSS-style token injection attempts', () => {
      const maliciousToken = '<script>alert("xss")</script>';
      
      // Service should store it as-is (string) - protection is app-wide, not service-level
      JwtStorageService.setToken(maliciousToken);
      expect(JwtStorageService.getToken()).toBe(maliciousToken);
      expect(localStorageMock.store[storageKey]).toBe(maliciousToken);
      
      // Clean up
      JwtStorageService.removeToken();
    });
  });
});