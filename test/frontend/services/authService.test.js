/**
 * AuthService Test Suite
 * 
 * Comprehensive tests for the AuthService class covering:
 * - JWT authentication (success/failure scenarios)
 * - Session authentication fallback
 * - Dual logout functionality
 * - Token refresh mechanisms
 * - Error handling and edge cases
 * - Feature flag integration
 * - Vuex store integration
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import AuthService from '../../../app/frontend/services/authService';
import JwtStorageService from '../../../app/frontend/services/jwtStorage';
import axios from '../../../app/frontend/utils/axiosSetup';
import store from '../../../app/frontend/store';
import { sendFlashMessage } from '../../../app/frontend/utils/flashMessage';

// Mock dependencies
vi.mock('../../../app/frontend/utils/axiosSetup');
vi.mock('../../../app/frontend/services/jwtStorage');
vi.mock('../../../app/frontend/store');
vi.mock('../../../app/frontend/utils/flashMessage');

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
  writable: true
});

// Mock feature flags
Object.defineProperty(window, 'FEATURE_FLAGS', {
  value: {
    jwt_login_enabled: true
  },
  writable: true
});

describe('AuthService', () => {
  // Mock store getters
  const mockStoreGetters = {
    'userStore/hasJwtToken': false,
    'userStore/authMethod': null,
    'userStore/jwtToken': null,
    'userStore/effectiveUser': null,
    'userStore/isAuthenticated': false
  };

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup default store mock
    store.getters = { ...mockStoreGetters };
    store.commit = vi.fn();
    store.dispatch = vi.fn();
    
    // Reset sessionStorage mock
    mockSessionStorage.getItem.mockReturnValue(null);
    mockSessionStorage.setItem.mockImplementation(() => {});
    mockSessionStorage.removeItem.mockImplementation(() => {});
    
    // Reset feature flags
    window.FEATURE_FLAGS = { jwt_login_enabled: true };
  });

  describe('login', () => {
    const validCredentials = {
      email: '<EMAIL>',
      password: 'password123'
    };

    it('should throw error for missing credentials', async () => {
      await expect(AuthService.login()).rejects.toThrow('Email and password are required');
      await expect(AuthService.login({})).rejects.toThrow('Email and password are required');
      await expect(AuthService.login({ email: '<EMAIL>' })).rejects.toThrow('Email and password are required');
    });

    describe('JWT Authentication (Primary Path)', () => {
      it('should successfully login with JWT when enabled', async () => {
        // Mock successful JWT login response
        axios.post.mockResolvedValueOnce({
          data: {
            success: true,
            access_token: 'jwt_access_token',
            refresh_token: 'jwt_refresh_token',
            user: {
              id: 1,
              email: '<EMAIL>',
              company_id: 1
            }
          }
        });

        // Mock successful user data fetch
        axios.get.mockResolvedValueOnce({
          data: {
            roles: ['admin'],
            permissions: { can_manage_contracts: true },
            company: { id: 1, name: 'Test Company' },
            plan_info: { hasPlusPlan: true }
          }
        });

        const result = await AuthService.login(validCredentials);

        // Verify JWT login was attempted
        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/jwt_login', {
          email: '<EMAIL>',
          password: 'password123'
        }, {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        // Verify tokens were stored
        expect(JwtStorageService.setToken).toHaveBeenCalledWith('jwt_access_token');
        expect(mockSessionStorage.setItem).toHaveBeenCalledWith('TYMBOX_REFRESH_TOKEN', 'jwt_refresh_token');

        // Verify Vuex store was updated
        expect(store.commit).toHaveBeenCalledWith('userStore/SET_JWT_TOKEN', 'jwt_access_token');
        expect(store.commit).toHaveBeenCalledWith('userStore/SET_JWT_USER', {
          id: 1,
          email: '<EMAIL>',
          company_id: 1
        });

        // Verify additional user data was fetched and stored
        expect(store.commit).toHaveBeenCalledWith('userStore/setUserRoles', ['admin']);
        expect(store.commit).toHaveBeenCalledWith('userStore/setPermissions', { can_manage_contracts: true });

        // Verify return value
        expect(result).toEqual({
          success: true,
          authMethod: 'jwt',
          user: {
            id: 1,
            email: '<EMAIL>',
            company_id: 1
          },
          message: 'Logged in successfully via JWT'
        });
      });

      it('should handle JWT login failure and fallback to session', async () => {
        // Mock JWT login failure
        axios.post.mockRejectedValueOnce(new Error('JWT endpoint error'));
        
        // Mock successful session login
        axios.post.mockResolvedValueOnce({
          data: {
            success: true,
            user: {
              email: '<EMAIL>',
              role: 'admin'
            }
          }
        });

        // Mock successful user data fetch for session
        store.dispatch.mockResolvedValueOnce();

        const result = await AuthService.login(validCredentials);

        // Verify JWT was attempted first
        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/jwt_login', validCredentials, expect.any(Object));
        
        // Verify session fallback was used
        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/login', validCredentials, expect.any(Object));
        expect(store.dispatch).toHaveBeenCalledWith('userStore/fetchUserData');

        expect(result).toEqual({
          success: true,
          authMethod: 'session',
          user: {
            email: '<EMAIL>',
            role: 'admin'
          },
          message: 'Logged in successfully via session'
        });
      });

      it('should skip JWT when feature flag is disabled', async () => {
        window.FEATURE_FLAGS.jwt_login_enabled = false;

        // Mock successful session login
        axios.post.mockResolvedValueOnce({
          data: {
            success: true,
            user: { email: '<EMAIL>' }
          }
        });
        store.dispatch.mockResolvedValueOnce();

        const result = await AuthService.login(validCredentials);

        // Verify JWT was skipped
        expect(axios.post).toHaveBeenCalledTimes(1);
        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/login', validCredentials, expect.any(Object));
        
        expect(result.authMethod).toBe('session');
      });

      it('should handle missing refresh token gracefully', async () => {
        // Mock JWT login without refresh token
        axios.post.mockResolvedValueOnce({
          data: {
            success: true,
            access_token: 'jwt_access_token',
            // No refresh_token provided
            user: { id: 1, email: '<EMAIL>' }
          }
        });

        axios.get.mockResolvedValueOnce({ data: {} });

        const result = await AuthService.login(validCredentials);

        expect(result.success).toBe(true);
        expect(JwtStorageService.setToken).toHaveBeenCalledWith('jwt_access_token');
        expect(mockSessionStorage.setItem).not.toHaveBeenCalledWith('TYMBOX_REFRESH_TOKEN', expect.anything());
      });
    });

    describe('Session Authentication (Fallback Path)', () => {
      it('should handle both JWT and session failures', async () => {
        // Mock JWT login failure
        axios.post.mockRejectedValueOnce(new Error('JWT failed'));
        
        // Mock session login failure
        axios.post.mockRejectedValueOnce(new Error('Session failed'));

        await expect(AuthService.login(validCredentials)).rejects.toThrow('Login failed with both JWT and session methods');

        expect(axios.post).toHaveBeenCalledTimes(2);
      });

      it('should handle successful session login when JWT endpoint returns error', async () => {
        // Mock JWT login returning error response
        axios.post.mockResolvedValueOnce({
          data: {
            success: false,
            error: 'Invalid credentials'
          }
        });
        
        // Mock successful session login
        axios.post.mockResolvedValueOnce({
          data: {
            success: true,
            user: { email: '<EMAIL>' }
          }
        });
        store.dispatch.mockResolvedValueOnce();

        const result = await AuthService.login(validCredentials);

        expect(result.authMethod).toBe('session');
        expect(result.success).toBe(true);
      });
    });
  });

  describe('logout', () => {
    it('should handle JWT logout successfully', async () => {
      // Setup JWT authentication state
      store.getters['userStore/hasJwtToken'] = true;
      store.getters['userStore/jwtToken'] = 'current_jwt_token';
      store.getters['userStore/authMethod'] = 'jwt';

      // Mock successful JWT logout
      axios.post.mockResolvedValueOnce({ data: { success: true } });

      const result = await AuthService.logout();

      expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/jwt_logout', {}, {
        headers: {
          'Authorization': 'Bearer current_jwt_token',
          'Accept': 'application/json'
        }
      });

      expect(store.commit).toHaveBeenCalledWith('userStore/CLEAR_JWT_TOKEN');
      expect(store.commit).toHaveBeenCalledWith('userStore/clearAuth');
      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('TYMBOX_REFRESH_TOKEN');

      expect(result).toEqual({
        success: true,
        jwtLogoutSuccess: true,
        sessionLogoutSuccess: true,
        message: 'Logged out successfully'
      });
    });

    it('should handle session logout successfully', async () => {
      // Setup session authentication state
      store.getters['userStore/hasJwtToken'] = false;
      store.getters['userStore/authMethod'] = 'session';

      // Mock successful session logout
      axios.post.mockResolvedValueOnce({ data: { success: true } });

      const result = await AuthService.logout();

      expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/logout', {}, {
        headers: {
          'Accept': 'application/json'
        }
      });

      expect(result.success).toBe(true);
    });

    it('should handle dual authentication logout', async () => {
      // Setup dual authentication state
      store.getters['userStore/hasJwtToken'] = true;
      store.getters['userStore/jwtToken'] = 'jwt_token';
      store.getters['userStore/authMethod'] = 'session';

      // Mock both logout endpoints
      axios.post.mockResolvedValueOnce({ data: { success: true } }); // JWT logout
      axios.post.mockResolvedValueOnce({ data: { success: true } }); // Session logout

      const result = await AuthService.logout();

      expect(axios.post).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
      expect(result.jwtLogoutSuccess).toBe(true);
      expect(result.sessionLogoutSuccess).toBe(true);
    });

    it('should clear local state even when server logout fails', async () => {
      store.getters['userStore/hasJwtToken'] = true;
      store.getters['userStore/jwtToken'] = 'jwt_token';

      // Mock server error
      axios.post.mockRejectedValueOnce(new Error('Server error'));

      const result = await AuthService.logout();

      // Should still clear local state
      expect(store.commit).toHaveBeenCalledWith('userStore/CLEAR_JWT_TOKEN');
      expect(store.commit).toHaveBeenCalledWith('userStore/clearAuth');
      
      expect(result.success).toBe(false);
      expect(result.jwtLogoutSuccess).toBe(false);
    });
  });

  describe('refreshToken', () => {
    it('should successfully refresh JWT token', async () => {
      mockSessionStorage.getItem.mockReturnValue('refresh_token_123');

      axios.post.mockResolvedValueOnce({
        data: {
          success: true,
          access_token: 'new_access_token',
          refresh_token: 'new_refresh_token'
        }
      });

      const result = await AuthService.refreshToken();

      expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/refresh_token', {
        refresh_token: 'refresh_token_123'
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      expect(JwtStorageService.setToken).toHaveBeenCalledWith('new_access_token');
      expect(store.commit).toHaveBeenCalledWith('userStore/SET_JWT_TOKEN', 'new_access_token');
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith('TYMBOX_REFRESH_TOKEN', 'new_refresh_token');

      expect(result).toBe(true);
    });

    it('should throw error when no refresh token is available', async () => {
      mockSessionStorage.getItem.mockReturnValue(null);

      await expect(AuthService.refreshToken()).rejects.toThrow('No refresh token available');
    });

    it('should logout user when refresh fails', async () => {
      mockSessionStorage.getItem.mockReturnValue('invalid_refresh_token');
      axios.post.mockRejectedValueOnce(new Error('Refresh failed'));

      // Mock the logout method to avoid infinite recursion in test
      const logoutSpy = vi.spyOn(AuthService, 'logout').mockResolvedValueOnce({
        success: true,
        jwtLogoutSuccess: true,
        sessionLogoutSuccess: true
      });

      await expect(AuthService.refreshToken()).rejects.toThrow('Refresh failed');
      expect(logoutSpy).toHaveBeenCalled();
    });
  });

  describe('register', () => {
    const registrationData = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'John',
      last_name: 'Doe'
    };

    it('should successfully register a new user', async () => {
      axios.post.mockResolvedValueOnce({
        status: 201,
        data: {
          success: true,
          user: {
            id: 2,
            email: '<EMAIL>'
          }
        }
      });

      const result = await AuthService.register(registrationData);

      expect(axios.post).toHaveBeenCalledWith('/users', {
        user: registrationData
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      expect(result).toEqual({
        success: true,
        message: 'Registration successful',
        user: {
          id: 2,
          email: '<EMAIL>'
        }
      });
    });

    it('should handle registration failure', async () => {
      const mockError = new Error('Email already exists');
      mockError.response = {
        data: {
          error: 'Email already exists'
        }
      };
      
      axios.post.mockRejectedValueOnce(mockError);

      await expect(AuthService.register(registrationData)).rejects.toThrow('Email already exists');
    });
  });

  describe('Utility Methods', () => {
    it('should return correct JWT enabled status', () => {
      expect(AuthService.isJwtEnabled()).toBe(true);

      window.FEATURE_FLAGS.jwt_login_enabled = false;
      expect(AuthService.isJwtEnabled()).toBe(false);
    });

    it('should return current authentication method', () => {
      store.getters['userStore/authMethod'] = 'jwt';
      expect(AuthService.getCurrentAuthMethod()).toBe('jwt');

      store.getters['userStore/authMethod'] = 'session';
      expect(AuthService.getCurrentAuthMethod()).toBe('session');
    });

    it('should return authentication status', () => {
      store.getters['userStore/isAuthenticated'] = true;
      expect(AuthService.isAuthenticated()).toBe(true);

      store.getters['userStore/isAuthenticated'] = false;
      expect(AuthService.isAuthenticated()).toBe(false);
    });

    it('should return current user', () => {
      const mockUser = { id: 1, email: '<EMAIL>' };
      store.getters['userStore/effectiveUser'] = mockUser;
      
      expect(AuthService.getCurrentUser()).toEqual(mockUser);
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors with flash messages', () => {
      const mockError = new Error('Test error');
      mockError.response = {
        data: {
          error: 'Custom error message'
        }
      };

      const result = AuthService.handleAuthError(mockError, 'login');

      expect(sendFlashMessage).toHaveBeenCalledWith('Custom error message', 'error');
      expect(result).toEqual({
        success: false,
        error: 'Custom error message'
      });
    });

    it('should handle errors without response data', () => {
      const mockError = new Error('Network error');

      const result = AuthService.handleAuthError(mockError);

      expect(sendFlashMessage).toHaveBeenCalledWith('Network error', 'error');
      expect(result).toEqual({
        success: false,
        error: 'Network error'
      });
    });

    it('should handle errors with no specific message', () => {
      const mockError = {};

      const result = AuthService.handleAuthError(mockError);

      expect(sendFlashMessage).toHaveBeenCalledWith('An authentication error occurred', 'error');
      expect(result).toEqual({
        success: false,
        error: 'An authentication error occurred'
      });
    });
  });

  describe('Feature Flag Integration', () => {
    it('should handle missing FEATURE_FLAGS gracefully', () => {
      const originalFlags = window.FEATURE_FLAGS;
      window.FEATURE_FLAGS = undefined;

      // Should default to true when feature flags are not available
      expect(AuthService.isJwtEnabled()).toBe(true);
      
      // Restore original value
      window.FEATURE_FLAGS = originalFlags;
    });

    it('should handle missing jwt_login_enabled flag', () => {
      window.FEATURE_FLAGS = {};

      // Should default to true when specific flag is missing
      expect(AuthService.isJwtEnabled()).toBe(true);
    });
  });

  describe('Storage Error Handling', () => {
    const testCredentials = {
      email: '<EMAIL>',
      password: 'password123'
    };

    it('should handle JwtStorageService errors during login', async () => {
      // Mock JWT login success but storage failure
      axios.post.mockResolvedValueOnce({
        data: {
          success: true,
          access_token: 'jwt_token',
          user: { id: 1, email: '<EMAIL>' }
        }
      });

      JwtStorageService.setToken.mockImplementation(() => {
        throw new Error('Storage error');
      });

      // Should throw due to storage error
      await expect(AuthService.login(testCredentials)).rejects.toThrow();
    });

    it('should handle sessionStorage errors during refresh token storage', async () => {
      axios.post.mockResolvedValueOnce({
        data: {
          success: true,
          access_token: 'jwt_token',
          refresh_token: 'refresh_token',
          user: { id: 1, email: '<EMAIL>' }
        }
      });

      axios.get.mockResolvedValueOnce({ data: {} });

      // Reset the storage service mock to not throw
      JwtStorageService.setToken.mockImplementation(() => {});

      mockSessionStorage.setItem.mockImplementation(() => {
        throw new Error('SessionStorage error');
      });

      // Should not fail login due to refresh token storage error
      const result = await AuthService.login(testCredentials);
      expect(result.success).toBe(true);
    });
  });
});