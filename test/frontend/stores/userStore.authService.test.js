/**
 * Test Suite for userStore AuthService Integration (Chunk 25)
 * 
 * Tests the updated userStore actions that now use AuthService instead of direct API calls.
 * Ensures proper integration between Vuex store and AuthService for login, logout, and registration.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock browser environment first
Object.defineProperty(global, 'document', {
  value: {
    querySelector: vi.fn(() => ({ getAttribute: vi.fn(() => 'csrf-token') })),
    cookie: 'locale=cs'
  }
});

Object.defineProperty(global, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
  }
});

Object.defineProperty(global, 'sessionStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
  }
});

// Mock dependencies before imports
vi.mock('../../../app/frontend/services/authService');
vi.mock('../../../app/frontend/utils/flashMessage');
vi.mock('../../../app/frontend/services/jwtStorage', () => ({
  default: {
    getToken: vi.fn(() => null),
    setToken: vi.fn(),
    removeToken: vi.fn(),
    hasToken: vi.fn(() => false)
  }
}));

// Mock axios with full axios structure
vi.mock('axios', () => ({
  default: {
    defaults: {
      headers: {
        common: {}
      },
      withCredentials: false
    },
    interceptors: {
      request: {
        use: vi.fn()
      },
      response: {
        use: vi.fn()
      }
    },
    get: vi.fn(),
    post: vi.fn()
  }
}));

import AuthService from '../../../app/frontend/services/authService';
import { sendFlashMessage } from '../../../app/frontend/utils/flashMessage';

// Import userStore after mocking dependencies
import userStore from '../../../app/frontend/store/userStore';

describe('userStore AuthService Integration', () => {
  let mockCommit;
  let mockDispatch;
  let mockGetters;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock store context
    mockCommit = vi.fn();
    mockDispatch = vi.fn();
    mockGetters = {};
    
    // Setup AuthService mocks
    AuthService.login = vi.fn();
    AuthService.logout = vi.fn();
    AuthService.register = vi.fn();
  });

  describe('login action', () => {
    it('should handle successful JWT login', async () => {
      // Arrange
      const credentials = { 
        email: '<EMAIL>', 
        password: 'password123',
        remember_me: true 
      };
      
      const authServiceResult = {
        success: true,
        authMethod: 'jwt',
        user: { id: 1, email: '<EMAIL>' }
      };
      
      AuthService.login.mockResolvedValue(authServiceResult);
      mockDispatch.mockResolvedValue();

      // Act
      const result = await userStore.actions.login(
        { commit: mockCommit, dispatch: mockDispatch }, 
        credentials
      );

      // Assert
      expect(AuthService.login).toHaveBeenCalledWith(credentials);
      expect(mockDispatch).toHaveBeenCalledWith('fetchUserData');
      expect(sendFlashMessage).toHaveBeenCalledWith('Přihlášení bylo úspěšné', 'success');
      expect(result).toEqual({
        success: true,
        authMethod: 'jwt',
        user: authServiceResult.user
      });
    });

    it('should handle successful session login', async () => {
      // Arrange
      const credentials = { 
        email: '<EMAIL>', 
        password: 'password123',
        remember_me: false 
      };
      
      const authServiceResult = {
        success: true,
        authMethod: 'session',
        user: { id: 1, email: '<EMAIL>' }
      };
      
      AuthService.login.mockResolvedValue(authServiceResult);
      mockDispatch.mockResolvedValue();

      // Act
      const result = await userStore.actions.login(
        { commit: mockCommit, dispatch: mockDispatch }, 
        credentials
      );

      // Assert
      expect(AuthService.login).toHaveBeenCalledWith(credentials);
      expect(mockDispatch).toHaveBeenCalledWith('fetchUserData');
      expect(sendFlashMessage).toHaveBeenCalledWith('Přihlášení bylo úspěšné', 'success');
      expect(result.authMethod).toBe('session');
    });

    it('should handle login failure with error message', async () => {
      // Arrange
      const credentials = { email: '<EMAIL>', password: 'wrong' };
      const loginError = new Error('Invalid credentials');
      
      AuthService.login.mockRejectedValue(loginError);

      // Act & Assert
      await expect(userStore.actions.login(
        { commit: mockCommit, dispatch: mockDispatch }, 
        credentials
      )).rejects.toThrow('Invalid credentials');
      
      expect(AuthService.login).toHaveBeenCalledWith(credentials);
      expect(sendFlashMessage).toHaveBeenCalledWith('Nesprávný email nebo heslo', 'error');
      expect(mockDispatch).not.toHaveBeenCalled();
    });

    it('should handle fetchUserData failure gracefully', async () => {
      // Arrange
      const credentials = { email: '<EMAIL>', password: 'password123' };
      const authServiceResult = {
        success: true,
        authMethod: 'jwt',
        user: { id: 1, email: '<EMAIL>' }
      };
      
      AuthService.login.mockResolvedValue(authServiceResult);
      // Mock fetchUserData to fail, but login should still complete
      mockDispatch.mockImplementation((action) => {
        if (action === 'fetchUserData') {
          return Promise.reject(new Error('Failed to fetch user data'));
        }
        return Promise.resolve();
      });

      // Act
      const result = await userStore.actions.login(
        { commit: mockCommit, dispatch: mockDispatch }, 
        credentials
      );

      // Assert
      expect(result.success).toBe(true);
      expect(sendFlashMessage).toHaveBeenCalledWith('Přihlášení bylo úspěšné', 'success');
    });
  });

  describe('logout action', () => {
    it('should handle successful logout', async () => {
      // Arrange
      const logoutResult = {
        success: true,
        jwtLogoutSuccess: true,
        sessionLogoutSuccess: true
      };
      
      AuthService.logout.mockResolvedValue(logoutResult);

      // Act
      await userStore.actions.logout({ commit: mockCommit });

      // Assert
      expect(AuthService.logout).toHaveBeenCalled();
      expect(sendFlashMessage).toHaveBeenCalledWith('Odhlášení bylo úspěšné', 'success');
      expect(mockCommit).not.toHaveBeenCalledWith('clearAuth'); // AuthService handles this
    });

    it('should handle partial logout failure', async () => {
      // Arrange
      const logoutResult = {
        success: false,
        jwtLogoutSuccess: true,
        sessionLogoutSuccess: false
      };
      
      AuthService.logout.mockResolvedValue(logoutResult);
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      // Act
      await userStore.actions.logout({ commit: mockCommit });

      // Assert
      expect(AuthService.logout).toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledWith('Logout may have partially failed:', {
        jwtLogoutSuccess: true,
        sessionLogoutSuccess: false
      });
      expect(sendFlashMessage).toHaveBeenCalledWith('Odhlášení bylo úspěšné', 'success');
      
      consoleWarnSpy.mockRestore();
    });

    it('should handle logout error and clear local state', async () => {
      // Arrange
      const logoutError = new Error('Network error');
      AuthService.logout.mockRejectedValue(logoutError);
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      await userStore.actions.logout({ commit: mockCommit });

      // Assert
      expect(AuthService.logout).toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Logout error:', logoutError);
      expect(mockCommit).toHaveBeenCalledWith('clearAuth'); // Fallback cleanup
      expect(sendFlashMessage).toHaveBeenCalledWith('Odhlášení bylo úspěšné', 'success');
      
      consoleErrorSpy.mockRestore();
    });
  });

  describe('register action', () => {
    it('should handle successful registration and auto-login', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123'
      };
      
      const registerResult = {
        success: true,
        message: 'Registration successful',
        user: { id: 1, email: '<EMAIL>' }
      };
      
      AuthService.register.mockResolvedValue(registerResult);
      mockDispatch.mockResolvedValue({ success: true, authMethod: 'jwt' });

      // Act
      const result = await userStore.actions.register(
        { commit: mockCommit, dispatch: mockDispatch }, 
        userData
      );

      // Assert
      expect(AuthService.register).toHaveBeenCalledWith(userData);
      expect(mockDispatch).toHaveBeenCalledWith('login', {
        email: userData.email,
        password: userData.password
      });
      expect(result).toEqual(registerResult);
    });

    it('should handle registration with validation errors', async () => {
      // Arrange
      const userData = {
        email: 'invalid-email',
        password: '123',
        password_confirmation: '456'
      };
      
      const validationError = new Error('Validation failed');
      validationError.response = {
        data: {
          errors: {
            email: ['is invalid'],
            password: ['is too short'],
            password_confirmation: ['does not match']
          }
        }
      };
      
      AuthService.register.mockRejectedValue(validationError);

      // Act & Assert
      await expect(userStore.actions.register(
        { commit: mockCommit, dispatch: mockDispatch }, 
        userData
      )).rejects.toThrow('Validation failed');
      
      expect(AuthService.register).toHaveBeenCalledWith(userData);
      expect(sendFlashMessage).toHaveBeenCalledWith(
        'email: is invalid; password: is too short; password_confirmation: does not match', 
        'error'
      );
      expect(mockDispatch).not.toHaveBeenCalled();
    });

    it('should handle registration with generic error', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123'
      };
      
      const genericError = new Error('Server error');
      AuthService.register.mockRejectedValue(genericError);

      // Act & Assert
      await expect(userStore.actions.register(
        { commit: mockCommit, dispatch: mockDispatch }, 
        userData
      )).rejects.toThrow('Server error');
      
      expect(AuthService.register).toHaveBeenCalledWith(userData);
      expect(sendFlashMessage).toHaveBeenCalledWith('Registrace selhala', 'error');
    });

    it('should handle auto-login failure after successful registration', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123'
      };
      
      const registerResult = {
        success: true,
        message: 'Registration successful',
        user: { id: 1, email: '<EMAIL>' }
      };
      
      AuthService.register.mockResolvedValue(registerResult);
      // Mock login to fail, but registration should still complete
      mockDispatch.mockImplementation((action) => {
        if (action === 'login') {
          return Promise.reject(new Error('Login failed'));
        }
        return Promise.resolve();
      });

      // Act
      const result = await userStore.actions.register(
        { commit: mockCommit, dispatch: mockDispatch }, 
        userData
      );

      // Assert
      expect(AuthService.register).toHaveBeenCalledWith(userData);
      expect(mockDispatch).toHaveBeenCalledWith('login', {
        email: userData.email,
        password: userData.password
      });
      // Should still return successful registration result
      expect(result).toEqual(registerResult);
    });
  });

  describe('edge cases', () => {
    it('should handle missing credentials gracefully', async () => {
      // Arrange
      const invalidCredentials = { email: '' };
      const credentialError = new Error('Email and password are required');
      AuthService.login.mockRejectedValue(credentialError);

      // Act & Assert
      await expect(userStore.actions.login(
        { commit: mockCommit, dispatch: mockDispatch }, 
        invalidCredentials
      )).rejects.toThrow('Email and password are required');
      
      expect(sendFlashMessage).toHaveBeenCalledWith('Nesprávný email nebo heslo', 'error');
    });

    it('should handle AuthService being unavailable', async () => {
      // Arrange
      AuthService.login.mockImplementation(() => {
        throw new Error('AuthService not available');
      });

      // Act & Assert
      await expect(userStore.actions.login(
        { commit: mockCommit, dispatch: mockDispatch }, 
        { email: '<EMAIL>', password: 'password' }
      )).rejects.toThrow('AuthService not available');
    });
  });
});