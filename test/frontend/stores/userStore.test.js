import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createStore } from 'vuex'
import userStore from '@/store/userStore'
import axios from 'axios'
import JwtStorageService from '@/services/jwtStorage'

// Mock axios
vi.mock('axios', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
    delete: vi.fn(),
    defaults: {
      headers: {
        common: {}
      }
    }
  }
}))

// Mock JwtStorageService
vi.mock('@/services/jwtStorage')

// Mock flash message utility
vi.mock('@/utils/flashMessage', () => ({
  sendFlashMessage: vi.fn()
}))

describe('userStore', () => {
  let store

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Mock JwtStorageService methods
    JwtStorageService.getToken = vi.fn(() => null)
    JwtStorageService.setToken = vi.fn()
    JwtStorageService.removeToken = vi.fn()
    JwtStorageService.hasToken = vi.fn(() => false)
    
    store = createStore({
      modules: {
        user: userStore,
      },
    })
  })

  describe('mutations', () => {
    it('setUserRole sets the user role', () => {
      store.commit('user/setUserRole', 'admin')
      expect(store.state.user.role).toBe('admin')
    })

    it('setUserRoles handles array of roles', () => {
      store.commit('user/setUserRoles', ['owner', 'admin'])
      expect(store.state.user.roles).toEqual(['owner', 'admin'])
    })

    it('setUserRoles handles single role', () => {
      store.commit('user/setUserRoles', 'owner')
      expect(store.state.user.roles).toEqual(['owner'])
    })

    it('setUserRoles handles array with all values', () => {
      store.commit('user/setUserRoles', ['owner', 'admin', 'employee'])
      expect(store.state.user.roles).toEqual(['owner', 'admin', 'employee'])
    })

    it('setPermissions sets permissions object', () => {
      const permissions = { canEdit: true, canDelete: false }
      store.commit('user/setPermissions', permissions)
      expect(store.state.user.permissions).toEqual(permissions)
    })

    it('setPermissions handles null permissions', () => {
      store.commit('user/setPermissions', null)
      expect(store.state.user.permissions).toEqual({})
    })

    it('setEmail sets user email', () => {
      store.commit('user/setEmail', '<EMAIL>')
      expect(store.state.user.email).toBe('<EMAIL>')
    })

    it('setPlanInfo sets plan information', () => {
      const planInfo = {
        hasPlusPlan: true,
        hasPremiumPlan: false,
        currentPlan: 'plus',
      }
      store.commit('user/setPlanInfo', planInfo)
      expect(store.state.user.hasPlusPlan).toBe(true)
      expect(store.state.user.hasPremiumPlan).toBe(false)
      expect(store.state.user.currentPlan).toBe('plus')
    })
  })

  describe('getters', () => {
    beforeEach(() => {
      store.commit('user/setUserRoles', ['owner', 'admin'])
      store.commit('user/setPermissions', {
        canEdit: true,
        canDelete: false,
        canView: true,
      })
    })

    it('firstRole returns the first role', () => {
      store.commit('user/setUserRole', 'owner')
      expect(store.getters['user/firstRole']).toBe('owner')
    })

    it('hasRole checks if user has specific role', () => {
      expect(store.getters['user/hasRole']('owner')).toBe(true)
      expect(store.getters['user/hasRole']('admin')).toBe(true)
      expect(store.getters['user/hasRole']('employee')).toBe(false)
    })

    it('hasAnyRole checks if user has any of the specified roles', () => {
      expect(store.getters['user/hasAnyRole'](['employee', 'owner'])).toBe(true)
      expect(store.getters['user/hasAnyRole'](['employee', 'supervisor'])).toBe(false)
    })

    it('isOwner returns true for owner role', () => {
      expect(store.getters['user/isOwner']).toBe(true)
    })

    it('isAdmin returns true for admin role', () => {
      expect(store.getters['user/isAdmin']).toBe(true)
    })

    it('isSupervisor returns false when user is not supervisor', () => {
      expect(store.getters['user/isSupervisor']).toBe(false)
    })

    it('isManager returns true for owner, admin, or supervisor', () => {
      expect(store.getters['user/isManager']).toBe(true)
      
      // Test with only supervisor role
      store.commit('user/setUserRoles', ['supervisor'])
      expect(store.getters['user/isManager']).toBe(true)
      
      // Test with employee role
      store.commit('user/setUserRoles', ['employee'])
      expect(store.getters['user/isManager']).toBe(false)
    })

    it('can checks permissions', () => {
      expect(store.getters['user/can']('canEdit')).toBe(true)
      expect(store.getters['user/can']('canDelete')).toBe(false)
      expect(store.getters['user/can']('canView')).toBe(true)
      expect(store.getters['user/can']('nonExistentPermission')).toBe(false)
    })

    it('plan getters return correct values', () => {
      store.commit('user/setPlanInfo', {
        hasPlusPlan: true,
        hasPremiumPlan: false,
        currentPlan: 'plus',
      })
      
      expect(store.getters['user/hasPlusPlan']).toBe(true)
      expect(store.getters['user/hasPremiumPlan']).toBe(false)
      expect(store.getters['user/currentPlan']).toBe('plus')
    })
    
    it('JWT-related getters return correct values', () => {
      // Initially no JWT token
      expect(store.getters['user/hasJwtToken']).toBe(false)
      expect(store.getters['user/jwtToken']).toBe(null)
      expect(store.getters['user/jwtUser']).toBe(null)
      expect(store.getters['user/authMethod']).toBe(null)
      
      // Set JWT token
      store.commit('user/SET_JWT_TOKEN', 'test-jwt-token')
      expect(store.getters['user/hasJwtToken']).toBe(true)
      expect(store.getters['user/jwtToken']).toBe('test-jwt-token')
      expect(store.getters['user/authMethod']).toBe('jwt')
      
      // Set JWT user
      const jwtUser = { id: 1, email: '<EMAIL>' }
      store.commit('user/SET_JWT_USER', jwtUser)
      expect(store.getters['user/jwtUser']).toEqual(jwtUser)
      expect(store.getters['user/effectiveUser']).toEqual(jwtUser)
      
      // Clear JWT token
      store.commit('user/CLEAR_JWT_TOKEN')
      expect(store.getters['user/hasJwtToken']).toBe(false)
      expect(store.getters['user/jwtToken']).toBe(null)
      expect(store.getters['user/jwtUser']).toBe(null)
    })
    
    it('effectiveUser prefers JWT user over session user', () => {
      const sessionUser = { id: 1, email: '<EMAIL>' }
      const jwtUser = { id: 1, email: '<EMAIL>' }
      
      // Set session user only
      store.commit('user/setAuth', { token: null, user: sessionUser })
      expect(store.getters['user/effectiveUser']).toEqual(sessionUser)
      expect(store.getters['user/authMethod']).toBe('session')
      
      // Add JWT user - should take precedence
      store.commit('user/SET_JWT_USER', jwtUser)
      expect(store.getters['user/effectiveUser']).toEqual(jwtUser)
      expect(store.getters['user/authMethod']).toBe('session') // No JWT token set yet
      
      // Set JWT token
      store.commit('user/SET_JWT_TOKEN', 'test-token')
      expect(store.getters['user/authMethod']).toBe('jwt')
    })
  })

  describe('JWT mutations', () => {
    it('SET_JWT_TOKEN stores token and sets axios header', () => {
      const token = 'test-jwt-token'
      
      store.commit('user/SET_JWT_TOKEN', token)
      
      expect(store.state.user.jwtToken).toBe(token)
      expect(JwtStorageService.setToken).toHaveBeenCalledWith(token)
      expect(axios.defaults.headers.common['Authorization']).toBe(`Bearer ${token}`)
    })
    
    it('SET_JWT_TOKEN handles storage errors gracefully', () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      JwtStorageService.setToken = vi.fn(() => {
        throw new Error('Storage error')
      })
      
      store.commit('user/SET_JWT_TOKEN', 'test-token')
      
      expect(store.state.user.jwtToken).toBe(null)
      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to store JWT token:', expect.any(Error))
      
      consoleErrorSpy.mockRestore()
    })
    
    it('SET_JWT_TOKEN with null clears token and headers', () => {
      // First set a token
      store.commit('user/SET_JWT_TOKEN', 'test-token')
      
      // Then clear it
      store.commit('user/SET_JWT_TOKEN', null)
      
      expect(store.state.user.jwtToken).toBe(null)
      expect(JwtStorageService.removeToken).toHaveBeenCalled()
      expect(axios.defaults.headers.common['Authorization']).toBeUndefined()
    })
    
    it('CLEAR_JWT_TOKEN clears all JWT state', () => {
      // Set up JWT state
      store.commit('user/SET_JWT_TOKEN', 'test-token')
      store.commit('user/SET_JWT_USER', { id: 1, email: '<EMAIL>' })
      
      // Clear JWT
      store.commit('user/CLEAR_JWT_TOKEN')
      
      expect(store.state.user.jwtToken).toBe(null)
      expect(store.state.user.jwtUser).toBe(null)
      expect(JwtStorageService.removeToken).toHaveBeenCalled()
      expect(axios.defaults.headers.common['Authorization']).toBeUndefined()
    })
    
    it('SET_JWT_USER sets user and authentication state', () => {
      const user = { id: 1, email: '<EMAIL>' }
      
      store.commit('user/SET_JWT_USER', user)
      
      expect(store.state.user.jwtUser).toEqual(user)
      expect(store.state.user.isAuthenticated).toBe(true)
    })
    
    it('clearAuth clears both session and JWT state', () => {
      // Set up both session and JWT state
      store.commit('user/setAuth', { token: 'session-token', user: { id: 1 } })
      store.commit('user/SET_JWT_TOKEN', 'jwt-token')
      store.commit('user/SET_JWT_USER', { id: 1, email: '<EMAIL>' })
      store.commit('user/setUserRole', 'admin')
      
      // Clear all auth
      store.commit('user/clearAuth')
      
      // Verify session state cleared
      expect(store.state.user.token).toBe(null)
      expect(store.state.user.user).toBe(null)
      
      // Verify JWT state cleared
      expect(store.state.user.jwtToken).toBe(null)
      expect(store.state.user.jwtUser).toBe(null)
      
      // Verify shared state cleared
      expect(store.state.user.isAuthenticated).toBe(false)
      expect(store.state.user.role).toBe('')
      expect(store.state.user.roles).toEqual([])
      expect(store.state.user.company).toBe(null)
      
      // Verify storage cleared
      expect(JwtStorageService.removeToken).toHaveBeenCalled()
    })
  })
  
  describe('actions', () => {
    beforeEach(() => {
      // Reset axios mocks before each test
      vi.clearAllMocks()
      axios.post.mockClear()
      axios.get.mockClear()
      axios.delete.mockClear()
      
      // Reset JwtStorageService mocks
      JwtStorageService.getToken = vi.fn(() => null)
      JwtStorageService.setToken = vi.fn()
      JwtStorageService.removeToken = vi.fn()
      JwtStorageService.hasToken = vi.fn(() => false)
      
      // Reset store state
      store.commit('user/clearAuth')
    })

    describe('fetchUserData', () => {
      it('fetches and commits user data', async () => {
        const mockUserData = {
          data: {
            email: '<EMAIL>',
            role: 'admin',
            roles: ['admin', 'owner'],
            permissions: { canEdit: true },
            has_plus_plan: true,
            has_premium_plan: false,
            current_plan: 'plus',
          },
        }

        axios.get = vi.fn().mockResolvedValueOnce(mockUserData)

        await store.dispatch('user/fetchUserData')

        expect(axios.get).toHaveBeenCalledWith('/api/v1/user', {
          withCredentials: true
        })
        expect(store.state.user.email).toBe('<EMAIL>')
        expect(store.state.user.role).toBe('admin')
        expect(store.state.user.roles).toEqual(['admin', 'owner'])
        expect(store.state.user.permissions).toEqual({ canEdit: true })
        expect(store.state.user.hasPlusPlan).toBe(true)
        expect(store.state.user.hasPremiumPlan).toBe(false)
        expect(store.state.user.currentPlan).toBe('plus')
      })

      it('handles errors gracefully', async () => {
        // Mock console.error to suppress expected error output
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
        
        axios.get = vi.fn().mockRejectedValueOnce(new Error('Network error'))
        
        // Should not throw
        await expect(store.dispatch('user/fetchUserData')).resolves.toBeUndefined()
        
        // Verify error was logged
        expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching user data:', expect.any(Error))
        
        consoleErrorSpy.mockRestore()
      })

      it('clears auth on 401 error', async () => {
        axios.get.mockRejectedValueOnce({
          response: { status: 401 }
        })
        
        // Mock console.error to suppress expected error output
        const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
        
        await store.dispatch('user/fetchUserData')
        
        expect(store.state.user.isAuthenticated).toBe(false)
        
        consoleErrorSpy.mockRestore()
      })
    })

    describe('login', () => {
      it('successfully logs in with JWT', async () => {
        const credentials = { email: '<EMAIL>', password: 'password123' }
        const mockJwtResponse = {
          data: {
            success: true,
            access_token: 'jwt-access-token',
            refresh_token: 'jwt-refresh-token',
            user: { id: 1, email: '<EMAIL>', company_id: 1 }
          }
        }
        
        // Mock axios.post to return JWT response
        axios.post.mockResolvedValueOnce(mockJwtResponse)
        // Mock axios.get for fetchUserData call
        axios.get.mockResolvedValueOnce({
          data: { email: '<EMAIL>', role: 'user' }
        })

        const result = await store.dispatch('user/login', credentials)

        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/jwt_login', credentials)
        expect(store.state.user.jwtToken).toBe('jwt-access-token')
        expect(store.state.user.jwtUser).toEqual(mockJwtResponse.data.user)
        expect(result).toEqual(mockJwtResponse.data)
      })

      it('falls back to session login when JWT fails', async () => {
        const credentials = { email: '<EMAIL>', password: 'password123' }
        const mockSessionResponse = { status: 200, data: { success: true } }
        
        // Mock cookie for locale
        Object.defineProperty(document, 'cookie', {
          writable: true,
          value: 'locale=cs'
        })
        
        axios.post
          .mockRejectedValueOnce(new Error('JWT login failed'))  // JWT login fails
          .mockResolvedValueOnce(mockSessionResponse)             // Session login succeeds
        
        // Mock axios.get for fetchUserData call
        axios.get.mockResolvedValueOnce({
          data: { email: '<EMAIL>', role: 'user' }
        })

        const result = await store.dispatch('user/login', credentials)

        expect(axios.post).toHaveBeenNthCalledWith(1, '/api/v1/auth/jwt_login', credentials)
        expect(axios.post).toHaveBeenNthCalledWith(2, '/cs/users/sign_in', { user: credentials })
        expect(result).toEqual(mockSessionResponse.data)
      })

      it('handles both JWT and session login failures', async () => {
        const credentials = { email: '<EMAIL>', password: 'wrong' }
        
        axios.post
          .mockRejectedValueOnce(new Error('JWT login failed'))
          .mockRejectedValueOnce(new Error('Session login failed'))

        await expect(store.dispatch('user/login', credentials)).rejects.toThrow('Session login failed')
        
        expect(axios.post).toHaveBeenCalledTimes(2)
      })

      it('handles JWT login without access_token gracefully', async () => {
        const credentials = { email: '<EMAIL>', password: 'password123' }
        const mockResponse = {
          data: { success: true, user: { email: '<EMAIL>' } }
        }
        
        axios.post.mockResolvedValueOnce(mockResponse)  // JWT login returns response without access_token

        // Should not execute JWT branch, should return undefined
        const result = await store.dispatch('user/login', credentials)

        expect(result).toBeUndefined()
        expect(store.state.user.jwtToken).toBe(null)
        expect(store.state.user.jwtUser).toBe(null)
      })
    })

    describe('logout', () => {
      it('logs out via JWT endpoint first', async () => {
        axios.post.mockResolvedValueOnce({ data: { success: true } })

        await store.dispatch('user/logout')

        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/logout')
        expect(store.state.user.isAuthenticated).toBe(false)
        expect(store.state.user.jwtToken).toBe(null)
        expect(store.state.user.user).toBe(null)
      })

      it('falls back to session logout when JWT logout fails', async () => {
        // Mock cookie for locale
        Object.defineProperty(document, 'cookie', {
          writable: true,
          value: 'locale=en'
        })
        
        axios.post.mockClear()
        axios.delete.mockClear()
        
        axios.post.mockRejectedValueOnce(new Error('JWT logout failed'))
        axios.delete.mockResolvedValueOnce({ data: { success: true } })

        await store.dispatch('user/logout')

        expect(axios.post).toHaveBeenCalledWith('/api/v1/auth/logout')
        expect(axios.delete).toHaveBeenCalledWith('/en/users/sign_out')
        expect(store.state.user.isAuthenticated).toBe(false)
      })

      it('clears all auth state regardless of API response', async () => {
        // Set up some auth state first
        store.commit('user/SET_JWT_TOKEN', 'test-token')
        store.commit('user/SET_JWT_USER', { id: 1 })
        store.commit('user/setAuth', { token: 'session-token', user: { id: 1 } })
        
        // Mock cookie for locale
        Object.defineProperty(document, 'cookie', {
          writable: true,
          value: 'locale=cs'
        })
        
        // Mock console.warn to suppress expected warning output
        const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
        
        axios.post.mockClear()
        axios.delete.mockClear()
        
        axios.post.mockRejectedValueOnce(new Error('Network error'))
        axios.delete.mockRejectedValueOnce(new Error('Network error'))

        await store.dispatch('user/logout')

        // Should clear auth even if API calls fail
        expect(store.state.user.isAuthenticated).toBe(false)
        expect(store.state.user.jwtToken).toBe(null)
        expect(store.state.user.jwtUser).toBe(null)
        expect(store.state.user.token).toBe(null)
        expect(store.state.user.user).toBe(null)
        
        // Should have logged a warning about failed logout
        expect(consoleWarnSpy).toHaveBeenCalledWith('Both JWT and session logout failed:', expect.any(Error))
        
        consoleWarnSpy.mockRestore()
      })
    })

    describe('register', () => {
      it('successfully registers and logs in user', async () => {
        const userData = {
          email: '<EMAIL>',
          password: 'password123',
          name: 'New User'
        }
        
        const mockRegisterResponse = { status: 201, data: { success: true } }
        
        // Mock cookie for locale
        Object.defineProperty(document, 'cookie', {
          writable: true,
          value: 'locale=sk'
        })
        
        // Clear and set up fresh mocks
        axios.post.mockClear()
        axios.get.mockClear()
        
        // Mock the register API call
        axios.post.mockResolvedValueOnce(mockRegisterResponse)
        // Mock the subsequent login call (JWT)
        axios.post.mockResolvedValueOnce({
          data: {
            access_token: 'jwt-token',
            user: { email: '<EMAIL>' }
          }
        })
        // Mock fetchUserData
        axios.get.mockResolvedValueOnce({
          data: { email: '<EMAIL>', role: 'user' }
        })

        const result = await store.dispatch('user/register', userData)

        expect(axios.post).toHaveBeenNthCalledWith(1, '/sk/users', { user: userData })
        expect(result).toEqual(mockRegisterResponse.data)
      })

      it('handles registration validation errors', async () => {
        const userData = { email: 'invalid', password: '123' }
        const mockErrorResponse = {
          response: {
            data: {
              errors: {
                email: ['is invalid'],
                password: ['is too short']
              }
            }
          }
        }
        
        // Default locale should be 'cs'
        Object.defineProperty(document, 'cookie', {
          writable: true,
          value: ''
        })
        
        // Clear any previous mocks and set up fresh mock
        axios.post.mockClear()
        axios.post.mockRejectedValueOnce(mockErrorResponse)

        await expect(store.dispatch('user/register', userData)).rejects.toThrow()
        
        expect(axios.post).toHaveBeenCalledWith('/cs/users', { user: userData })
      })

      it('handles generic registration errors', async () => {
        const userData = { email: '<EMAIL>', password: 'password123' }
        
        axios.post.mockClear()
        axios.post.mockRejectedValueOnce(new Error('Server error'))

        await expect(store.dispatch('user/register', userData)).rejects.toThrow()
      })

      it('handles successful registration with 200 status', async () => {
        const userData = { email: '<EMAIL>', password: 'password123' }
        const mockResponse = { status: 200, data: { success: true } }
        
        // Clear and set up fresh mocks
        axios.post.mockClear()
        axios.get.mockClear()
        
        // Mock the register API call
        axios.post.mockResolvedValueOnce(mockResponse)
        // Mock the subsequent login call (JWT)
        axios.post.mockResolvedValueOnce({
          data: {
            access_token: 'jwt-token',
            user: { email: '<EMAIL>' }
          }
        })
        // Mock fetchUserData
        axios.get.mockResolvedValueOnce({
          data: { email: '<EMAIL>', role: 'user' }
        })

        await store.dispatch('user/register', userData)

        expect(axios.post).toHaveBeenNthCalledWith(1, '/cs/users', { user: userData })
        expect(axios.post).toHaveBeenNthCalledWith(2, '/api/v1/auth/jwt_login', {
          email: userData.email,
          password: userData.password
        })
      })
    })
  })
})