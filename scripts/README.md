# Scripts Directory

Organized collection of utility scripts for development, debugging, and maintenance.

## Directory Structure

```
scripts/
├── debug/          # Ad-hoc debugging scripts for specific issues
├── maintenance/    # Regular maintenance and cleanup scripts  
├── migration/      # Data migration and transformation scripts
├── testing/        # Testing utilities and helpers
└── README.md       # This file
```

## Usage

All scripts should be run using Rails runner to have access to the application context:

```bash
bundle exec rails runner scripts/category/script_name.rb
```

## Best Practices

### Naming Convention
- Use descriptive names: `fix_orphaned_primary_companies.rb`
- Include issue numbers when applicable: `tym52_debug_user_roles.rb`
- Use snake_case for consistency with Rails conventions

### Documentation
- Each directory should have its own README.md
- Scripts should include comments explaining their purpose
- Include usage examples and any prerequisites

### Safety
- Scripts that modify data should include confirmation prompts
- Always test scripts in development first
- Include rollback instructions for migration scripts

### Cleanup
- Remove or archive scripts when issues are permanently resolved
- Keep reusable utilities, remove one-time fixes