# ABOUTME: Verification script to check user's primary company and active roles after a fix
# ABOUTME: Usage: rails runner scripts/debug/verify_fix.rb [user_id]

user_id = ARGV[0]&.to_i || (puts "Usage: rails runner scripts/debug/verify_fix.rb <user_id>"; exit(1))

begin
  user = User.find(user_id)
rescue ActiveRecord::RecordNotFound
  puts "❌ User with ID #{user_id} not found"
  exit(1)
end

puts "=== VERIFICATION ==="
puts "User: #{user.email} (ID: #{user_id})"
puts "Primary company ID: #{user.primary_company&.id}"
puts "Primary company name: #{user.primary_company&.name}"
puts ""
puts "Active roles:"
user.company_user_roles.each do |role|
  puts "  Company #{role.company_id} (#{role.company.name}): #{role.role.name}, Primary: #{role.is_primary}"
end
puts ""
puts "JWT should now work with company_id: #{user.primary_company&.id}"