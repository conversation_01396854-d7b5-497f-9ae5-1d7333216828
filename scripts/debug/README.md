# Debug Scripts

This directory contains ad-hoc debugging and testing scripts for troubleshooting specific issues.

## Usage

Run scripts with Rails runner:
```bash
bundle exec rails runner scripts/debug/script_name.rb [user_id]
```

All debug scripts require a user ID as the first argument.

## Available Scripts

### Company Disconnection Issue (TYM-52)
- `debug_user.rb` - Check user's company roles state
  ```bash
  bundle exec rails runner scripts/debug/debug_user.rb 7
  ```
- `verify_fix.rb` - Verify user primary company is properly set
  ```bash
  bundle exec rails runner scripts/debug/verify_fix.rb 7
  ```
- `test_jwt_fix.rb` - Test JWT authentication flow for user
  ```bash
  bundle exec rails runner scripts/debug/test_jwt_fix.rb 7
  ```

## Best Practices

- Scripts should be self-contained and documented
- Use descriptive names indicating the issue they address
- Include issue numbers in script names when applicable
- Clean up scripts when issues are resolved permanently