# ABOUTME: Test script to simulate JWT authentication process for a specific user
# ABOUTME: Usage: rails runner scripts/debug/test_jwt_fix.rb [user_id]

user_id = ARGV[0]&.to_i || (puts "Usage: rails runner scripts/debug/test_jwt_fix.rb <user_id>"; exit(1))

begin
  user = User.find(user_id)
rescue ActiveRecord::RecordNotFound
  puts "❌ User with ID #{user_id} not found"
  exit(1)
end

puts "=== JWT AUTHENTICATION TEST ==="
puts "Testing user: #{user.email} (ID: #{user_id})"
puts ""

# Simulate what happens during JWT token generation
primary_company = user.primary_company

if primary_company
  puts "✅ User has primary company: #{primary_company.id} (#{primary_company.name})"
  puts "✅ JWT will include company_id: #{primary_company.id}"
  
  # Check if user has active role in this company
  active_role = user.company_user_roles.find_by(company: primary_company, active: true)
  if active_role
    puts "✅ User has active role: #{active_role.role.name}"
    puts "✅ JWT tenant validation will PASS"
    puts "✅ @company will be set to: #{primary_company.name}"
    puts "✅ All API endpoints should work"
  else
    puts "❌ User has no active role in primary company"
  end
else
  puts "❌ User has no primary company - JWT will have company_id: null"
  puts "❌ All API endpoints will fail"
end