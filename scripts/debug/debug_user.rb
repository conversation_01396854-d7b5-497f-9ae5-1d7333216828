# ABOUTME: Debug script to display user state including primary company and all roles
# ABOUTME: Usage: rails runner scripts/debug/debug_user.rb [user_id]

user_id = ARGV[0]&.to_i || (puts "Usage: rails runner scripts/debug/debug_user.rb <user_id>"; exit(1))

begin
  user = User.find(user_id)
rescue ActiveRecord::RecordNotFound
  puts "❌ User with ID #{user_id} not found"
  exit(1)
end

puts "=== USER #{user_id} STATE ==="
puts "Email: #{user.email}"
puts "Primary company: #{user.primary_company&.id}"
puts "All roles (including inactive):"
CompanyUserRole.unscoped.where(user_id: user_id).each do |role|
  puts "  ID #{role.id}: Company #{role.company_id}, Active: #{role.active}, Primary: #{role.is_primary}"
end