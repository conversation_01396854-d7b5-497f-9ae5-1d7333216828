<template>
  <div :class="notification ? 'flex flex-col p-2' : 'card flex flex-col p-4'">
    <div class="card-header flex justify-between items-start mb-3 p-0 border-b-0">
      <div class="flex items-center gap-2">
        <CalendarCheck :size="16" class="text-gray-500 flex-shrink-0" />
        <h3 class="text-base font-semibold m-0">
          {{ booking.booking_link?.name || $t('booking.updated', 'Aktualizovaná rezervace') }}
        </h3>
      </div>
      <div class="relative" ref="dropdown">
        <button class="p-1 text-gray-500 hover:text-gray-700" @click="toggleDropdown">
          <MoreVertical :size="18" />
        </button>
        <div
          v-if="showDropdown"
          class="absolute right-0 p-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
        >
          <a href="#"
             v-if="booking.status === 'confirmed'"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="openRescheduleModal">
            {{ $t('reschedule', 'Přeplánovat') }}
          </a>
          <a href="#"
             v-if="booking.status === 'pending' || booking.status === 'rescheduled'"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="$emit('confirm', booking)">
            {{ $t('booking.confirm_without_time', 'Potvrdit bez času') }}
          </a>
          <a href="#"
             v-if="booking.status === 'pending' || booking.status === 'rescheduled'"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="openConfirmWithTimeModal()">
            {{ $t('booking.confirm_with_time', 'Potvrdit s časem') }}
          </a>
          <a href="#"
             v-if="booking.status === 'confirmed' && !booking.work"
             class="block px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100"
             @click.prevent="$emit('convert-to-work', booking)">
            {{ $t('booking.convert_to_work', 'Převést na práci') }}
          </a>
          <a href="#"
             v-if="booking.status !== 'cancelled'"
             class="block px-3 py-1.5 text-sm text-red-600 hover:bg-red-50"
             @click.prevent="$emit('cancel', booking)">
            {{ $t('cancel', 'Zrušit') }}
          </a>
        </div>
      </div>
    </div>

    <div class="flex flex-wrap gap-2 mb-3">
      <span class="badge" :class="statusBadgeClass">
        {{ translatedStatus }}
      </span>
      <span v-if="booking.work?.status" class="badge" :class="workStatusBadgeClass">
        {{ translatedWorkStatus }}
      </span>
    </div>

    <div class="flex flex-col gap-1.5 text-sm text-gray-700">
      <div class="flex items-center gap-1.5">
        <Calendar :size="14" class="text-gray-500 flex-shrink-0" />
        <span>
          {{ formatDate(booking.preferred_date) }} - {{ translatedTime }}
          {{ booking.specific_time ? `- ${$t('preferred_time', 'Preferovaný čas')} ${formatTime(booking.specific_time)}` : '' }}
        </span>
      </div>
      <div class="flex items-center gap-1.5">
        <MapPin :size="14" class="text-gray-500 flex-shrink-0" />
        {{ booking.location }}
      </div>
    </div>

    <p v-if="booking.message" class="text-sm text-gray-600 mb-3 break-words">
      {{ booking.message }}
    </p>

    <div class="flex flex-col gap-1.5 text-sm text-gray-700">
      <div class="flex items-center gap-1.5">
        <User :size="14" class="text-gray-500 flex-shrink-0" />
        <span class="text-sm">{{ booking.client_name }}</span>
      </div>
      <div class="flex items-center gap-1.5">
        <Mail :size="14" class="text-gray-500 flex-shrink-0" />
        <a v-if="booking.client_email" :href="'mailto:' + booking.client_email" class="text-link">{{ booking.client_email }}</a>
        <span v-else class="bg-red-100">
          {{ $t('booking.no_email', 'nemá e-mail - nutné kontaktovat telefonicky') }}
        </span>
      </div>
      <div v-if="booking.client_phone" class="flex items-center gap-1.5">
        <Phone :size="14" class="text-gray-500 flex-shrink-0" />
        <a :href="'tel:' + booking.client_phone" class="text-link">{{ booking.client_phone }}</a>
      </div>
    </div>

 
    <!-- Modal -->
    <div v-if="showTimeConfirmModal" class="modal-overlay" @click.self="showTimeConfirmModal = false">
      <div class="modal-container">
        <div class="modal-header">
          <h3>{{ isRescheduling ? $t('booking.reschedule', 'Přeplánovat rezervaci') : $t('booking.confirm_with_date_time', 'Potvrdit s konkrétním datem a/nebo časem') }}</h3>
          <button class="close-btn" @click="showTimeConfirmModal = false">&times;</button>
        </div>
        <div class="central-modal-content">
          <div class="mb-4">
            <label for="confirm-date" class="block text-sm font-medium text-gray-700 mb-1">{{ $t('date', 'Datum') }}:</label>
            <VueDatePicker
              v-model="confirmDate"
              model-type="timestamp"
              :locale= "$i18n.locale"
              format="d. M. yyyy"
              :cancel-text="$t('cancel', 'Zrušit')"
              :select-text="$t('select', 'Vybrat')"
              :enable-time-picker="false"
              :placeholder="$t('select_date', 'Vyberte datum')"
              input-class-name="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t('time', 'Čas') }}:</label>
             <TimePicker 
                v-model="confirmedTime"
                class="w-full" 
            />
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" @click="confirmWithTime">{{ $t('confirm', 'Potvrdit') }}</button>
            <button class="btn btn-outline" @click="showTimeConfirmModal = false">{{ $t('cancel', 'Zrušit') }}</button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import dayjs from 'dayjs';
import { Calendar, Clock, Mail, MoreVertical, Phone, User, CalendarCheck, MapPin } from 'lucide-vue-next'; // Import icons
import TimePicker from '@/components/TimePicker.vue'; // Import TimePicker

export default {
  props: {
    booking: {
      type: Object,
      required: true
    },
    compact: {
      type: Boolean,
      default: false
    },
    notification: {
      type: Boolean,
      default: false
    }
  },
  components: {
    VueDatePicker,
    Calendar, 
    Clock,
    Mail,
    MoreVertical,
    Phone,
    User,
    CalendarCheck,
    MapPin,
    TimePicker // Register TimePicker
  },
  emits: ['confirm', 'cancel', 'view', 'convert-to-work', 'update'],
  data() {
    return {
      showTimeConfirmModal: false,
      confirmDate: this.booking.preferred_date ? dayjs(this.booking.preferred_date).valueOf() : null,
      confirmHour: null,
      confirmMinute: null,
      isRescheduling: false,
      showDropdown: false, 
      confirmedTime: { hour: null, minute: null },
      showConfirmModal: false,
    };
  },
  computed: {
    translatedStatus() {
      const statusTranslations = {
        pending: this.$t('booking.new_reservation', 'Nová rezervace'),
        confirmed: this.$t('booking.confirmed_reservation', 'Potvrzená rezervace'),
        cancelled: this.$t('booking.cancelled_reservation', 'Zrušená rezervace'),
        rescheduled: this.$t('booking.rescheduled_reservation', 'Přeplánovaná rezervace')
      };
      return statusTranslations[this.booking.status] || this.booking.status;
    },
    translatedTime() {
      const timeTranslations = {
        morning: this.$t('morning', 'Dopoledne'),
        afternoon: this.$t('afternoon', 'Odpoledne')
      };
      return timeTranslations[this.booking.preferred_period] || this.booking.preferred_period;
    },
    translatedWorkStatus() {
      const workStatusTranslations = {
        scheduled: this.$t('works.scheduled_work', 'Naplánovaná práce'),
        in_progress: this.$t('works.in_progress_work', 'Práce probíhá'),
        completed: this.$t('works.completed_work', 'Dokončená práce'),
        cancelled: this.$t('works.cancelled_work', 'Zrušená práce'),
        rescheduled: this.$t('booking.booking_change_notice', 'Pozor, změna rezervace'),
        unprocessed: this.$t('works.unprocessed_work', 'Nová práce')
      };
      return workStatusTranslations[this.booking.work?.status] || this.booking.work?.status;
    },
    statusBadgeClass() {
      const statusClasses = {
        pending: 'badge-warning',
        confirmed: 'badge-success',
        cancelled: 'badge-danger',
        rescheduled: 'badge-warning' 
      };
      return statusClasses[this.booking.status] || 'badge-gray';
    },
    workStatusBadgeClass() {
        if (!this.booking.work?.status) return '';
        const workStatusClasses = {
            scheduled: 'badge-info', 
            in_progress: 'badge-primary', 
            completed: 'badge-success',
            cancelled: 'badge-danger',
            rescheduled: 'badge-warning',
            unprocessed: 'badge-secondary'
        };
        return workStatusClasses[this.booking.work?.status] || 'badge-gray';
    }
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
    },
    handleClickOutside(event) {
      if (this.showDropdown && this.$refs.dropdown && !this.$refs.dropdown.contains(event.target)) {
        this.showDropdown = false;
      }
    },
    openConfirmWithTimeModal() {
      this.confirmDate = this.booking.preferred_date ? dayjs(this.booking.preferred_date).valueOf() : null;
      
      // Reset confirmedTime
      this.confirmedTime = { hour: null, minute: null };

      // Initialize confirmedTime if specific_time exists
      if (this.booking.specific_time && typeof this.booking.specific_time === 'string') {
        const parsedDateTime = dayjs(this.booking.specific_time);
        if (parsedDateTime.isValid()) {
          this.confirmedTime = { 
            hour: parsedDateTime.hour(), 
            minute: parsedDateTime.minute() 
          };
        } else {
           console.error("Failed to parse booking.specific_time on modal open:", this.booking.specific_time);
        }
      }

      this.isRescheduling = false;
      this.showTimeConfirmModal = true;
      this.showDropdown = false;
    },
    formatDate(dateString) {
      if (!dateString) return this.$t('unspecified', 'Nespecifikován');
      const date = new Date(dateString);
      return date.toLocaleDateString('cs-CZ', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric'
      });
    },
    formatTime(timestampString) {
      if (!timestampString) return this.$t('unspecified', 'Nespecifikován'); 
      const parsedDateTime = dayjs(timestampString);
      if (parsedDateTime.isValid()) {
        return parsedDateTime.format('HH:mm');
      } else {
        console.error("Invalid timestamp string for formatting:", timestampString);
        return this.$t('booking.time-error', 'Chyba času'); 
      }
    },
    confirmWithTime() {
      const options = {};
      let specificTimeString = null;

      // Use confirmedTime.hour and confirmedTime.minute
      if (this.confirmDate && this.confirmedTime.hour !== null && this.confirmedTime.minute !== null) {
        const datePart = dayjs(this.confirmDate).format('YYYY-MM-DD');
        // Ensure hour and minute are numbers before padding
        const hour = parseInt(this.confirmedTime.hour, 10);
        const minute = parseInt(this.confirmedTime.minute, 10);

        if (isNaN(hour) || isNaN(minute)) {
            alert(this.$t('booking.invalid-hour-minute', 'Neplatná hodina nebo minuta.'));
            return;
        }

        const hourPart = String(hour).padStart(2, '0');
        const minutePart = String(minute).padStart(2, '0');

        specificTimeString = `${datePart}T${hourPart}:${minutePart}:00`;
        options.specificTime = specificTimeString; 
      } else if (this.confirmedTime.hour !== null || this.confirmedTime.minute !== null) {
        alert(this.$t('booking.date-required', 'Pro výběr konkrétního času musíte vybrat i datum.'));
        return; 
      }

      if (this.confirmDate) {
         options.preferredDate = dayjs(this.confirmDate).format('YYYY-MM-DD');
      } 

      if (!options.preferredDate && !options.specificTime) {
        alert(this.$t('booking.select-date-min', 'Prosím vyberte alespoň datum pro potvrzení/přeplánování.'));
        return;
      }

      if (this.isRescheduling) {
        this.$emit('update', this.booking, options);
      } else {
        this.$emit('confirm', this.booking, options);
      }
      
      this.showTimeConfirmModal = false;
      this.isRescheduling = false;
    },
    openRescheduleModal() {
      this.confirmDate = this.booking.preferred_date ? dayjs(this.booking.preferred_date).valueOf() : null;
      
      // Reset confirmedTime
      this.confirmedTime = { hour: null, minute: null };

      // Initialize confirmedTime if specific_time exists
      if (this.booking.specific_time && typeof this.booking.specific_time === 'string') {
        const parsedDateTime = dayjs(this.booking.specific_time);
        if (parsedDateTime.isValid()) {
          this.confirmedTime = { 
            hour: parsedDateTime.hour(), 
            minute: parsedDateTime.minute() 
          };
        } else {
           console.error("Failed to parse booking.specific_time on reschedule modal open:", this.booking.specific_time);
        }
      }

      this.isRescheduling = true;
      this.showTimeConfirmModal = true;
      this.showDropdown = false;
    },
  },
  mounted() {
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: auto;
  max-width: 500px;
  /* Ensure modal content can scroll if it's too long */
  max-height: 90vh; 
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #333;
}

.central-modal-content {
  /* Allows content to scroll if it exceeds modal height */
  overflow-y: auto; 
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
</style>