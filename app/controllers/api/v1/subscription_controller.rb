module Api
  module V1
    class SubscriptionController < ApiController
      before_action :authenticate_user!
      before_action :set_tenant_company
      
      def status
        plan_name = @company.current_plan&.name || 'Free'
        
        available_features = []
        
        # Free tier features (moved from paid tier)
        available_features.push('booking', 'reservation', 'meeting', 'work_planning')
        
        # Paid features in plus are also available in premium
        if ['plus', 'premium'].include?(plan_name)
          available_features.push('advanced_notifications', 'beta_features')
        end
        
        # Premium-only features
        if plan_name == 'premium'
          available_features.push('advanced_reporting', 'api_access')
        end
        
        render json: {
          success: true,
          current_plan: plan_name,
          subscription_active: @company.current_subscription.present?,
          available_features: available_features,
          expires_at: @company.current_subscription&.expire_date
        }
      end
      
      private
      
      def set_tenant_company
        @company = ActsAsTenant.current_tenant
      end
    end
  end
end