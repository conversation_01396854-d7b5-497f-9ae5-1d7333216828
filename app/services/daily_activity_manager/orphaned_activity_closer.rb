# ABOUTME: Service for automatically closing orphaned daily activities from previous days
# ABOUTME: Prevents data integrity issues when activities remain open indefinitely
#
# NOTE: This service closes ALL daily activities from previous days with a nil end_time,
# not just those considered "orphaned" (e.g., having a nil daily_log_id).

module DailyActivityManager
  class OrphanedActivityCloser
    def self.call
      new.call
    end

    def initialize
      @current_date = Date.current
    end

    def call
      puts "🔍 DEBUG OrphanedActivityCloser - Starting cleanup for date: #{@current_date}"
      puts "🔍 DEBUG OrphanedActivityCloser - Current tenant: #{ActsAsTenant.current_tenant&.name}"
      
      open_activities = find_orphaned_activities
      puts "🔍 DEBUG OrphanedActivityCloser - Found #{open_activities.count} open activities"
      
      return [] if open_activities.empty?

      # Pass the relation directly and return only successfully closed activities
      closed_activities = close_activities(open_activities)
      puts "🔍 DEBUG OrphanedActivityCloser - Successfully closed #{closed_activities.count} activities"
      
      closed_activities
    end

    private

    def find_orphaned_activities
      puts "🔍 DEBUG OrphanedActivityCloser - Looking for activities before: #{@current_date.beginning_of_day}"
      
      query = DailyActivity.where(
        end_time: nil
      ).where(
        'start_time < ?', @current_date.beginning_of_day
      )
      
      puts "🔍 DEBUG OrphanedActivityCloser - Base query found: #{query.count} activities"
      
      # Add company scoping if current tenant is set
      if ActsAsTenant.current_tenant
        puts "🔍 DEBUG OrphanedActivityCloser - Adding company scoping for: #{ActsAsTenant.current_tenant.name}"
        query = query.where(company: ActsAsTenant.current_tenant)
        puts "🔍 DEBUG OrphanedActivityCloser - After company scoping: #{query.count} activities"
      end
      
      query.each do |activity|
        puts "🔍 DEBUG Activity found: ID=#{activity.id}, start_time=#{activity.start_time}, daily_log_id=#{activity.daily_log_id}"
      end
      
      query
    end

    def close_activities(activities)
      closed_activities = []
      ApplicationRecord.transaction do
        activities.find_each do |activity|
          # Set end_time to end of day for the activity's start date
          end_of_activity_day = activity.start_time.end_of_day
          activity.update!(end_time: end_of_activity_day)
          # Only add to the list after successful update
          closed_activities << activity
        end
      end
      # Return the list of successfully closed activities
      # If transaction fails, exception is raised and this line is not reached
      closed_activities
    end
  end
end