# ABOUTME: Backfills NIL status values for legacy Event records
# ABOUTME: Ensures consistent status field for conflict detection queries
class BackfillEventStatusForLegacyEvents < ActiveRecord::Migration[7.0]
  def up
    # Update NIL status events based on event_type
    # Vacation events should be 'pending', all others should be 'approved'
    
    # Process in batches to avoid table locks on large datasets
    Event.where(status: nil).find_in_batches(batch_size: 1000) do |batch|
      puts "Processing batch of #{batch.size} events with NIL status..."
      
      vacation_ids = batch.select { |e| e.event_type == 'vacation' }.map(&:id)
      non_vacation_ids = batch.map(&:id) - vacation_ids
      
      # Set vacation events to 'pending' (awaiting approval)
      if vacation_ids.any?
        Event.where(id: vacation_ids).update_all(status: 'pending')
        puts "  Updated #{vacation_ids.size} vacation events to 'pending'"
      end
      
      # Set non-vacation events to 'approved' (no approval needed)
      if non_vacation_ids.any?
        Event.where(id: non_vacation_ids).update_all(status: 'approved')
        puts "  Updated #{non_vacation_ids.size} non-vacation events to 'approved'"
      end
    end
    
    puts "Migration completed successfully!"
  end
  
  def down
    # This is irreversible - we cannot determine which events originally had NIL status
    raise ActiveRecord::IrreversibleMigration, "Cannot rollback status backfill - original NIL values lost"
  end
end
