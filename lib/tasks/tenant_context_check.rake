# frozen_string_literal: true

# ABOUTME: Development environment tenant context validator to prevent TYM-30 debugging hell
# ABOUTME: by providing real-time context validation and alignment guidance for developers.

namespace :tenant_context do
  desc "Check development environment for tenant context alignment and potential mismatches"
  task check: :environment do
    puts "🔍 TENANT CONTEXT HEALTH CHECK"
    puts "=" * 50
    puts "Preventing TYM-30 multi-tenant testing hell..."
    puts ""
    
    # Check if we're in development environment
    unless Rails.env.development?
      puts "❌ This task is only for development environment"
      puts "Current environment: #{Rails.env}"
      exit 1
    end

    # Get current tenant context
    current_tenant = ActsAsTenant.current_tenant
    puts "📍 CURRENT TENANT CONTEXT"
    if current_tenant
      puts "✅ ActsAsTenant.current_tenant: #{current_tenant.id} - #{current_tenant.name}"
    else
      puts "⚠️  ActsAsTenant.current_tenant: NONE (nil)"
    end
    puts ""

    # Check available test users
    puts "👥 AVAILABLE TEST USERS"
    test_users = User.where(email: [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ])
    
    if test_users.any?
      test_users.each do |user|
        puts "   #{user.email} (ID: #{user.id})"
        user_companies = user.companies.includes(:company_user_roles)
        user_companies.each do |company|
          role = user.company_user_roles.find_by(company: company)&.role&.name
          puts "     - Company #{company.id}: #{company.name} (#{role})"
        end
      end
    else
      puts "❌ No test users found in development database"
    end
    puts ""

    # Check for potential data context issues
    puts "🏢 COMPANY DATA DISTRIBUTION"
    Company.includes(:contracts, :events, :bookings).limit(5).each do |company|
      contracts_count = company.contracts.count
      events_count = company.events.count
      bookings_count = company.bookings.count
      
      puts "   Company #{company.id} (#{company.name}):"
      puts "     - Contracts: #{contracts_count}"
      puts "     - Events: #{events_count}" 
      puts "     - Bookings: #{bookings_count}"
      
      if contracts_count > 0 && (events_count > 0 || bookings_count > 0)
        puts "     ✅ Has test data available"
      elsif contracts_count > 0
        puts "     ⚠️  Has contracts but no events/bookings"
      else
        puts "     ⚠️  No test data"
      end
    end
    puts ""

    # Check for debug scripts that might indicate previous context issues
    puts "🔧 DEBUG SCRIPTS ANALYSIS"
    debug_scripts = Dir.glob(Rails.root.join("*.rb")).select do |file|
      filename = File.basename(file)
      filename.include?('investigate') || 
      filename.include?('check_') || 
      filename.include?('debug_') ||
      filename.include?('test_event') ||
      filename.include?('create_events')
    end
    
    if debug_scripts.any?
      puts "⚠️  Found potential debugging scripts from previous context issues:"
      debug_scripts.each do |script|
        puts "     - #{File.basename(script)}"
      end
      puts "   Consider cleaning these up after context issues are resolved."
    else
      puts "✅ No debugging scripts found"
    end
    puts ""

    # Provide recommendations
    puts "💡 RECOMMENDATIONS"
    puts "1. Use TenantContextValidator in your tests:"
    puts "   ensure_tenant_jwt_context_match(your_jwt_token)"
    puts ""
    puts "2. Check context before API calls:"
    puts "   debug_current_context(your_jwt_token)"
    puts ""  
    puts "3. Create aligned test data:"
    puts "   token = create_test_data_with_matching_jwt_context(user, company)"
    puts ""
    puts "4. Always verify context alignment in tests:"
    puts "   ActsAsTenant.with_tenant(your_target_company) do"
    puts "     # Create your test data here"
    puts "     token = generate_jwt_for_company(user, your_target_company)"
    puts "     # Make API calls with aligned context"
    puts "   end"
    puts ""
    puts "✅ Tenant context check complete!"
  end

  desc "Generate JWT token for specific user and company (development only)"
  task :generate_jwt, [:user_email, :company_id] => :environment do |task, args|
    unless Rails.env.development?
      puts "❌ This task is only for development environment"
      exit 1
    end

    user_email = args[:user_email]
    company_id = args[:company_id]&.to_i

    unless user_email && company_id
      puts "Usage: rake tenant_context:generate_jwt[user_email,company_id]"
      puts "Example: rake tenant_context:generate_jwt[<EMAIL>,98]"
      exit 1
    end

    user = User.find_by(email: user_email)
    unless user
      puts "❌ User not found: #{user_email}"
      exit 1
    end

    company = Company.find_by(id: company_id)
    unless company
      puts "❌ Company not found: #{company_id}"
      exit 1
    end

    # Verify user has access to company
    user_role = user.company_user_roles.find_by(company: company)
    unless user_role
      puts "❌ User #{user_email} has no role in company #{company_id} (#{company.name})"
      puts "Available companies for this user:"
      user.companies.each do |c|
        role = user.company_user_roles.find_by(company: c)&.role&.name
        puts "   - Company #{c.id}: #{c.name} (#{role})"
      end
      exit 1
    end

    # Generate JWT token
    begin
      if defined?(JwtService)
        token = JwtService.encode(user_id: user.id, company_id: company.id)
      else
        payload = {
          user_id: user.id,
          company_id: company.id,
          iat: Time.current.to_i,
          exp: 24.hours.from_now.to_i
        }
        token = JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
      end

      puts "✅ JWT Token generated successfully!"
      puts ""
      puts "User: #{user.email} (ID: #{user.id})"
      puts "Company: #{company.name} (ID: #{company.id})"
      puts "Role: #{user_role.role.name}"
      puts ""
      puts "JWT Token:"
      puts token
      puts ""
      puts "💡 Use this token for API testing in Company #{company.id} context"
      puts "Make sure to create test data in the same company context:"
      puts ""
      puts "ActsAsTenant.with_tenant(Company.find(#{company.id})) do"
      puts "  # Create your test data here"
      puts "end"

    rescue => e
      puts "❌ Failed to generate JWT token: #{e.message}"
      exit 1
    end
  end

  desc "Validate specific JWT token against current tenant context"
  task :validate_jwt, [:token] => :environment do |task, args|
    token = args[:token]
    
    unless token
      puts "Usage: rake tenant_context:validate_jwt[your_jwt_token]"
      exit 1
    end

    puts "🔍 VALIDATING JWT CONTEXT"
    puts "=" * 40
    
    # Include the validator module
    include TenantContextValidator if defined?(TenantContextValidator)
    
    begin
      # Decode the token
      if defined?(JwtService)
        payload = JwtService.decode(token)
      else
        payload = JWT.decode(token, Rails.application.secret_key_base, true, algorithm: 'HS256')[0]
      end

      jwt_user_id = payload['user_id']
      jwt_company_id = payload['company_id']
      
      puts "JWT Token Contents:"
      puts "  User ID: #{jwt_user_id}"
      puts "  Company ID: #{jwt_company_id}"
      
      if jwt_company_id
        company = Company.find_by(id: jwt_company_id)
        puts "  Company Name: #{company&.name || 'NOT FOUND'}"
      end
      puts ""

      # Check current tenant context
      current_tenant = ActsAsTenant.current_tenant
      puts "Current Tenant Context:"
      puts "  ActsAsTenant.current_tenant: #{current_tenant&.id || 'NONE'}"
      puts "  Tenant Name: #{current_tenant&.name || 'NONE'}"
      puts ""

      # Validate alignment
      if jwt_company_id && current_tenant
        if jwt_company_id == current_tenant.id
          puts "✅ CONTEXT ALIGNED"
          puts "JWT company_id matches ActsAsTenant.current_tenant"
        else
          puts "❌ CONTEXT MISMATCH DETECTED"
          puts "This is the TYM-30 scenario that causes debugging hell!"
          puts ""
          puts "PROBLEM:"
          puts "  - JWT expects Company #{jwt_company_id}"
          puts "  - Current tenant is Company #{current_tenant.id}"
          puts ""
          puts "SOLUTION:"
          puts "  ActsAsTenant.current_tenant = Company.find(#{jwt_company_id})"
        end
      elsif jwt_company_id && !current_tenant
        puts "⚠️  JWT has company context but no tenant is set"
        puts "Set tenant: ActsAsTenant.current_tenant = Company.find(#{jwt_company_id})"
      elsif !jwt_company_id && current_tenant
        puts "⚠️  Tenant is set but JWT has no company context"
      else
        puts "⚠️  Neither JWT nor tenant context specify a company"
      end

    rescue JWT::DecodeError => e
      puts "❌ Invalid JWT token: #{e.message}"
    rescue => e
      puts "❌ Error validating token: #{e.message}"
    end
  end
end