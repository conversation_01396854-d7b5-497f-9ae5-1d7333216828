# frozen_string_literal: true

# ABOUTME: Comprehensive tenant context debugging utility that replaces the ad-hoc debug scripts
# ABOUTME: created during TYM-30 debugging session, providing structured context investigation tools.

class TenantContextDebugger
  class << self
    # Main debugging method that replicates the investigation done during TYM-30
    # This replaces: investigate_contract_mismatch.rb, check_api_company_context.rb, etc.
    #
    # @param user_email [String] Email of user to investigate
    # @param jwt_token [String, nil] Optional JWT token to validate against
    # @return [Hash] Complete context analysis
    def investigate_context_mismatch(user_email, jwt_token: nil)
      puts "🔍 INVESTIGATING TENANT CONTEXT (TYM-30 Style Analysis)"
      puts "=" * 60
      puts "User: #{user_email}"
      puts "JWT Token: #{jwt_token ? 'PROVIDED' : 'NOT PROVIDED'}"
      puts ""

      analysis = {
        user: nil,
        companies: [],
        contracts: [],
        events: [],
        jwt_context: nil,
        mismatches: [],
        recommendations: []
      }

      # Find the user
      user = User.find_by(email: user_email)
      unless user
        puts "❌ User not found: #{user_email}"
        return analysis
      end
      
      analysis[:user] = {
        id: user.id,
        email: user.email,
        companies_count: user.companies.count
      }
      puts "✅ User found: #{user.id} - #{user.email}"
      puts ""

      # Analyze JWT token if provided
      if jwt_token
        analysis[:jwt_context] = analyze_jwt_context(jwt_token)
        puts "JWT Context Analysis:"
        puts "  User ID: #{analysis[:jwt_context][:user_id]}"
        puts "  Company ID: #{analysis[:jwt_context][:company_id]}"
        puts "  Company Name: #{analysis[:jwt_context][:company_name]}"
        puts ""
      end

      # Investigate each company the user belongs to
      user.companies.includes(:contracts, :events).each do |company|
        puts "🏢 COMPANY ANALYSIS: #{company.id} - #{company.name}"
        puts "-" * 40
        
        company_analysis = analyze_company_context(user, company)
        analysis[:companies] << company_analysis
        
        # Check for context mismatches if JWT provided
        if jwt_token && analysis[:jwt_context][:company_id]
          jwt_company_id = analysis[:jwt_context][:company_id]
          if jwt_company_id != company.id
            puts "⚠️  POTENTIAL MISMATCH: JWT points to Company #{jwt_company_id}, analyzing Company #{company.id}"
          else
            puts "✅ CONTEXT ALIGNED: JWT and current analysis both use Company #{company.id}"
          end
        end
        puts ""
      end

      # Special analysis for JWT company if it's different
      if jwt_token && analysis[:jwt_context][:company_id]
        jwt_company_id = analysis[:jwt_context][:company_id]
        jwt_company = Company.find_by(id: jwt_company_id)
        
        if jwt_company && !user.companies.include?(jwt_company)
          puts "🚨 JWT COMPANY MISMATCH DETECTED!"
          puts "JWT points to Company #{jwt_company_id} (#{jwt_company.name})"
          puts "But user only belongs to: #{user.companies.pluck(:id, :name)}"
          analysis[:mismatches] << "JWT company mismatch"
        end
      end

      # Generate recommendations
      analysis[:recommendations] = generate_recommendations(analysis)
      
      puts "💡 RECOMMENDATIONS:"
      analysis[:recommendations].each_with_index do |rec, i|
        puts "#{i + 1}. #{rec}"
      end

      analysis
    end

    # Create test events in specific company context (replaces create_events_company3.rb)
    def create_test_events_for_context_validation(company_id, user_email)
      puts "📝 CREATING TEST EVENTS FOR CONTEXT VALIDATION"
      puts "=" * 50
      
      company = Company.find_by(id: company_id)
      unless company
        puts "❌ Company #{company_id} not found"
        return nil
      end

      user = User.find_by(email: user_email)
      unless user
        puts "❌ User #{user_email} not found"
        return nil
      end

      results = {}
      
      ActsAsTenant.with_tenant(company) do
        puts "🏢 Creating events in Company #{company.id} (#{company.name}) context"
        
        # Find or create contract
        contract = company.contracts.find_by(user_id: user.id)
        unless contract
          puts "No contract found for user, creating one..."
          contract = FactoryBot.create(:contract, user: user, company: company, skip_invitation: true)
        end
        puts "✅ Using contract: #{contract.id}"

        # Clean up any existing test events
        Event.where(title: ['Test Illness Event', 'Test Vacation Event']).destroy_all
        puts "🧹 Cleaned up previous test events"

        # Create test events
        illness_event = Event.create!(
          contract: contract,
          event_type: :illness,
          start_time: Time.current,
          end_time: Time.current + 8.hours,
          title: "Test Illness Event"
        )
        
        vacation_event = Event.create!(
          contract: contract,
          event_type: :vacation,
          start_time: Time.current + 1.day,
          end_time: Time.current + 2.days,
          title: "Test Vacation Event"
        )

        results = {
          company: company,
          contract: contract,
          illness_event: illness_event,
          vacation_event: vacation_event,
          jwt_token: generate_matching_jwt_token(user, company)
        }

        puts "✅ Created events:"
        puts "  - Illness: #{illness_event.id} (#{illness_event.event_type})"
        puts "  - Vacation: #{vacation_event.id} (#{vacation_event.event_type})"
        puts ""
        puts "🎯 Generated JWT token for testing:"
        puts results[:jwt_token]
        puts ""
        puts "💡 Use this token for API testing - it's aligned with the test data context!"
      end

      results
    end

    # Test conflict detection with proper context (replaces proper_conflict_test.rb)
    def test_conflict_detection_with_context(contract_id, start_date, end_date)
      puts "🔍 TESTING CONFLICT DETECTION WITH CONTEXT VALIDATION"
      puts "=" * 55
      
      contract = Contract.find_by(id: contract_id)
      unless contract
        puts "❌ Contract #{contract_id} not found"
        return nil
      end

      company = contract.company
      puts "Testing in Company #{company.id} (#{company.name}) context"
      puts "Contract: #{contract.id} - #{contract.first_name} #{contract.last_name}"
      puts "Date range: #{start_date} to #{end_date}"
      puts ""

      # Ensure we're in the correct tenant context
      ActsAsTenant.with_tenant(company) do
        puts "✅ ActsAsTenant context set to Company #{company.id}"
        
        # Run the same conflict detection logic that the API uses
        events = Event.joins(:contract)
          .where(contract_id: [contract_id])
          .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
          .where.not(status: 'rejected')
          .includes(:contract)

        puts "🔍 Conflict detection results:"
        puts "  Found #{events.count} events in conflict range"
        
        events.each do |event|
          puts "  - #{event.title} (#{event.event_type}) - Status: #{event.status} - Contract: #{event.contract_id}"
        end

        if events.any?
          puts "✅ Conflict detection working correctly"
        else
          puts "⚠️  No events found - check if test data exists in this company context"
        end

        events
      end
    end

    private

    def analyze_jwt_context(token)
      begin
        if defined?(JwtService)
          payload = JwtService.decode(token)
        else
          payload = JWT.decode(token, Rails.application.secret_key_base, true, algorithm: 'HS256')[0]
        end

        company_id = payload['company_id']
        company = Company.find_by(id: company_id) if company_id

        {
          user_id: payload['user_id'],
          company_id: company_id,
          company_name: company&.name,
          expired: payload['exp'] && Time.at(payload['exp']) < Time.current,
          payload: payload
        }
      rescue => e
        {
          error: e.message,
          user_id: nil,
          company_id: nil,
          company_name: nil,
          expired: nil
        }
      end
    end

    def analyze_company_context(user, company)
      analysis = {
        company_id: company.id,
        company_name: company.name,
        user_role: nil,
        contracts: [],
        events: [],
        context_data: {}
      }

      # Get user role in company
      user_role = user.company_user_roles.find_by(company: company)
      analysis[:user_role] = user_role&.role&.name

      # Analyze in company context
      ActsAsTenant.with_tenant(company) do
        puts "  ActsAsTenant context: #{ActsAsTenant.current_tenant.id}"
        
        # Find contracts
        contracts = company.contracts.where(user_id: user.id)
        puts "  Contracts: #{contracts.count}"
        contracts.each do |contract|
          contract_data = {
            id: contract.id,
            name: "#{contract.first_name} #{contract.last_name}",
            email: contract.email,
            created_at: contract.created_at
          }
          analysis[:contracts] << contract_data
          puts "    - Contract #{contract.id}: #{contract.first_name} #{contract.last_name}"
        end

        # Find events for these contracts
        if contracts.any?
          events = Event.where(contract_id: contracts.pluck(:id)).limit(10)
          puts "  Events: #{events.count}"
          events.each do |event|
            event_data = {
              id: event.id,
              title: event.title,
              event_type: event.event_type,
              status: event.status,
              contract_id: event.contract_id
            }
            analysis[:events] << event_data
            puts "    - Event #{event.id}: #{event.title} (#{event.event_type}) - Contract: #{event.contract_id}"
          end
        end

        analysis[:context_data] = {
          acts_as_tenant_id: ActsAsTenant.current_tenant.id,
          total_contracts: contracts.count,
          total_events: analysis[:events].count
        }
      end

      analysis
    end

    def generate_matching_jwt_token(user, company)
      if defined?(JwtService)
        JwtService.encode(user_id: user.id, company_id: company.id)
      else
        payload = {
          user_id: user.id,
          company_id: company.id,
          iat: Time.current.to_i,
          exp: 24.hours.from_now.to_i
        }
        JWT.encode(payload, Rails.application.secret_key_base, 'HS256')
      end
    end

    def generate_recommendations(analysis)
      recommendations = []
      
      if analysis[:mismatches].any?
        recommendations << "Context mismatches detected - align JWT company_id with test data company context"
      end

      if analysis[:jwt_context] && analysis[:jwt_context][:company_id]
        jwt_company_id = analysis[:jwt_context][:company_id]
        recommendations << "Use ActsAsTenant.with_tenant(Company.find(#{jwt_company_id})) when creating test data"
        recommendations << "Or generate new JWT for your test data's company context"
      end

      if analysis[:companies].empty?
        recommendations << "User has no company associations - create company_user_role records"
      end

      recommendations << "Use TenantContextValidator.debug_current_context(token) before making API calls"
      recommendations << "Use create_in_tenant_context(:factory_name, company: target_company) for aligned test data"

      recommendations
    end
  end
end